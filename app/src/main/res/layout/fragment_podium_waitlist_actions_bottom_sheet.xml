<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable name="viewModel" type="com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel" />
        <variable name="speaker" type="com.app.messej.data.model.api.podium.PodiumSpeaker" />
        <variable name="isSelf" type="Boolean" />
    </data>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingBottom="80dp"
        android:animateLayoutChanges="true"
        android:background="@color/colorSurface">

        <View
            android:id="@+id/handle"
            android:layout_width="60dp"
            android:layout_height="8dp"
            android:background="@drawable/ic_bottom_sheet_handle"
            android:layout_marginTop="@dimen/activity_margin"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <include
            android:id="@+id/user_info"
            layout="@layout/layout_podium_bottom_sheet_user_info"
            app:userTribeName="@{speaker.tribeName}"
            app:podiumName="@{viewModel.podium.name}"
            app:hideSendGift="@{viewModel.canSendAndReceiveGifts(speaker) &amp;&amp; !viewModel.podiumKind.birthDayPodium}"
            app:speaker="@{speaker}"/>

        <View
            android:id="@+id/line"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@color/colorDividerLight"
            android:layout_marginTop="@dimen/activity_margin"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/user_info" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_allow_to_speak"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIf="@{!viewModel.iAmElevated || isSelf}">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_allow_to_speak"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:srcCompat="@drawable/ic_podium_unmute" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_allow_to_speak"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:textColor="@{viewModel.hasSpaceForNewSpeaker?@color/textColorPrimary:@color/textColorHint}"
                    tools:textColor="@color/textColorHint"
                    android:text="@string/podium_action_allow_to_speak"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/loader1"
                    app:layout_constraintStart_toEndOf="@id/image_allow_to_speak"
                    app:layout_constraintTop_toTopOf="parent"/>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/loader1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:indeterminate="true"
                    app:goneIfNot="@{viewModel.allowToSpeakLoading}"
                    app:indicatorSize="20dp"
                    app:trackThickness="2dp"
                    android:visibility="gone"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_decline"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIf="@{!viewModel.iAmElevated || isSelf}">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_decline"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:srcCompat="@drawable/ic_decline" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_decline"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:layout_marginEnd="@dimen/activity_margin"
                    android:text="@string/common_decline"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/loader7"
                    app:layout_constraintStart_toEndOf="@id/image_decline"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/loader7"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:indeterminate="true"
                    app:goneIfNot="@{viewModel.declineRequestToSpeakLoading}"
                    app:indicatorSize="20dp"
                    app:trackThickness="2dp"
                    android:visibility="gone"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_appoint_admin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIfNot="@{viewModel.canAppointAsAdmin(speaker.id,speaker.premiumUser)}">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_appoint_admin"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:srcCompat="@drawable/ic_podium_speaker_appoint_as_admin" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_appoint_admin"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:text="@string/podium_live_action_make_admin"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/loader2"
                    app:layout_constraintStart_toEndOf="@id/image_appoint_admin"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/loader2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:indeterminate="true"
                    app:indicatorSize="20dp"
                    app:trackThickness="2dp"
                    app:goneIfNot="@{viewModel.appointAsAdminLoading}"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
<!--            <androidx.constraintlayout.widget.ConstraintLayout-->
<!--                android:id="@+id/action_send_gift"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:clickable="true"-->
<!--                android:foreground="?attr/selectableItemBackground"-->
<!--                app:goneIfNot="@{viewModel.canSendAndReceiveGifts(speaker)}">-->

<!--                <androidx.appcompat.widget.AppCompatImageView-->
<!--                    android:id="@+id/appCompatImageView15"-->
<!--                    android:layout_width="24dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginVertical="@dimen/activity_margin"-->
<!--                    android:layout_marginStart="@dimen/extra_margin"-->
<!--                    android:adjustViewBounds="true"-->
<!--                    app:layout_constraintBottom_toBottomOf="parent"-->
<!--                    app:layout_constraintStart_toStartOf="parent"-->
<!--                    app:layout_constraintTop_toTopOf="parent"-->
<!--                    app:srcCompat="@drawable/ic_podium_speaker_gift_circle" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/title_send_gift"-->
<!--                    android:layout_width="0dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/element_spacing"-->
<!--                    android:text="@string/title_send_gift"-->
<!--                    android:textAppearance="@style/TextAppearance.Flashat.Body1"-->
<!--                    app:layout_constrainedWidth="true"-->
<!--                    app:layout_constraintBottom_toBottomOf="parent"-->
<!--                    app:layout_constraintEnd_toStartOf="@+id/goto_send_gift"-->
<!--                    app:layout_constraintStart_toEndOf="@id/appCompatImageView15"-->
<!--                    app:layout_constraintTop_toTopOf="parent" />-->

<!--                <androidx.appcompat.widget.AppCompatImageView-->
<!--                    android:id="@+id/goto_send_gift"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginEnd="@dimen/activity_margin"-->
<!--                    app:layout_constraintBottom_toBottomOf="parent"-->
<!--                    app:layout_constraintEnd_toEndOf="parent"-->
<!--                    app:layout_constraintTop_toTopOf="parent"-->
<!--                    app:srcCompat="@drawable/ic_caret_right" />-->

<!--                <View-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="1px"-->
<!--                    android:background="@color/colorDividerLight"-->
<!--                    app:layout_constraintBottom_toBottomOf="parent"-->
<!--                    app:layout_constraintEnd_toEndOf="parent"-->
<!--                    app:layout_constraintStart_toStartOf="parent" />-->

<!--            </androidx.constraintlayout.widget.ConstraintLayout>-->

<!--            <androidx.constraintlayout.widget.ConstraintLayout-->
<!--                android:id="@+id/action_send_flax"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:clickable="true"-->
<!--                android:foreground="?attr/selectableItemBackground"-->
<!--                app:goneIfNot="@{viewModel.canSendFlaxTo(speaker)}">-->

<!--                <androidx.appcompat.widget.AppCompatImageView-->
<!--                    android:id="@+id/image_send_flax"-->
<!--                    android:layout_width="24dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginVertical="@dimen/activity_margin"-->
<!--                    android:layout_marginStart="@dimen/extra_margin"-->
<!--                    android:adjustViewBounds="true"-->
<!--                    app:layout_constraintBottom_toBottomOf="parent"-->
<!--                    app:layout_constraintStart_toStartOf="parent"-->
<!--                    app:layout_constraintTop_toTopOf="parent"-->
<!--                    app:srcCompat="@drawable/ic_flax_coin" />-->


<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/text_send_flax"-->
<!--                    android:layout_width="0dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="@dimen/element_spacing"-->
<!--                    android:text="@string/send_flax"-->
<!--                    android:textAppearance="@style/TextAppearance.Flashat.Body1"-->
<!--                    app:layout_constraintBottom_toBottomOf="parent"-->
<!--                    app:layout_constraintEnd_toStartOf="@id/goto_send_flax"-->
<!--                    app:layout_constraintStart_toEndOf="@id/image_send_flax"-->
<!--                    app:layout_constraintTop_toTopOf="parent" />-->

<!--                <androidx.appcompat.widget.AppCompatImageView-->
<!--                    android:id="@+id/goto_send_flax"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginEnd="@dimen/activity_margin"-->
<!--                    app:layout_constraintBottom_toBottomOf="parent"-->
<!--                    app:layout_constraintEnd_toEndOf="parent"-->
<!--                    app:layout_constraintTop_toTopOf="parent"-->
<!--                    app:srcCompat="@drawable/ic_caret_right" />-->

<!--                <View-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="1px"-->
<!--                    android:background="@color/colorDividerLight"-->
<!--                    app:layout_constraintBottom_toBottomOf="parent"-->
<!--                    app:layout_constraintEnd_toEndOf="parent"-->
<!--                    app:layout_constraintStart_toStartOf="parent" />-->

<!--            </androidx.constraintlayout.widget.ConstraintLayout>-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_pause_gift"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIfNot="@{viewModel.shouldShowPauseGift(speaker.id, speaker.citizenship.isVisitor,speaker.citizenship.isGolden)}">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_pause_gift"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_pause_gift" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/title_pause_gift"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:text="@{viewModel.isUserGiftPaused(speaker.id)?@string/podium_resume_gift:@string/podium_pause_gift}"
                    tools:text="@string/podium_pause_gift"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/goto_pause_gift"
                    app:layout_constraintStart_toEndOf="@id/image_pause_gift"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/goto_pause_gift"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_caret_right" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_dismiss_admin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIfNot="@{viewModel.canDismissAdmin(speaker.id)}">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_dismiss_admin"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:srcCompat="@drawable/ic_podium_speaker_dismiss_as_admin" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_dismiss_admin"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:text="@string/podium_action_dismiss_admin"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/image_dismiss_admin"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/loader3" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/loader3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:indeterminate="true"
                    app:indicatorSize="20dp"
                    app:trackThickness="2dp"
                    app:goneIfNot="@{viewModel.dismissAsAdminLoading}"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_cancel_admin_invite"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:goneIfNot="@{viewModel.canCancelAdminInvite(speaker.id)}">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_cancel_admin_invite"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:srcCompat="@drawable/ic_podium_speaker_cancel_admin_invite" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_cancel_admin_invite"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/huddle_cancel_admin_invite"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/loader4"
                    app:layout_constraintStart_toEndOf="@id/image_cancel_admin_invite"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/loader4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:indeterminate="true"
                    app:indicatorSize="20dp"
                    app:trackThickness="2dp"
                    app:goneIfNot="@{viewModel.cancelAdminInviteLoading}"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIf="@{isSelf}">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_info"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:srcCompat="@drawable/ic_podium_speaker_info" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_info"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:text="@string/podium_action_id_card"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/image_info"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/goto_info" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/goto_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:srcCompat="@drawable/ic_caret_right" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_block"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIfNot="@{viewModel.canBlock(speaker.id)}">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_block"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:srcCompat="@drawable/ic_podium_speaker_block" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_block"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/user_action_block"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    android:textColor="@color/colorError"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/image_block"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/loader5" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/loader5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:indeterminate="true"
                    app:goneIfNot="@{viewModel.userBlockLoading}"
                    app:indicatorSize="20dp"
                    app:trackThickness="2dp"
                    android:visibility="gone"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_report_user"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIfNot="@{viewModel.canReport(speaker)}">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_report_user"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:background="@drawable/ic_dot"
                    android:backgroundTint="@color/colorPrimary"
                    android:padding="2dp"
                    app:tint="@color/textColorOnPrimary"
                    app:srcCompat="@drawable/ic_report_outline_cut" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_report_user"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/report_user"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    android:textColor="@color/colorError"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/image_report_user"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_ban_user"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIfNot="@{viewModel.canBan(speaker)}">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_ban_user"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:background="@drawable/ic_dot"
                    android:backgroundTint="@color/colorPrimary"
                    android:padding="2dp"
                    app:tint="@color/textColorOnPrimary"
                    app:srcCompat="@drawable/ic_ban_user_round" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_ban_user"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/report_user_ban"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    android:textColor="@color/colorError"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/image_ban_user"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_cancel_request"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIfNot="@{isSelf}">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_cancel_request"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:srcCompat="@drawable/ic_cancel_request" />


                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_cancel_request"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/podium_action_cancel_speak_request"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    android:textColor="@color/colorError"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/image_cancel_request"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/loader6" />


                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/loader6"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:indeterminate="true"
                    app:indicatorSize="20dp"
                    app:trackThickness="2dp"
                    android:visibility="gone"
                    app:goneIfNot="@{viewModel.cancelSpeakRequestLoading}"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>
</layout>

