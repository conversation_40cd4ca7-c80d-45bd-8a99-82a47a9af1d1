<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="topGifter"
            type="com.app.messej.data.model.entity.Podium.TopGifters" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="64dp"
        android:layout_height="wrap_content">

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/user_dp"
            android:layout_width="50dp"
            android:layout_height="0dp"
            android:layout_margin="@dimen/element_spacing"
            android:scaleType="centerCrop"
            app:imageUrl="@{topGifter.thumbnail}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:placeholder="@{@drawable/im_user_placeholder_square}"
            app:riv_oval="true"
            tools:src="@drawable/im_user_placeholder_square" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/superstar_premium_badge"
            android:layout_width="@dimen/premium_badge_size"
            android:layout_height="@dimen/premium_badge_size"
            android:elevation="4dp"
            app:layout_constraintStart_toStartOf="@id/user_dp"
            app:layout_constraintTop_toTopOf="@id/user_dp"
            app:userBadgeOnPrimary="@{topGifter.userBadge}"
            tools:src="@drawable/ic_user_badge_premium" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@+id/user_dp"
            android:background="@drawable/ic_dot"
            android:backgroundTint="@color/colorDarkOverlay"
            app:goneIfNull="@{topGifter.score}"
            android:padding="4dp"
            app:layout_constraintEnd_toEndOf="@+id/user_dp">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Flashat.Label.Smaller.Bold"
                android:textColor="@color/textColorOnPrimary"
                android:text="@{topGifter.updatedScore}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="20" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>