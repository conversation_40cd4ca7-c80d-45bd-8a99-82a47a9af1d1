<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.app.messej.data.model.enums.TheaterCharge" />

        <variable
            name="viewModel"
            type="com.app.messej.ui.home.publictab.podiums.manage.PodiumTheaterChargesViewModel" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:textAppearance="@style/TextAppearance.Flashat.Subtitle1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginTop="@dimen/activity_margin"
                android:text="@string/podium_speaker_charges_title"
                goneIf="@{viewModel.podiumKind.isBirthDayPodium}"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_height="wrap_content"/>

            <LinearLayout
                android:id="@+id/linear_layout"
                android:layout_width="match_parent"
                app:layout_constraintTop_toBottomOf="@id/title"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/activity_margin"
                android:gravity="top"
                android:layout_marginTop="@dimen/activity_margin"
                android:layout_height="wrap_content">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSpeakerCharges"
                    android:singleLine="false"
                    android:layout_width="0dp"
                    android:lines="2"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:backgroundTint="@{viewModel.chargeType == TheaterCharge.SPEAKER ? @color/colorPrimary : @color/colorSurfaceSecondaryDarker}"
                    android:onClick="@{() -> viewModel.setChargeType(TheaterCharge.SPEAKER)}"
                    android:text="@string/podium_theater_speaker_fees"
                    android:textAllCaps="false"
                    android:textColor="@{viewModel.chargeType == TheaterCharge.SPEAKER ? @color/textColorOnPrimary : @color/textColorPrimary}"
                    tools:enabled="true"/>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnJoiningFees"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:paddingHorizontal="@dimen/activity_margin"
                    android:singleLine="false"
                    android:lines="2"
                    goneIf="@{viewModel.podiumKind.isBirthDayPodium}"
                    android:layout_marginStart="@dimen/activity_margin"
                    android:layout_height="wrap_content"
                    android:backgroundTint="@{viewModel.chargeType == TheaterCharge.JOINING_FEE ? @color/colorPrimary : @color/colorSurfaceSecondaryDarker}"
                    android:onClick="@{() -> viewModel.setChargeType(TheaterCharge.JOINING_FEE)}"
                    android:text="@string/podium_theater_joining_fees"
                    android:textAllCaps="false"
                    android:textColor="@{viewModel.chargeType == TheaterCharge.JOINING_FEE ? @color/textColorOnPrimary : @color/textColorPrimary}"
                    tools:enabled="true" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnBirthdayGift"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:paddingHorizontal="@dimen/activity_margin"
                    android:singleLine="false"
                    android:lines="2"
                    goneIfNot="@{viewModel.podiumKind.isBirthDayPodium}"
                    android:layout_marginStart="@dimen/activity_margin"
                    android:layout_height="wrap_content"
                    android:backgroundTint="@{viewModel.chargeType == TheaterCharge.USER_GIFTS_SENT ? @color/colorPrimary : @color/colorSurfaceSecondaryDarker}"
                    android:onClick="@{() -> viewModel.setChargeType(TheaterCharge.USER_GIFTS_SENT)}"
                    android:text="@string/podium_birthday_gift_fees"
                    android:textAllCaps="false"
                    android:textColor="@{viewModel.chargeType == TheaterCharge.USER_GIFTS_SENT ? @color/textColorOnPrimary : @color/textColorPrimary}"
                    tools:enabled="true" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnLikesCharges"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/activity_margin"
                    android:singleLine="false"
                    android:lines="2"
                    android:layout_marginHorizontal="@dimen/activity_margin"
                    goneIf="@{viewModel.podiumKind.isBirthDayPodium}"
                    android:layout_weight="1"
                    android:backgroundTint="@{viewModel.chargeType == TheaterCharge.LIKE ? @color/colorPrimary : @color/colorSurfaceSecondaryDarker}"
                    android:onClick="@{() -> viewModel.setChargeType(TheaterCharge.LIKE)}"
                    android:text="@string/podium_theater_likes_charges"
                    android:textAllCaps="false"
                    android:textColor="@{viewModel.chargeType == TheaterCharge.LIKE ? @color/textColorOnPrimary : @color/textColorPrimary}"
                    tools:enabled="true" />

            </LinearLayout>

            <com.kennyc.view.MultiStateView
                android:id="@+id/multiStateView"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/linear_layout"
                app:msv_emptyView="@layout/layout_list_state_empty"
                app:msv_errorView="@layout/layout_list_state_error"
                app:msv_loadingView="@layout/layout_eds_state_loading_chat_list"
                app:msv_viewState="content">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvCharges"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/activity_margin"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:listitem="@layout/item_theater_charges" />
            </com.kennyc.view.MultiStateView>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>