<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable name="viewModel" type="com.app.messej.ui.home.gift.bottomSheet.BirthdayBottomSheetViewModel" />
        <variable name="birthdaySize" type="String"/>
        <variable name="currentBirthday" type="Boolean"/>
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/bg_birthday_podium_bottom_sheet">
        </androidx.appcompat.widget.AppCompatImageView>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_close"
                style="@style/Widget.Flashat.Button.TextButton.IconOnly"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/element_spacing"
                android:layout_marginEnd="@dimen/activity_margin"
                app:icon="@drawable/ic_close"
                app:iconTint="@color/white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/bg_inner"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginHorizontal="@dimen/double_margin"
            android:layout_marginTop="@dimen/double_margin"
            android:background="@drawable/bg_birthday_profile"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btn_close">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_create"
                style="@style/TextAppearance.Flashat.Headline5"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:layout_marginTop="@dimen/activity_margin"
                android:text="@string/podium_birthday_bottom_sheet_title"
                android:textColor="@color/colorAlwaysLightSecondary"
                app:layout_constraintBottom_toTopOf="@id/text_birthday"
                app:layout_constraintEnd_toEndOf="parent"
                app:textAllCaps="true"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_birthday"
                style="@style/TextAppearance.Flashat.Headline4"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/activity_margin"
                android:gravity="center_horizontal"
                android:text="@string/podium_birthday_full_text"
                android:textColor="@color/colorAlwaysLightSecondary"
                app:layout_constraintBottom_toTopOf="@id/text_description"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/text_create"
                app:textAllCaps="true" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_description"
                style="@style/TextAppearance.Flashat.Headline5"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/activity_margin"
                android:gravity="center_horizontal"
                android:text="@string/podium_birthday_bottom_sheet_description"
                android:textColor="@color/colorAlwaysLightSecondary"
                app:layout_constraintBottom_toTopOf="@id/button_send_birthday_gift"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/text_birthday" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/button_send_birthday_gift"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/double_margin"
                android:background="@drawable/bg_send_gift_birthday"
                app:layout_constraintBottom_toBottomOf="@id/bg_inner"
                android:layout_marginTop="@dimen/extra_margin"
                app:layout_constraintTop_toBottomOf="@id/text_description"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/TextAppearance.Flashat.Subtitle1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:padding="@dimen/element_spacing"
                    android:text="@string/podium_create_birthday_podium"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>