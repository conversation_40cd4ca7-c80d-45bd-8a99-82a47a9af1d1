<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.app.messej.ui.profile.PublicUserProfileViewModel" />

        <import type="com.app.messej.data.model.enums.EditableFieldMode" />

        <import type="com.app.messej.ui.profile.PublicUserProfileViewModel.PrivateChatStatus" />

        <variable
            name="userType"
            type="String" />

        <variable
            name="idCardDrawBackground"
            type="android.graphics.drawable.Drawable" />

        <variable
            name="idCardUserStrengthDrawBackground"
            type="android.graphics.drawable.Drawable" />

        <variable
            name="textIdCardDrawBackground"
            type="android.graphics.drawable.Drawable" />

        <variable
            name="textAboutDrawBackground"
            type="android.graphics.drawable.Drawable" />

        <variable
            name="tribeDrawBackground"
            type="android.graphics.drawable.Drawable" />

        <variable
            name="idColor"
            type="Integer" />

        <variable
            name="subTitleColor"
            type="Integer" />

        <variable
            name="usertypeColor"
            type="Integer" />

        <variable
            name="labelTextColor"
            type="Integer" />

        <variable
            name="iconTintColor"
            type="Integer" />

        <variable
            name="textColor"
            type="Integer" />

        <variable
            name="userIconTint"
            type="Integer" />

        <variable
            name="isResident"
            type="boolean" />

        <variable
            name="userIconTextTint"
            type="Integer" />

        <variable
            name="haveTribe"
            type="boolean" />

        <variable
            name="isPresident"
            type="boolean" />

        <variable
            name="rankTintColor"
            type="Integer" />

        <variable
            name="isVisitor"
            type="boolean" />

        <variable
            name="actionItemColor"
            type="Integer" />

    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            android:id="@+id/custom_action_bar"
            layout="@layout/item_custom_action_bar_rating" />

        <com.kennyc.view.MultiStateView
            android:id="@+id/multiStateView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior"
            app:msv_emptyView="@layout/layout_list_state_empty"
            app:msv_errorView="@layout/layout_list_state_error"
            app:msv_loadingView="@layout/layout_eds_id_card_state_loading"
            app:msv_viewState="content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_behavior="@string/appbar_scrolling_view_behavior">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/cardView_header"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/element_spacing"
                        android:layout_marginTop="@dimen/activity_margin"
                        app:cardCornerRadius="@dimen/element_spacing"
                        app:cardElevation="@dimen/cardview_default_elevation"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">


                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/layout_header"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@{idCardDrawBackground}"
                            tools:background="@drawable/bg_idcard_citizen">

                            <androidx.constraintlayout.widget.Guideline
                                android:id="@+id/guideLine_main"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                app:layout_constraintGuide_percent=".56" />

                            <androidx.constraintlayout.widget.Guideline
                                android:id="@+id/guideLine_top"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                app:layout_constraintGuide_percent=".24" />

                            <androidx.constraintlayout.widget.Guideline
                                android:id="@+id/guideLine_bottom"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                app:layout_constraintGuide_percent=".75" />

                            <androidx.constraintlayout.widget.Guideline
                                android:id="@+id/guideLine_user"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                app:layout_constraintGuide_percent=".26" />

                            <androidx.constraintlayout.widget.Guideline
                                android:id="@+id/guideLine_End"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                app:layout_constraintGuide_percent=".89" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/text_idCard"
                                style="@style/TextAppearance.Flashat.Subtitle1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/activity_margin"
                                android:textAllCaps= "@{isPresident ? true : false}"
                                android:background="@{isResident==true?@drawable/bg_idcard_resident_white : textIdCardDrawBackground}"
                                android:paddingHorizontal="@dimen/element_spacing"
                                android:paddingVertical="@dimen/line_spacing"
                                android:text="@{isPresident ? @string/title_id_card : @string/id_card_header_identity_card}"
                                android:textColor="@{isResident==true?@color/textColorOnSecondary: idColor}"
                                app:layout_constraintStart_toStartOf="parent"

                                app:layout_constraintTop_toTopOf="parent"
                                tools:text="@string/id_card_header_identity_card"
                                tools:background="@drawable/bg_label_idcard_citizen_ambassador"
                                tools:textColor="@color/white" />


                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/text_citizenship"
                                style="@style/TextAppearance.Flashat.Headline5"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:text="@{userType}"
                                android:ellipsize="end"
                                android:maxLines="1"
                                android:textAlignment="textEnd"
                                android:textAllCaps= "@{isPresident ? true : false}"
                                android:textColor="@{usertypeColor}"
                                app:layout_constraintBottom_toBottomOf="@id/text_idCard"
                                app:layout_constraintEnd_toStartOf="@id/layout_popup"
                                app:layout_constraintStart_toEndOf="@id/text_idCard"
                                android:layout_marginHorizontal="@dimen/line_spacing"
                                tools:text="Citizen"
                                tools:textColor="@color/colorPrimary" />

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:id="@+id/layout_popup"
                                goneIf="@{viewModel.isCurrentUser}"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                app:layout_constraintBottom_toBottomOf="@id/text_citizenship"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="@id/text_citizenship">

                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/btn_id_card_popup"
                                    style="@style/Widget.Flashat.Button.TextButton.IconOnly"
                                    goneIf="@{viewModel.profile.isSuperstar==true &amp;&amp; viewModel.chatStatus== PrivateChatStatus.RESTRICTED}"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    app:icon="@drawable/ic_more_vertical"
                                    app:iconTint="@{iconTintColor}"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintTop_toTopOf="parent" />
                            </androidx.constraintlayout.widget.ConstraintLayout>


                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_id_card_name_title"
                                style="@style/TextAppearance.Flashat.Label.Bold"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/activity_margin"
                                android:layout_marginTop="@dimen/element_spacing"
                                tools:layout_marginStart="@dimen/activity_margin"
                                android:layout_marginEnd="@dimen/line_spacing"
                                android:text="@string/title_nickname_name"
                                android:textColor="@{labelTextColor}"
                                app:layout_constraintEnd_toStartOf="@id/text_name_barrier"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/guideLine_top"
                                tools:textColor="@color/textColorPrimary" />


                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/text_name_barrier"
                                style="@style/TextAppearance.Flashat.Label"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text=":"
                                android:textColor="@{textColor}"
                                app:layout_constraintBottom_toBottomOf="@id/txt_id_card_name_title"
                                app:layout_constraintEnd_toEndOf="@id/guideLine_user"
                                app:layout_constraintTop_toTopOf="@id/txt_id_card_name_title"
                                tools:textColor="@color/textColorPrimary" />


                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_id_card_name"
                                style="@style/TextAppearance.Flashat.Label.Bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/element_spacing"
                                android:ellipsize="end"
                                android:maxLines="1"
                                android:text="@{viewModel.profile.name}"
                                android:textColor="@{textColor}"
                                app:flow_horizontalAlign="start"
                                app:layout_constrainedWidth="true"
                                app:layout_constraintBottom_toBottomOf="@+id/txt_id_card_name_title"
                                app:layout_constraintEnd_toEndOf="@id/guideLine_main"
                                app:layout_constraintHorizontal_bias="0.0"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintStart_toEndOf="@id/guideLine_user"
                                app:layout_constraintTop_toTopOf="@+id/txt_id_card_name_title"
                                tools:text="Sholin"
                                tools:textColor="@color/colorPrimaryDark" />


                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_id_card_user_title"
                                style="@style/TextAppearance.Flashat.Label.Bold"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/line_spacing"
                                android:layout_marginEnd="@dimen/line_spacing"
                                android:text="@string/id_card_title_id_code"
                                android:textColor="@{labelTextColor}"
                                app:layout_constraintEnd_toStartOf="@id/text_idcode_barrier"
                                app:layout_constraintStart_toStartOf="@id/txt_id_card_name_title"
                                app:layout_constraintTop_toBottomOf="@+id/txt_id_card_name"
                                tools:textColor="@color/textColorPrimary" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/text_idcode_barrier"
                                style="@style/TextAppearance.Flashat.Label"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text=":"
                                android:textColor="@{textColor}"
                                app:layout_constraintBottom_toBottomOf="@id/txt_id_card_user_title"
                                app:layout_constraintEnd_toEndOf="@id/guideLine_user"
                                app:layout_constraintTop_toTopOf="@id/txt_id_card_user_title"
                                tools:textColor="@color/textColorPrimary" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_id_card_user"
                                style="@style/TextAppearance.Flashat.Label.Bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/element_spacing"
                                android:ellipsize="end"
                                android:maxLines="1"
                                android:text="@{viewModel.profile.username}"
                                android:textColor="@{textColor}"
                                app:flow_horizontalAlign="start"
                                app:layout_constrainedWidth="true"
                                app:layout_constraintBottom_toBottomOf="@+id/txt_id_card_user_title"
                                app:layout_constraintEnd_toEndOf="@id/guideLine_main"
                                app:layout_constraintHorizontal_bias="0.0"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintStart_toEndOf="@id/guideLine_user"
                                app:layout_constraintTop_toTopOf="@+id/txt_id_card_user_title"
                                tools:text="Sholin88"
                                tools:textColor="@color/colorPrimaryDark" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_id_card_nickname_title"
                                style="@style/TextAppearance.Flashat.Label.Bold"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/line_spacing"
                                android:layout_marginEnd="@dimen/line_spacing"
                                android:text="@string/title_id_card_nick_name"
                                android:textColor="@{labelTextColor}"
                                app:goneIfNot="@{viewModel.isCurrentUser()==false}"
                                app:layout_constraintEnd_toStartOf="@id/text_nickName_barrier"
                                app:layout_constraintStart_toStartOf="@id/txt_id_card_name_title"
                                app:layout_constraintTop_toBottomOf="@+id/txt_id_card_user_title"
                                tools:textColor="@color/textColorPrimary" />


                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/text_nickName_barrier"
                                style="@style/TextAppearance.Flashat.Label"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text=":"
                                android:textColor="@{textColor}"
                                app:goneIfNot="@{viewModel.isCurrentUser()==false}"
                                app:layout_constraintBottom_toBottomOf="@id/txt_id_card_nickname_title"
                                app:layout_constraintEnd_toEndOf="@id/guideLine_user"
                                app:layout_constraintTop_toTopOf="@id/txt_id_card_nickname_title"
                                tools:textColor="@color/textColorPrimary" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_id_card_nickName"
                                style="@style/TextAppearance.Flashat.Label.Bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/element_spacing"
                                android:ellipsize="end"
                                android:maxLines="1"
                                android:text="@{viewModel.userNickName.nickName}"
                                android:textColor="@{textColor}"
                                app:flow_horizontalAlign="start"
                                app:goneIfNot="@{viewModel.isCurrentUser()==false}"
                                app:layout_constrainedWidth="true"
                                app:layout_constraintBottom_toBottomOf="@+id/txt_id_card_nickname_title"
                                app:layout_constraintEnd_toEndOf="@id/guideLine_main"
                                app:layout_constraintHorizontal_bias="0.0"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintStart_toEndOf="@id/guideLine_user"
                                app:layout_constraintTop_toTopOf="@+id/txt_id_card_nickname_title"
                                tools:text="hey da"
                                tools:textColor="@color/colorPrimaryDark" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_id_card_dage_title"
                                style="@style/TextAppearance.Flashat.Label.Bold"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/line_spacing"
                                android:layout_marginEnd="@dimen/line_spacing"
                                android:text="@string/id_card_title_flashat_age"
                                android:textColor="@{labelTextColor}"
                                app:layout_constraintEnd_toStartOf="@id/text_flashatAge_barrier"
                                app:layout_constraintStart_toStartOf="@id/txt_id_card_name_title"
                                app:layout_constraintTop_toBottomOf="@+id/txt_id_card_nickname_title"
                                tools:textColor="@color/textColorPrimary" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/text_flashatAge_barrier"
                                style="@style/TextAppearance.Flashat.Label"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text=":"
                                android:textColor="@{textColor}"
                                app:layout_constraintBottom_toBottomOf="@id/txt_id_card_dage_title"
                                app:layout_constraintEnd_toEndOf="@id/guideLine_user"
                                app:layout_constraintTop_toTopOf="@id/txt_id_card_dage_title"
                                tools:textColor="@color/textColorPrimary" />


                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_id_card_dage"
                                style="@style/TextAppearance.Flashat.Label.Bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/element_spacing"
                                android:ellipsize="end"
                                android:maxLines="1"
                                android:text="@{String.valueOf(viewModel.profile.dage)}"
                                android:textColor="@{textColor}"
                                app:flow_horizontalAlign="start"
                                app:layout_constrainedWidth="true"
                                app:layout_constraintBottom_toBottomOf="@+id/txt_id_card_dage_title"
                                app:layout_constraintEnd_toEndOf="@id/guideLine_main"
                                app:layout_constraintHorizontal_bias="0.0"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintStart_toEndOf="@id/guideLine_user"
                                app:layout_constraintTop_toTopOf="@+id/txt_id_card_dage_title"
                                tools:text="700"
                                tools:textColor="@color/colorPrimaryDark" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_id_card_birthday_title"
                                style="@style/TextAppearance.Flashat.Label.Bold"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/line_spacing"
                                android:layout_marginEnd="@dimen/line_spacing"
                                android:text="@string/id_card_title_birthday"
                                android:textColor="@{labelTextColor}"
                                app:layout_constraintEnd_toStartOf="@id/text_birthday_barrier"
                                app:layout_constraintStart_toStartOf="@id/txt_id_card_name_title"
                                app:layout_constraintTop_toBottomOf="@+id/txt_id_card_dage_title"
                                tools:textColor="@color/textColorPrimary" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/text_birthday_barrier"
                                style="@style/TextAppearance.Flashat.Label"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text=":"
                                android:textColor="@{textColor}"
                                app:layout_constraintBottom_toBottomOf="@id/txt_id_card_birthday_title"
                                app:layout_constraintEnd_toEndOf="@id/guideLine_user"
                                app:layout_constraintTop_toTopOf="@id/txt_id_card_birthday_title"
                                tools:textColor="@color/textColorPrimary" />


                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_id_card_birthday"
                                style="@style/TextAppearance.Flashat.Label.Bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/element_spacing"
                                android:ellipsize="end"
                                android:maxLines="1"
                                android:text="@{viewModel.profile.birthday}"
                                android:textColor="@{textColor}"
                                app:flow_horizontalAlign="start"
                                app:layout_constrainedWidth="true"
                                app:layout_constraintBottom_toBottomOf="@+id/txt_id_card_birthday_title"
                                app:layout_constraintEnd_toEndOf="@id/guideLine_main"
                                app:layout_constraintHorizontal_bias="0.0"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintStart_toEndOf="@id/guideLine_user"
                                app:layout_constraintTop_toTopOf="@+id/txt_id_card_birthday_title"
                                tools:text="8 July"
                                tools:textColor="@color/colorPrimaryDark" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_id_card_flag_title"
                                style="@style/TextAppearance.Flashat.Label.Bold"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/line_spacing"
                                android:layout_marginEnd="@dimen/line_spacing"
                                android:paddingBottom="@dimen/element_spacing"
                                android:text="@string/id_card_title_flag"
                                android:textColor="@{labelTextColor}"
                                app:layout_constraintEnd_toStartOf="@id/text_flashatAge_barrier"
                                app:layout_constraintStart_toStartOf="@id/txt_id_card_name_title"
                                app:layout_constraintTop_toBottomOf="@+id/txt_id_card_birthday_title"
                                tools:textColor="@color/textColorPrimary" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/text_flashatFlag_barrier"
                                style="@style/TextAppearance.Flashat.Label"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text=":"
                                android:textColor="@{textColor}"
                                app:layout_constraintEnd_toEndOf="@id/guideLine_user"
                                app:layout_constraintTop_toTopOf="@id/txt_id_card_flag_title"
                                tools:textColor="@color/textColorPrimary" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/id_card_flag"
                                android:layout_width="wrap_content"
                                android:layout_height="20dp"
                                android:layout_marginStart="@dimen/element_spacing"
                                android:paddingBottom="@dimen/element_spacing"
                                android:adjustViewBounds="true"
                                app:layout_constraintBottom_toBottomOf="@+id/txt_id_card_flag_title"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintStart_toEndOf="@+id/guideLine_user"
                                app:layout_constraintTop_toTopOf="@+id/txt_id_card_flag_title"
                                tools:srcCompat="@drawable/flag_france"/>

<!--                            goneIfNot="@{viewModel.accountDetails.showCountryFlag}"-->

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/superstar_premium_badge"
                                goneIfNot="@{viewModel.profile.premiumUser}"
                                android:layout_width="28dp"
                                android:layout_height="28dp"
                                android:elevation="4dp"
                                android:src="@drawable/ic_user_badge_premium"
                                app:layout_constraintStart_toStartOf="@+id/id_card_dp"
                                app:layout_constraintTop_toTopOf="@+id/id_card_dp" />

                            <com.makeramen.roundedimageview.RoundedImageView
                                android:id="@+id/id_card_dp"
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                android:layout_marginTop="@dimen/element_spacing"
                                android:layout_marginBottom="@dimen/element_spacing"
                                android:scaleType="centerCrop"
                                app:imageUrl="@{viewModel.isCurrentUser()? viewModel.currentUserProfile.thumbnail : viewModel.profile.thumbnail}"
                                app:layout_constraintBottom_toTopOf="@id/guideLine_bottom"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="@id/guideLine_main"
                                app:layout_constraintTop_toBottomOf="@+id/guideLine_top"
                                app:placeholder="@{@drawable/im_user_placeholder_opaque}"
                                app:riv_border_color="@color/white"
                                app:riv_border_width="3dp"
                                app:riv_corner_radius="@dimen/drawer_header_dp_size"
                                tools:src="@drawable/im_user_placeholder_opaque" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_id_card_issue_date_title"
                                style="@style/TextAppearance.Flashat.Label.Smaller.Bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/element_spacing"
                                android:layout_marginTop="@dimen/element_spacing"
                                android:text="@string/id_card_title_issue_date"
                                android:textColor="@{ isPresident ? subTitleColor : textColor}"
                                app:layout_constraintEnd_toStartOf="@id/txt_id_card_expiry_date_title"
                                app:layout_constraintStart_toStartOf="@id/guideLine_main"
                                app:layout_constraintTop_toBottomOf="@+id/guideLine_bottom"
                                tools:textColor="@color/textColorPrimary" />


                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_id_card_issue_date"
                                style="@style/TextAppearance.Flashat.Label.Smaller.Bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.profile.issueDate}"
                                android:textColor="@{ isPresident ? subTitleColor : textColor}"
                                app:layout_constraintEnd_toEndOf="@id/txt_id_card_issue_date_title"
                                app:layout_constraintStart_toStartOf="@id/txt_id_card_issue_date_title"
                                app:layout_constraintTop_toBottomOf="@id/txt_id_card_issue_date_title"
                                tools:text="01/01/24"
                                tools:textColor="@color/textColorPrimary" />


                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_id_card_expiry_date_title"
                                style="@style/TextAppearance.Flashat.Label.Smaller.Bold"
                                goneIfNot="@{viewModel.profile.premiumUser}"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/element_spacing"
                                android:layout_marginTop="@dimen/element_spacing"
                                android:text="@string/id_card_title_expiry_date"
                                android:textColor="@{ isPresident ? subTitleColor : textColor}"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@id/txt_id_card_issue_date_title"
                                app:layout_constraintTop_toBottomOf="@+id/guideLine_bottom"
                                tools:textColor="@color/textColorPrimary" />


                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_id_card_expiry_date"
                                style="@style/TextAppearance.Flashat.Label.Smaller.Bold"
                                goneIfNot="@{viewModel.profile.premiumUser}"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.profile.subscriptionExpiryDate}"
                                android:textColor="@{ isPresident ? subTitleColor : textColor}"
                                app:layout_constraintEnd_toEndOf="@id/txt_id_card_expiry_date_title"
                                app:layout_constraintStart_toStartOf="@id/txt_id_card_expiry_date_title"
                                app:layout_constraintTop_toBottomOf="@id/txt_id_card_expiry_date_title"
                                tools:text="05/08/24"
                                tools:textColor="@color/textColorPrimary" />

                        </androidx.constraintlayout.widget.ConstraintLayout>
                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/cardView_userStrength"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/element_spacing"
                        android:layout_marginTop="@dimen/element_spacing"
                        app:cardCornerRadius="@dimen/element_spacing"
                        app:cardElevation="@dimen/cardview_default_elevation"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/cardView_header">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/layout_userStrength"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@{idCardUserStrengthDrawBackground}"
                            tools:background="@drawable/bg_idcard_citizen">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/text_strength"
                                style="@style/TextAppearance.Flashat.Subtitle1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textAllCaps= "@{isPresident ? true : false}"
                                android:layout_marginTop="@dimen/activity_margin"
                                android:background="@{textIdCardDrawBackground}"
                                android:elevation="@dimen/element_spacing"
                                android:paddingHorizontal="@dimen/element_spacing"
                                android:paddingVertical="@dimen/line_spacing"
                                android:text="@string/id_card_header_user_strength"
                                android:textColor="@{isPresident ? subTitleColor : idColor}"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                tools:background="@drawable/bg_label_idcard_citizen_ambassador"
                                tools:textColor="@color/white" />

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:id="@+id/layoutPremiumRatingTribeBanner"
                                android:layout_width="wrap_content"
                                android:layout_height="0dp"
                                android:background="@drawable/bg_id_card_tribe"
                                android:elevation="@dimen/element_spacing"
                                app:layout_constraintBottom_toBottomOf="@id/text_strength"
                                app:layout_constraintEnd_toEndOf="@+id/cardView_items"
                                app:layout_constraintTop_toTopOf="@id/text_strength">

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/textViewPremiumRating"
                                    style="@style/TextAppearance.Flashat.Label.Smaller.Bold"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="16dp"
                                    android:text="@{@string/user_strength_rating_prefix(viewModel.profile.flaxRatePercentage.intValue())}"
                                    android:textAllCaps="@{isPresident ? true : false}"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toTopOf="parent"
                                    tools:text="Rating: 99%" />

                                <View
                                    android:id="@+id/viewSeparator"
                                    android:layout_width="2dp"
                                    android:layout_height="0dp"
                                    android:layout_marginStart="4dp"
                                    android:layout_marginTop="8dp"
                                    android:layout_marginEnd="4dp"
                                    android:layout_marginBottom="8dp"
                                    app:goneIf="@{isVisitor || isResident}"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toStartOf="@+id/textViewPremiumTribe"
                                    app:layout_constraintStart_toEndOf="@id/textViewPremiumRating"
                                    app:layout_constraintTop_toTopOf="parent" />

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/textViewPremiumTribe"
                                    style="@style/TextAppearance.Flashat.Label.Smaller.Bold"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="8dp"
                                    app:goneIf="@{isVisitor || isResident}"
                                    android:text="@{@string/id_card_tribe_count(viewModel.profile.tribeParticipantsCount.toString())}"
                                    android:textAllCaps="@{isPresident ? true : false}"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintStart_toEndOf="@id/viewSeparator"
                                    app:layout_constraintTop_toTopOf="parent"
                                    tools:text="Tribe: 250" />
                            </androidx.constraintlayout.widget.ConstraintLayout>
                            <com.google.android.material.card.MaterialCardView
                                android:id="@+id/cardView_items"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_margin="@dimen/element_spacing"
                                app:cardBackgroundColor="@{isResident==true?@color/textColorOnPrimary : @color/buttonTabBackground}"
                                app:cardCornerRadius="@dimen/element_spacing"
                                app:cardElevation="@dimen/cardview_default_elevation"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent">

                                <androidx.constraintlayout.widget.ConstraintLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:paddingTop="@dimen/activity_margin">

                                    <androidx.constraintlayout.widget.ConstraintLayout
                                        android:id="@+id/layout_linear_top"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/double_margin"
                                        app:layout_constraintEnd_toEndOf="parent"
                                        app:layout_constraintStart_toStartOf="parent"
                                        app:layout_constraintTop_toTopOf="parent"
                                        app:layout_constraintBottom_toBottomOf="parent"
                                        tools:ignore="TooDeepLayout">


                                        <com.google.android.material.card.MaterialCardView
                                            android:id="@+id/card_pl"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            app:cardCornerRadius="@dimen/element_spacing"
                                            app:cardElevation="@dimen/element_spacing"
                                            app:layout_constraintBottom_toBottomOf="@id/card_lv"
                                            app:layout_constraintEnd_toStartOf="@id/card_others"
                                            app:layout_constraintStart_toEndOf="@id/card_rank_rating"
                                            app:layout_constraintTop_toTopOf="@id/card_lv">

                                        <androidx.constraintlayout.widget.ConstraintLayout
                                            android:id="@+id/layout_pl"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:padding="@dimen/line_spacing"
                                            app:layout_constraintTop_toTopOf="parent"
                                            tools:background="@drawable/bg_idcard_citizen">

                                            <androidx.appcompat.widget.AppCompatImageView
                                                android:id="@+id/img_id_card_pl"
                                                android:layout_width="22dp"
                                                android:layout_height="22dp"
                                                android:src="@drawable/ic_idcard_pl"
                                                app:layout_constraintBottom_toBottomOf="parent"
                                                app:layout_constraintStart_toStartOf="parent"
                                                app:layout_constraintTop_toTopOf="parent"
                                                app:tint="@{userIconTint}" />

                                            <androidx.constraintlayout.widget.ConstraintLayout
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:layout_marginStart="2dp"
                                                app:layout_constraintBottom_toBottomOf="@id/img_id_card_pl"
                                                app:layout_constraintStart_toEndOf="@id/img_id_card_pl"
                                                app:layout_constraintTop_toTopOf="@id/img_id_card_pl">

                                                <androidx.constraintlayout.widget.ConstraintLayout
                                                    android:id="@+id/pl_data"
                                                    android:layout_width="wrap_content"
                                                    android:layout_height="wrap_content"
                                                    app:layout_constraintEnd_toEndOf="parent"
                                                    app:layout_constraintStart_toStartOf="parent"
                                                    app:layout_constraintTop_toTopOf="parent">

                                                    <androidx.appcompat.widget.AppCompatTextView
                                                        android:id="@+id/text_pl"
                                                        style="@style/TextAppearance.Flashat.Label.Tiny.Bold"
                                                        goneIf="@{viewModel.profile.showCrownForSkill}"
                                                        android:layout_width="wrap_content"
                                                        android:layout_height="wrap_content"
                                                        android:text="@{viewModel.profile.plIsValid? @string/id_card_visitor_rating:viewModel.profile.plModified}"
                                                        android:textColor="@{userIconTextTint}"
                                                        app:layout_constraintEnd_toEndOf="parent"
                                                        app:layout_constraintStart_toStartOf="parent"
                                                        app:layout_constraintTop_toTopOf="parent"
                                                        tools:text="65" />

                                                    <androidx.appcompat.widget.AppCompatImageView
                                                        android:id="@+id/image_pl"
                                                        goneIfNot="@{viewModel.profile.showCrownForSkill}"
                                                        android:layout_width="wrap_content"
                                                        android:layout_height="wrap_content"
                                                        app:layout_constraintEnd_toEndOf="parent"
                                                        app:layout_constraintStart_toStartOf="parent"
                                                        app:layout_constraintTop_toTopOf="parent"
                                                        app:srcCompat="@drawable/bg_max_crown"
                                                        tools:text="65" />

                                                </androidx.constraintlayout.widget.ConstraintLayout>

                                                <androidx.appcompat.widget.AppCompatTextView
                                                    android:id="@+id/text_pl_title"
                                                    style="@style/TextAppearance.Flashat.Label.Tiny"
                                                    android:layout_width="wrap_content"
                                                    android:layout_height="wrap_content"
                                                    android:text="@string/id_card_pl"
                                                    android:textColor="@{userIconTextTint}"
                                                    app:layout_constraintEnd_toEndOf="parent"
                                                    app:layout_constraintStart_toStartOf="parent"
                                                    app:layout_constraintTop_toBottomOf="@+id/pl_data" />
                                            </androidx.constraintlayout.widget.ConstraintLayout>
                                        </androidx.constraintlayout.widget.ConstraintLayout>
                                        </com.google.android.material.card.MaterialCardView>



                                        <com.google.android.material.card.MaterialCardView
                                            android:id="@+id/card_lv"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="@dimen/element_spacing"
                                            app:cardCornerRadius="@dimen/element_spacing"
                                            app:cardElevation="@dimen/element_spacing"
                                            app:layout_constraintEnd_toStartOf="@id/card_rank_rating"
                                            app:layout_constraintStart_toStartOf="parent"
                                            app:layout_constraintTop_toTopOf="parent"
                                            tools:ignore="TooManyViews">

                                            <androidx.constraintlayout.widget.ConstraintLayout
                                                android:id="@+id/layout_lv"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:padding="@dimen/line_spacing"
                                                app:layout_constraintTop_toTopOf="parent"
                                                tools:background="@drawable/bg_idcard_citizen">

                                                <androidx.appcompat.widget.AppCompatImageView
                                                    android:id="@+id/img_id_card_lv"
                                                    android:layout_width="22dp"
                                                    android:layout_height="22dp"
                                                    android:src="@drawable/ic_idcard_lv"
                                                    app:layout_constraintBottom_toBottomOf="parent"
                                                    app:layout_constraintStart_toStartOf="parent"
                                                    app:layout_constraintTop_toTopOf="parent"
                                                    app:tint="@{userIconTint}" />

                                                <androidx.constraintlayout.widget.ConstraintLayout
                                                    android:layout_width="wrap_content"
                                                    android:layout_height="wrap_content"
                                                    android:layout_marginStart="2dp"
                                                    app:layout_constraintBottom_toBottomOf="@id/img_id_card_lv"
                                                    app:layout_constraintStart_toEndOf="@id/img_id_card_lv"
                                                    app:layout_constraintTop_toTopOf="@id/img_id_card_lv">

                                                    <androidx.constraintlayout.widget.ConstraintLayout
                                                        android:id="@+id/lv_data"
                                                        android:layout_width="wrap_content"
                                                        android:layout_height="wrap_content"
                                                        app:layout_constraintEnd_toEndOf="parent"
                                                        app:layout_constraintStart_toStartOf="parent"
                                                        app:layout_constraintTop_toTopOf="parent">

                                                        <androidx.appcompat.widget.AppCompatTextView
                                                            android:id="@+id/text_lv"
                                                            style="@style/TextAppearance.Flashat.Label.Tiny.Bold"
                                                            goneIf="@{viewModel.profile.showCrownForGenerosity}"
                                                            android:layout_width="wrap_content"
                                                            android:layout_height="wrap_content"
                                                            android:text="@{viewModel.profile.lvModified}"
                                                            android:textColor="@{userIconTextTint}"
                                                            app:layout_constraintEnd_toEndOf="parent"
                                                            app:layout_constraintStart_toStartOf="parent"
                                                            app:layout_constraintTop_toTopOf="parent"
                                                            tools:text="65" />

                                                        <androidx.appcompat.widget.AppCompatImageView
                                                            android:id="@+id/image_lv"
                                                            goneIfNot="@{viewModel.profile.showCrownForGenerosity}"
                                                            android:layout_width="wrap_content"
                                                            android:layout_height="wrap_content"
                                                            android:src="@drawable/bg_max_crown"
                                                            app:layout_constraintEnd_toEndOf="parent"
                                                            app:layout_constraintStart_toStartOf="parent"
                                                            app:layout_constraintTop_toTopOf="@id/text_lv"
                                                            tools:text="65" />
                                                    </androidx.constraintlayout.widget.ConstraintLayout>

                                                    <androidx.appcompat.widget.AppCompatTextView
                                                        android:id="@+id/text_title_lv"
                                                        style="@style/TextAppearance.Flashat.Label.Tiny"
                                                        android:layout_width="wrap_content"
                                                        android:layout_height="wrap_content"
                                                        android:text="@string/id_card_lv"
                                                        android:textColor="@{userIconTextTint}"
                                                        app:layout_constraintEnd_toEndOf="parent"
                                                        app:layout_constraintStart_toStartOf="parent"
                                                        app:layout_constraintTop_toBottomOf="@+id/lv_data" />
                                                </androidx.constraintlayout.widget.ConstraintLayout>
                                            </androidx.constraintlayout.widget.ConstraintLayout>
                                        </com.google.android.material.card.MaterialCardView>


                                        <com.google.android.material.card.MaterialCardView
                                            android:id="@+id/card_rank_rating"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            app:cardCornerRadius="@dimen/element_spacing"
                                            app:cardElevation="@dimen/element_spacing"
                                            android:layout_marginHorizontal="@dimen/line_spacing"
                                            app:layout_constraintBottom_toBottomOf="@id/card_lv"
                                            app:layout_constraintEnd_toStartOf="@+id/card_pl"
                                            app:layout_constraintStart_toEndOf="@+id/card_lv"
                                            app:layout_constraintTop_toTopOf="@id/card_lv">

                                            <androidx.constraintlayout.widget.ConstraintLayout
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:padding="@dimen/line_spacing"
                                                tools:background="@drawable/bg_idcard_citizen">


                                                <androidx.constraintlayout.widget.ConstraintLayout
                                                    android:id="@+id/layout_rating"
                                                    android:layout_width="wrap_content"
                                                    android:layout_height="wrap_content"
                                                    android:layout_marginStart="@dimen/element_spacing"
                                                    android:clickable="@{viewModel.isCurrentUser()}"
                                                    app:goneIfNot="@{isVisitor}"
                                                    app:layout_constraintBottom_toBottomOf="parent"
                                                    app:layout_constraintTop_toTopOf="parent"
                                                    app:layout_constraintStart_toStartOf="parent"
                                                    app:layout_constraintEnd_toEndOf="parent">

                                                    <androidx.appcompat.widget.AppCompatImageView
                                                        android:id="@+id/img_id_card_rating"
                                                        android:layout_width="22dp"
                                                        android:layout_height="22dp"
                                                        android:src="@drawable/ic_idcard_rating"
                                                        app:layout_constraintBottom_toBottomOf="parent"
                                                        app:layout_constraintStart_toStartOf="parent"
                                                        app:layout_constraintTop_toTopOf="parent"
                                                        app:tint="@{userIconTint}" />

                                                    <androidx.constraintlayout.widget.ConstraintLayout
                                                        android:layout_width="wrap_content"
                                                        android:layout_height="wrap_content"
                                                        android:layout_marginStart="2dp"
                                                        app:layout_constraintBottom_toBottomOf="@id/img_id_card_rating"
                                                        app:layout_constraintStart_toEndOf="@id/img_id_card_rating"
                                                        app:layout_constraintTop_toTopOf="@id/img_id_card_rating">

                                                        <androidx.appcompat.widget.AppCompatTextView
                                                            android:id="@+id/text_rating"
                                                            style="@style/TextAppearance.Flashat.Label.Tiny.Bold"
                                                            android:layout_width="wrap_content"
                                                            android:layout_height="wrap_content"
                                                            android:text="@{@string/common_percentage_value(viewModel.profile.flaxRatePercentage.intValue())}"
                                                            android:textColor="@{userIconTextTint}"
                                                            app:layout_constraintEnd_toEndOf="parent"
                                                            app:layout_constraintStart_toStartOf="parent"
                                                            app:layout_constraintTop_toTopOf="parent"
                                                            tools:text="65%" />

                                                        <androidx.appcompat.widget.AppCompatTextView
                                                            android:id="@+id/text_title_rating"
                                                            style="@style/TextAppearance.Flashat.Label.Tiny"
                                                            android:layout_width="wrap_content"
                                                            android:layout_height="wrap_content"
                                                            android:text="@string/id_card_rating"
                                                            android:textColor="@{userIconTextTint}"
                                                            app:layout_constraintEnd_toEndOf="parent"
                                                            app:layout_constraintStart_toStartOf="parent"
                                                            app:layout_constraintTop_toBottomOf="@+id/text_rating" />
                                                    </androidx.constraintlayout.widget.ConstraintLayout>
                                                </androidx.constraintlayout.widget.ConstraintLayout>

                                                <androidx.constraintlayout.widget.ConstraintLayout
                                                    android:id="@+id/layout_rank"
                                                    android:layout_width="wrap_content"
                                                    android:layout_height="wrap_content"
                                                    app:goneIf="@{isVisitor}"
                                                    app:layout_constraintBottom_toBottomOf="parent"
                                                    app:layout_constraintTop_toTopOf="parent"
                                                    app:layout_constraintStart_toStartOf="parent"
                                                    app:layout_constraintEnd_toEndOf="parent">

                                                    <androidx.appcompat.widget.AppCompatImageView
                                                        android:id="@+id/img_id_card_rank"
                                                        android:layout_width="22dp"
                                                        android:layout_height="22dp"
                                                        android:src="@drawable/ic_idcard_popularity"
                                                        app:layout_constraintBottom_toBottomOf="parent"
                                                        app:layout_constraintStart_toStartOf="parent"
                                                        app:layout_constraintTop_toTopOf="parent"
                                                        app:tint="@{rankTintColor}" />

                                                    <androidx.constraintlayout.widget.ConstraintLayout
                                                        android:layout_width="wrap_content"
                                                        android:layout_height="wrap_content"
                                                        android:layout_marginStart="2dp"
                                                        app:layout_constraintBottom_toBottomOf="@id/img_id_card_rank"
                                                        app:layout_constraintStart_toEndOf="@id/img_id_card_rank"
                                                        app:layout_constraintTop_toTopOf="@id/img_id_card_rank">

                                                        <androidx.constraintlayout.widget.ConstraintLayout
                                                            android:id="@+id/rank_data"
                                                            android:layout_width="wrap_content"
                                                            android:layout_height="wrap_content"
                                                            app:layout_constraintEnd_toEndOf="parent"
                                                            app:layout_constraintStart_toStartOf="parent"
                                                            app:layout_constraintTop_toTopOf="parent">

                                                            <androidx.appcompat.widget.AppCompatTextView
                                                                android:id="@+id/text_rank"
                                                                style="@style/TextAppearance.Flashat.Label.Tiny.Bold"
                                                                android:layout_width="wrap_content"
                                                                android:layout_height="wrap_content"
                                                                android:textColor="@{rankTintColor}"
                                                                app:layout_constraintEnd_toEndOf="parent"
                                                                app:layout_constraintStart_toStartOf="parent"
                                                                app:layout_constraintTop_toTopOf="parent"
                                                                tools:text="4" />
                                                        </androidx.constraintlayout.widget.ConstraintLayout>

                                                        <androidx.appcompat.widget.AppCompatTextView
                                                            android:id="@+id/text_title_rank"
                                                            style="@style/TextAppearance.Flashat.Label.Tiny"
                                                            android:layout_width="wrap_content"
                                                            android:layout_height="wrap_content"
                                                            android:text="@string/user_popularity_prefix"
                                                            android:textColor="@{rankTintColor}"
                                                            app:layout_constraintEnd_toEndOf="parent"
                                                            app:layout_constraintStart_toStartOf="parent"
                                                            app:layout_constraintTop_toBottomOf="@+id/rank_data" />
                                                    </androidx.constraintlayout.widget.ConstraintLayout>
                                                </androidx.constraintlayout.widget.ConstraintLayout>
                                            </androidx.constraintlayout.widget.ConstraintLayout>
                                        </com.google.android.material.card.MaterialCardView>


                                        <com.google.android.material.card.MaterialCardView
                                            android:id="@+id/card_others"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            app:cardCornerRadius="@dimen/element_spacing"
                                            app:cardElevation="@dimen/element_spacing"
                                            app:layout_constraintBottom_toBottomOf="@+id/card_pl"
                                            app:layout_constraintEnd_toEndOf="parent"
                                            app:layout_constraintStart_toEndOf="@id/card_pl"
                                            app:layout_constraintTop_toTopOf="@+id/card_pl"
                                            android:layout_marginHorizontal="@dimen/line_spacing">


                                            <androidx.constraintlayout.widget.ConstraintLayout
                                                android:id="@+id/layout_others"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:padding="@dimen/element_spacing"
                                                android:clickable="true"
                                                tools:background="@drawable/bg_idcard_citizen">

                                                <androidx.appcompat.widget.AppCompatImageView
                                                    android:id="@+id/img_id_card_other"
                                                    android:layout_width="20dp"
                                                    android:layout_height="20dp"
                                                    android:src="@drawable/ic_idcard_other"
                                                    app:layout_constraintStart_toStartOf="parent"
                                                    app:layout_constraintTop_toTopOf="parent"
                                                    app:layout_constraintBottom_toBottomOf="parent"
                                                    app:tint="@{userIconTint}" />

                                                <androidx.appcompat.widget.AppCompatTextView
                                                    android:id="@+id/text_title_other"
                                                    style="@style/TextAppearance.Flashat.Label.Tiny"
                                                    android:layout_width="wrap_content"
                                                    android:layout_height="wrap_content"
                                                    android:text="@string/id_card_other"
                                                    android:textColor="@{userIconTextTint}"
                                                    app:layout_constraintStart_toEndOf="@id/img_id_card_other"
                                                    app:layout_constraintTop_toTopOf="@id/img_id_card_other"
                                                    app:layout_constraintBottom_toBottomOf="@id/img_id_card_other"
                                                    android:layout_marginStart="@dimen/line_spacing"/>
                                            </androidx.constraintlayout.widget.ConstraintLayout>
                                        </com.google.android.material.card.MaterialCardView>

                                        <com.google.android.material.card.MaterialCardView
                                            android:id="@+id/card_others_count"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="@dimen/activity_margin"
                                            app:cardCornerRadius="@dimen/element_spacing"
                                            app:layout_constraintStart_toStartOf="parent"
                                            android:layout_marginBottom="@dimen/element_spacing"
                                            app:layout_constraintTop_toBottomOf="@id/card_pl"
                                            app:layout_constraintBottom_toBottomOf="parent"
                                            app:layout_constraintEnd_toStartOf="@id/card_others"
                                            android:layout_marginStart="@dimen/element_spacing">

                                            <androidx.constraintlayout.widget.ConstraintLayout
                                                android:id="@+id/layout_card_others"
                                                android:layout_width="match_parent"
                                                android:layout_height="wrap_content"
                                                tools:background="@drawable/bg_idcard_citizen">

                                            <LinearLayout
                                                android:layout_width="match_parent"
                                                android:layout_height="wrap_content"
                                                app:layout_constraintTop_toTopOf="parent"
                                                android:orientation="horizontal">

                                                <androidx.constraintlayout.widget.ConstraintLayout
                                                    android:id="@+id/layout_followers"
                                                    android:layout_width="wrap_content"
                                                    android:layout_height="wrap_content"
                                                    android:padding="@dimen/line_spacing"
                                                   android:layout_marginStart="2dp">

                                                    <androidx.appcompat.widget.AppCompatImageView
                                                        android:id="@+id/img_id_card_followers"
                                                        android:layout_width="22dp"
                                                        android:layout_height="22dp"
                                                        android:src="@drawable/ic_home_public_tab_followers_grey"
                                                        app:layout_constraintBottom_toBottomOf="parent"
                                                        app:layout_constraintStart_toStartOf="parent"
                                                        app:layout_constraintTop_toTopOf="parent"
                                                        app:tint="@{userIconTint}" />

                                                    <androidx.constraintlayout.widget.ConstraintLayout
                                                        android:layout_width="wrap_content"
                                                        android:layout_height="wrap_content"
                                                        android:layout_marginStart="2dp"
                                                        app:layout_constraintBottom_toBottomOf="@id/img_id_card_followers"
                                                        app:layout_constraintStart_toEndOf="@id/img_id_card_followers"
                                                        app:layout_constraintTop_toTopOf="@id/img_id_card_followers">

                                                        <androidx.appcompat.widget.AppCompatTextView
                                                            android:id="@+id/text_followers"
                                                            style="@style/TextAppearance.Flashat.Label.Tiny.Bold"
                                                            android:layout_width="wrap_content"
                                                            android:layout_height="wrap_content"
                                                            android:text="@{viewModel.profile.followersFormatted}"
                                                            android:textColor="@{userIconTextTint}"
                                                            app:layout_constraintEnd_toEndOf="parent"
                                                            app:layout_constraintStart_toStartOf="parent"
                                                            app:layout_constraintTop_toTopOf="parent"
                                                            tools:text="50" />

                                                        <androidx.appcompat.widget.AppCompatTextView
                                                            android:id="@+id/text_title_followers"
                                                            style="@style/TextAppearance.Flashat.Label.Tiny"
                                                            android:layout_width="wrap_content"
                                                            android:layout_height="wrap_content"
                                                            android:text="@string/home_private_tab_followers"
                                                            android:textAllCaps="false"
                                                            android:textColor="@{userIconTextTint}"
                                                            app:layout_constraintEnd_toEndOf="parent"
                                                            app:layout_constraintStart_toStartOf="parent"
                                                            app:layout_constraintTop_toBottomOf="@+id/text_followers" />
                                                    </androidx.constraintlayout.widget.ConstraintLayout>
                                                </androidx.constraintlayout.widget.ConstraintLayout>


                                                <androidx.constraintlayout.widget.ConstraintLayout
                                                    android:id="@+id/layout_dears"
                                                    android:layout_width="wrap_content"
                                                    android:layout_height="wrap_content"
                                                    android:padding="@dimen/line_spacing"
                                                    android:layout_marginStart="2dp">

                                                    <androidx.appcompat.widget.AppCompatImageView
                                                        android:id="@+id/img_id_card_dears"
                                                        android:layout_width="24dp"
                                                        android:layout_height="24dp"
                                                        android:src="@drawable/ic_idcard_dears"
                                                        app:layout_constraintBottom_toBottomOf="parent"
                                                        app:layout_constraintStart_toStartOf="parent"
                                                        app:layout_constraintTop_toTopOf="parent"
                                                        app:tint="@{userIconTint}" />

                                                    <androidx.constraintlayout.widget.ConstraintLayout
                                                        android:layout_width="wrap_content"
                                                        android:layout_height="wrap_content"
                                                        android:layout_marginStart="2dp"
                                                        app:layout_constraintBottom_toBottomOf="@id/img_id_card_dears"
                                                        app:layout_constraintStart_toEndOf="@id/img_id_card_dears"
                                                        app:layout_constraintTop_toTopOf="@id/img_id_card_dears">

                                                        <androidx.appcompat.widget.AppCompatTextView
                                                            android:id="@+id/text_dears"
                                                            style="@style/TextAppearance.Flashat.Label.Tiny.Bold"
                                                            android:layout_width="wrap_content"
                                                            android:layout_height="wrap_content"
                                                            android:text="@{viewModel.profile.dearsFormatted}"
                                                            android:textColor="@{userIconTextTint}"
                                                            app:layout_constraintEnd_toEndOf="parent"
                                                            app:layout_constraintStart_toStartOf="parent"
                                                            app:layout_constraintTop_toTopOf="parent"
                                                            tools:text="50" />

                                                        <androidx.appcompat.widget.AppCompatTextView
                                                            android:id="@+id/text_title_dears"
                                                            style="@style/TextAppearance.Flashat.Label.Tiny"
                                                            android:layout_width="wrap_content"
                                                            android:layout_height="wrap_content"
                                                            android:text="@string/profile_dear"
                                                            android:textColor="@{userIconTextTint}"
                                                            app:layout_constraintEnd_toEndOf="parent"
                                                            app:layout_constraintStart_toStartOf="parent"
                                                            app:layout_constraintTop_toBottomOf="@+id/text_dears" />
                                                    </androidx.constraintlayout.widget.ConstraintLayout>
                                                </androidx.constraintlayout.widget.ConstraintLayout>


                                                <androidx.constraintlayout.widget.ConstraintLayout
                                                    android:id="@+id/layout_fans"
                                                    android:layout_width="wrap_content"
                                                    android:layout_height="wrap_content"
                                                    android:layout_marginStart="2dp"
                                                    android:padding="@dimen/line_spacing"
                                                    app:layout_constraintBottom_toBottomOf="parent"
                                                    app:layout_constraintTop_toTopOf="@id/layout_dears">

                                                    <androidx.appcompat.widget.AppCompatImageView
                                                        android:id="@+id/img_id_card_fans"
                                                        android:layout_width="22dp"
                                                        android:layout_height="22dp"
                                                        android:src="@drawable/ic_idcard_fans"
                                                        app:layout_constraintBottom_toBottomOf="parent"
                                                        app:layout_constraintStart_toStartOf="parent"
                                                        app:layout_constraintTop_toTopOf="parent"
                                                        app:tint="@{userIconTint}" />

                                                    <androidx.constraintlayout.widget.ConstraintLayout
                                                        android:layout_width="wrap_content"
                                                        android:layout_height="wrap_content"
                                                        android:layout_marginStart="2dp"
                                                        app:layout_constraintBottom_toBottomOf="@id/img_id_card_fans"
                                                        app:layout_constraintStart_toEndOf="@id/img_id_card_fans"
                                                        app:layout_constraintTop_toTopOf="@id/img_id_card_fans">

                                                        <androidx.appcompat.widget.AppCompatTextView
                                                            android:id="@+id/text_fans"
                                                            style="@style/TextAppearance.Flashat.Label.Tiny.Bold"
                                                            android:layout_width="wrap_content"
                                                            android:layout_height="wrap_content"
                                                            android:text="@{viewModel.profile.fansFormatted}"
                                                            android:textColor="@{userIconTextTint}"
                                                            app:layout_constraintEnd_toEndOf="parent"
                                                            app:layout_constraintStart_toStartOf="parent"
                                                            app:layout_constraintTop_toTopOf="parent"
                                                            tools:text="65" />

                                                        <androidx.appcompat.widget.AppCompatTextView
                                                            android:id="@+id/text_title_fans"
                                                            style="@style/TextAppearance.Flashat.Label.Tiny"
                                                            android:layout_width="wrap_content"
                                                            android:layout_height="wrap_content"
                                                            android:text="@string/profile_fans"
                                                            android:textColor="@{userIconTextTint}"
                                                            app:layout_constraintEnd_toEndOf="parent"
                                                            app:layout_constraintStart_toStartOf="parent"
                                                            app:layout_constraintTop_toBottomOf="@+id/text_fans" />
                                                    </androidx.constraintlayout.widget.ConstraintLayout>
                                                </androidx.constraintlayout.widget.ConstraintLayout>

                                                <androidx.constraintlayout.widget.ConstraintLayout
                                                    android:id="@+id/layout_likers"
                                                    android:layout_width="wrap_content"
                                                    android:layout_height="wrap_content"
                                                    android:layout_marginStart="2dp"
                                                    app:layout_constraintBottom_toBottomOf="@id/layout_fans"
                                                    app:layout_constraintTop_toTopOf="@id/layout_fans">

                                                    <androidx.appcompat.widget.AppCompatImageView
                                                        android:id="@+id/img_id_card_likers"
                                                        android:layout_width="22dp"
                                                        android:layout_height="22dp"
                                                        android:src="@drawable/ic_idcard_likers"
                                                        app:layout_constraintBottom_toBottomOf="parent"
                                                        app:layout_constraintStart_toStartOf="parent"
                                                        app:layout_constraintTop_toTopOf="parent"
                                                        app:tint="@{userIconTint}" />

                                                    <androidx.constraintlayout.widget.ConstraintLayout
                                                        android:layout_width="wrap_content"
                                                        android:layout_height="wrap_content"
                                                        android:padding="@dimen/line_spacing"
                                                        app:layout_constraintBottom_toBottomOf="@id/img_id_card_likers"
                                                        app:layout_constraintStart_toEndOf="@id/img_id_card_likers"
                                                        app:layout_constraintTop_toTopOf="@id/img_id_card_likers">

                                                        <androidx.appcompat.widget.AppCompatTextView
                                                            android:id="@+id/text_likers"
                                                            style="@style/TextAppearance.Flashat.Label.Tiny.Bold"
                                                            android:layout_width="wrap_content"
                                                            android:layout_height="wrap_content"
                                                            android:text="@{viewModel.profile.likersFormatted}"
                                                            android:textColor="@{userIconTextTint}"
                                                            app:layout_constraintEnd_toEndOf="parent"
                                                            app:layout_constraintStart_toStartOf="parent"
                                                            app:layout_constraintTop_toTopOf="parent"
                                                            tools:text="105" />

                                                        <androidx.appcompat.widget.AppCompatTextView
                                                            android:id="@+id/text_title_likers"
                                                            style="@style/TextAppearance.Flashat.Label.Tiny"
                                                            android:layout_width="wrap_content"
                                                            android:layout_height="wrap_content"
                                                            android:text="@string/profile_likers"
                                                            android:textColor="@{userIconTextTint}"
                                                            app:layout_constraintEnd_toEndOf="parent"
                                                            app:layout_constraintStart_toStartOf="parent"
                                                            app:layout_constraintTop_toBottomOf="@+id/text_likers" />
                                                    </androidx.constraintlayout.widget.ConstraintLayout>
                                                </androidx.constraintlayout.widget.ConstraintLayout>

                                            </LinearLayout>
                                            </androidx.constraintlayout.widget.ConstraintLayout>

                                        </com.google.android.material.card.MaterialCardView>


                                        <com.google.android.material.card.MaterialCardView
                                            android:id="@+id/card_stars"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            app:cardCornerRadius="@dimen/element_spacing"
                                            app:cardElevation="@dimen/element_spacing"
                                            android:layout_marginStart="2dp"
                                            app:layout_constraintBottom_toBottomOf="@id/card_others_count"
                                           app:layout_constraintStart_toEndOf="@id/card_others_count"
                                            app:layout_constraintTop_toTopOf="@id/card_others_count"
                                            app:layout_constraintEnd_toEndOf="parent">

                                            <androidx.constraintlayout.widget.ConstraintLayout
                                                android:id="@+id/layout_stars"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:padding="@dimen/line_spacing"
                                                tools:background="@drawable/bg_idcard_citizen"
                                                app:layout_constraintTop_toTopOf="parent">

                                                <androidx.appcompat.widget.AppCompatImageView
                                                    android:id="@+id/img_id_card_stars"
                                                    android:layout_width="22dp"
                                                    android:layout_height="22dp"
                                                    android:src="@drawable/ic_idcard_stars"
                                                    app:layout_constraintStart_toStartOf="parent"
                                                    app:layout_constraintTop_toTopOf="parent"
                                                    app:layout_constraintBottom_toBottomOf="parent"
                                                    app:tint="@{userIconTint}" />

                                                <androidx.constraintlayout.widget.ConstraintLayout
                                                    android:layout_width="wrap_content"
                                                    android:layout_height="wrap_content"
                                                    android:layout_marginStart="2dp"
                                                    app:layout_constraintBottom_toBottomOf="@id/img_id_card_stars"
                                                    app:layout_constraintStart_toEndOf="@id/img_id_card_stars"
                                                    app:layout_constraintTop_toTopOf="@id/img_id_card_stars">

                                                    <androidx.appcompat.widget.AppCompatTextView
                                                        android:id="@+id/text_stars"
                                                        style="@style/TextAppearance.Flashat.Label.Tiny.Bold"
                                                        android:layout_width="wrap_content"
                                                        android:layout_height="wrap_content"
                                                        android:text="@{viewModel.profile.starsFormatted}"
                                                        android:textColor="@{userIconTextTint}"
                                                        app:layout_constraintEnd_toEndOf="parent"
                                                        app:layout_constraintStart_toStartOf="parent"
                                                        app:layout_constraintTop_toTopOf="parent"
                                                        tools:text="100" />

                                                    <androidx.appcompat.widget.AppCompatTextView
                                                        android:id="@+id/text_title_stars"
                                                        style="@style/TextAppearance.Flashat.Label.Tiny"
                                                        android:layout_width="wrap_content"
                                                        android:layout_height="wrap_content"
                                                        android:text="@string/profile_stars"
                                                        android:textColor="@{userIconTextTint}"
                                                        app:layout_constraintEnd_toEndOf="parent"
                                                        app:layout_constraintStart_toStartOf="parent"
                                                        app:layout_constraintTop_toBottomOf="@+id/text_stars" />
                                                </androidx.constraintlayout.widget.ConstraintLayout>

                                            </androidx.constraintlayout.widget.ConstraintLayout>

                                        </com.google.android.material.card.MaterialCardView>


                                    </androidx.constraintlayout.widget.ConstraintLayout>
                                </androidx.constraintlayout.widget.ConstraintLayout>

                            </com.google.android.material.card.MaterialCardView>
                        </androidx.constraintlayout.widget.ConstraintLayout>
                    </com.google.android.material.card.MaterialCardView>


                    <LinearLayout
                        android:id="@+id/action_others_data"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:baselineAligned="false"
                        app:layout_constraintTop_toBottomOf="@id/cardView_userStrength"
                        app:layout_constraintStart_toStartOf="@id/cardView_userStrength"
                        app:layout_constraintEnd_toEndOf="@id/cardView_userStrength"
                        android:layout_marginVertical="@dimen/activity_margin"
                        android:orientation="horizontal">

                        <include
                            android:id="@+id/action_huddle"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            app:goneIf="@{isVisitor}"
                            app:userActionIcon="@{@drawable/ic_home_public_tab_huddles_grey}"
                            app:actionText="@{@string/home_public_tab_huddles}"
                            app:actionItemColor="@{actionItemColor}"
                            layout="@layout/single_idcard_action_layout"
                            android:layout_weight="1"/>
                        <include
                            android:id="@+id/action_postat"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            app:userActionIcon="@{@drawable/ic_home_public_tab_postat_grey}"
                            app:actionText="@{@string/home_private_tab_postat}"
                            app:actionItemColor="@{actionItemColor}"
                            layout="@layout/single_idcard_action_layout"
                            android:layout_weight="1"
                            android:layout_marginHorizontal="@dimen/element_spacing"/>
                        <include
                            android:id="@+id/action_flash"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            app:userActionIcon="@{@drawable/ic_home_public_tab_flash_grey}"
                            app:actionText="@{@string/home_public_tab_flash}"
                            app:actionItemColor="@{actionItemColor}"
                            layout="@layout/single_idcard_action_layout"
                            android:layout_weight="1"/>
                    </LinearLayout>


                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/activity_margin"
                        android:layout_marginHorizontal="@dimen/element_spacing"
                        app:layout_constraintTop_toBottomOf="@id/action_others_data">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/text_about"
                            style="@style/TextAppearance.Flashat.Subtitle1"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@{textAboutDrawBackground}"
                            android:paddingHorizontal="@dimen/element_spacing"
                            android:paddingVertical="@dimen/line_spacing"
                            android:textAllCaps= "@{isPresident ? true : false}"
                            android:text="@string/common_about"
                            android:textColor="@{isPresident ? subTitleColor : idColor}"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:background="@drawable/bg_label_idcard_citizen_ambassador"
                            tools:textColor="@color/white" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/about_text"
                            style="@style/TextAppearance.Flashat.Body2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/element_spacing"
                            android:ellipsize="end"
                            android:maxLines="4"
                            android:text="@{viewModel.aboutUser}"
                            android:textColor="@color/textColorPrimary"
                            app:flow_horizontalAlign="start"
                            app:layout_constrainedWidth="true"
                            app:goneIfNullOrBlank="@{viewModel.aboutUser}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/text_about"
                            app:layout_constraintHorizontal_bias="0.0"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            tools:text="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/img_id_card_edit"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="@dimen/element_spacing"
                            android:src="@drawable/ic_edit"
                            app:goneIfNot="@{viewModel.isCurrentUser()==true}"
                            app:layout_constraintBottom_toBottomOf="@id/text_about"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="@id/text_about"
                            app:tint="@{isPresident ? subTitleColor : idColor}" />

                    </androidx.constraintlayout.widget.ConstraintLayout>


                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.kennyc.view.MultiStateView>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>