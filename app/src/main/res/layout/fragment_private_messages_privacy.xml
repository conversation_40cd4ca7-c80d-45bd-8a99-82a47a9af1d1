<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="com.app.messej.ui.home.settings.privacy.PrivateMessagePrivacyViewModel" />


    </data>   <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true">

        <include
            android:id="@+id/custom_action_bar"
            layout="@layout/item_custom_action_bar" />
        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            app:applySystemBarInsets="@{`bottom`}"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                >

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/txt_private_message_privacy"
                    style="@style/TextAppearance.Flashat.Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/activity_margin"
                    android:layout_marginTop="@dimen/activity_margin"
                    android:text="@string/settings_title_new_message"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
                <TextView
                    android:id="@+id/txt_private_message_privacy_subtitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/title_settings_disable_enable_private_messages"
                    app:layout_constraintStart_toStartOf="@+id/txt_private_message_privacy"
                    app:layout_constraintTop_toBottomOf="@+id/txt_private_message_privacy" />


                <com.google.android.material.materialswitch.MaterialSwitch

                    android:id="@+id/switch_private_message_privacy"
                    style="@style/Widget.Flashat.Switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="@{viewModel.isPrivacyEnabled}"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="@id/txt_private_message_privacy"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/txt_private_message_privacy" />


                <View
                    android:id="@+id/setting_selection_line_view"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="8dp"
                    android:background="@color/textColorBusinessPrimary"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/txt_private_message_privacy_subtitle" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <LinearLayout
                android:id="@+id/layout_private_message_privacy"
                android:orientation="vertical"
                android:visibility="@{viewModel.isPrivacyEnabled?View.VISIBLE:View.GONE}"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/txt_who_can_initiate_continue"
                        style="@style/TextAppearance.Flashat.Subtitle2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/activity_margin"
                        android:layout_marginTop="@dimen/activity_margin"
                       android:text="@string/who_can_send_you_private_message"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />


                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="20dp"
                        android:background="@color/textColorBusinessPrimary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/txt_who_can_initiate_continue" />

                </androidx.constraintlayout.widget.ConstraintLayout>
                <include
                    layout="@layout/item_settings_privacy_selection_customised"
                    android:id="@+id/item_all_continue_users"
                    app:title="@{@string/private_message_privacy_option_one}"
                    android:layout_marginHorizontal="@dimen/element_spacing"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isCheckBoxSelected="@{viewModel.selectedOldPrivateMessage.anyOne}"
                    />
                <include
                    layout="@layout/item_settings_privacy_selection_customised"
                    android:id="@+id/item_only_continue_dears"
                    app:title="@{@string/private_message_privacy_option_three}"
                    android:layout_marginHorizontal="@dimen/element_spacing"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isCheckBoxSelected="@{viewModel.selectedOldPrivateMessage.onlyDears}"
                    />
                <include
                    layout="@layout/item_settings_privacy_selection_customised"
                    android:id="@+id/item_only_continue_dears_and_fans"
                    app:title="@{@string/private_message_privacy_option_dears_and_fans}"
                    android:layout_marginHorizontal="@dimen/element_spacing"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isCheckBoxSelected="@{viewModel.selectedOldPrivateMessage.onlyDearsFans}"
                    />
                <include
                    layout="@layout/item_settings_privacy_selection_customised"
                    android:id="@+id/item_continue_premium_users"
                    app:title="@{@string/private_message_privacy_option_premium_users}"
                    android:layout_marginHorizontal="@dimen/element_spacing"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isCheckBoxSelected="@{viewModel.selectedOldPrivateMessage.onlyPremium}"
                    />
                <include
                    layout="@layout/item_settings_privacy_selection_customised"
                    android:id="@+id/item_continue_premium_users_with_hundred"
                    app:title="@{@string/private_message_privacy_option_premium_users_with_hundred}"
                    android:layout_marginHorizontal="@dimen/element_spacing"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isCheckBoxSelected="@{viewModel.selectedOldPrivateMessage.onlyPremiumWithHundred}"
                    />
                <include
                    layout="@layout/item_settings_privacy_selection_customised"
                    android:id="@+id/item_continue_all_users_with_hundred"
                    app:title="@{@string/private_message_privacy_option_all_users_with_hundred}"
                    android:layout_marginHorizontal="@dimen/element_spacing"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isCheckBoxSelected="@{viewModel.selectedOldPrivateMessage.onlyUsersWithHundred}"
                    />
                <include
                    layout="@layout/item_settings_privacy_selection_customised"
                    android:id="@+id/item_continue_none"
                    app:title="@{@string/private_message_privacy_option_four}"
                    android:layout_marginHorizontal="@dimen/element_spacing"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isCheckBoxSelected="@{viewModel.selectedOldPrivateMessage.noOne}"
                    />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/txt_who_can_initiate_start"
                        style="@style/TextAppearance.Flashat.Subtitle2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/activity_margin"
                        android:layout_marginTop="@dimen/activity_margin"
                        android:text="@string/who_can_send_you_new_private_message"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="20dp"
                        android:background="@color/textColorBusinessPrimary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/txt_who_can_initiate_start" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                    <include
                        layout="@layout/item_settings_privacy_selection_customised"
                        android:id="@+id/item_start_all_users"
                        app:title="@{@string/private_message_privacy_option_one}"
                        android:layout_marginHorizontal="@dimen/element_spacing"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:isCheckBoxSelected="@{viewModel.selectedNewPrivateMessage.anyOne}"
                        />
                    <include
                        layout="@layout/item_settings_privacy_selection_customised"
                        android:id="@+id/item_start_only_dears"
                        app:title="@{@string/private_message_privacy_option_three}"
                        android:layout_marginHorizontal="@dimen/element_spacing"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:isCheckBoxSelected="@{viewModel.selectedNewPrivateMessage.onlyDears}"
                        />
                    <include
                        layout="@layout/item_settings_privacy_selection_customised"
                        android:id="@+id/item_start_only_dears_and_fans"
                        app:title="@{@string/private_message_privacy_option_dears_and_fans}"
                        android:layout_marginHorizontal="@dimen/element_spacing"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:isCheckBoxSelected="@{viewModel.selectedNewPrivateMessage.onlyDearsFans}"
                        />
                    <include
                        layout="@layout/item_settings_privacy_selection_customised"
                        android:id="@+id/item_start_premium_users"
                        app:title="@{@string/private_message_privacy_option_premium_users_new}"
                        android:layout_marginHorizontal="@dimen/element_spacing"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:isCheckBoxSelected="@{viewModel.selectedNewPrivateMessage.onlyPremium}"
                        />
                    <include
                        layout="@layout/item_settings_privacy_selection_customised"
                        android:id="@+id/item_start_premium_users_with_hundred"
                        app:title="@{@string/private_message_privacy_option_premium_users_with_hundred_new}"
                        android:layout_marginHorizontal="@dimen/element_spacing"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:isCheckBoxSelected="@{viewModel.selectedNewPrivateMessage.onlyPremiumWithHundred}"
                        />
                    <include
                        layout="@layout/item_settings_privacy_selection_customised"
                        android:id="@+id/item_start_all_users_with_hundred"
                        app:title="@{@string/private_message_privacy_option_all_users_with_hundred}"
                        android:layout_marginHorizontal="@dimen/element_spacing"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:isCheckBoxSelected="@{viewModel.selectedNewPrivateMessage.onlyUsersWithHundred}"
                        />
                    <include
                        layout="@layout/item_settings_privacy_selection_customised"
                        android:id="@+id/item_start_none"
                        app:title="@{@string/private_message_privacy_option_four}"
                        android:layout_marginHorizontal="@dimen/element_spacing"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:isCheckBoxSelected="@{viewModel.selectedNewPrivateMessage.noOne}"
                        />

            </LinearLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>

