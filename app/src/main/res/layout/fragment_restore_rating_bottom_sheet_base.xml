<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="com.app.messej.ui.home.businesstab.BusinessDealsListViewModel" />

        <import type="com.app.messej.data.model.enums.RestoreType" />

        <variable
            name="isResident"
            type="Boolean" />
    </data>


    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <include
            android:id="@+id/custom_action_bar"
            layout="@layout/item_custom_action_bar_rating" />

        <androidx.core.widget.NestedScrollView
            android:id="@+id/nestedScrollView3"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="@dimen/double_margin"
            app:layout_anchor="@+id/nestedScrollView3"
            app:layout_anchorGravity="center">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/title_restore_rating"
                style="@style/TextAppearance.Flashat.Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/double_margin"
                android:layout_marginTop="@dimen/element_spacing"
                android:text="@string/select_one_restore_rating"
                android:textColor="@color/textColorPrimary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <RadioGroup
                android:id="@+id/radio_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/double_margin"
                android:layout_marginTop="@dimen/activity_margin"
                android:orientation="vertical"
                app:layout_constraintTop_toBottomOf="@id/title_restore_rating">

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:checked="@{viewModel.selectedRestoreRating==RestoreType.FOR_ME}"
                    android:onClick="@{() -> viewModel.setRestoreType(RestoreType.FOR_ME)}"
                    android:text="@string/account_management_restore_your_rating"
                    android:enabled="@{!viewModel.isFlaxRateFull}"
                    android:textAppearance="@style/TextAppearance.Flashat.Body2"
                    android:textColor="@color/textColorPrimary"
                    tools:checked="true" />

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:checked="@{viewModel.selectedRestoreRating==RestoreType.FOR_OTHER}"
                    android:onClick="@{() -> viewModel.setRestoreType(RestoreType.FOR_OTHER)}"
                    android:text="@string/restore_your_friend_s_rating"
                    android:textAppearance="@style/TextAppearance.Flashat.Body2"
                    android:textColor="@color/textColorPrimary" />
            </RadioGroup>


            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/text_search_beneficiary"
                style="@style/Widget.Flashat.GreyTextInput.ExposedDropdownMenu"
                goneIf="@{viewModel.selectedRestoreRating==RestoreType.FOR_ME}"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/activity_margin"
                android:layout_marginTop="@dimen/element_spacing"
                android:autofillHints="name"
                android:importantForAutofill="yes"
                app:hintAnimationEnabled="false"
                app:hintEnabled="false"
                app:layout_constraintTop_toBottomOf="@id/radio_group"
                tools:visibility="gone">

                <AutoCompleteTextView
                    android:id="@+id/autoCompleteTextView"
                    style="@style/Widget.Flashat.GreyTextInput"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:hint="@string/file_a_case_enter_a_user_name"
                    android:inputType="text"
                    android:paddingHorizontal="10dp"
                    android:text=""
                    android:textColor="@color/textColorSecondary" />
            </com.google.android.material.textfield.TextInputLayout>

            <androidx.cardview.widget.CardView
                android:id="@+id/selected_beneficiary"
                goneIfNull="@{viewModel.beneficiary}"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:layout_marginVertical="@dimen/extra_margin"
                app:cardBackgroundColor="@color/colorSurfaceSecondary"
                app:cardCornerRadius="@dimen/extra_margin"
                app:layout_constraintTop_toBottomOf="@id/text_search_beneficiary"
                tools:visibility="gone">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="10dp"
                    android:layout_marginVertical="10dp">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/badge_premium"
                        goneIfNot="@{viewModel.beneficiary.premiumUser}"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:elevation="10dp"
                        android:src="@drawable/ic_user_badge_premium"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.makeramen.roundedimageview.RoundedImageView
                        android:id="@+id/image_person_photo"
                        android:layout_width="35dp"
                        android:layout_height="35dp"
                        android:layout_marginHorizontal="4dp"
                        android:layout_marginVertical="4dp"
                        android:elevation="2dp"
                        android:scaleType="centerCrop"
                        app:imageUrl="@{viewModel.beneficiary.thumbnail}"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/badge_premium"
                        app:layout_constraintTop_toTopOf="parent"
                        app:riv_corner_radius="@dimen/drawer_header_dp_size"
                        app:placeholder="@{@drawable/im_user_placeholder_opaque}"
                        tools:src="@drawable/im_user_placeholder_opaque"  />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/layout_name"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/element_spacing"
                        app:layout_constraintBottom_toBottomOf="@id/image_person_photo"
                        app:layout_constraintEnd_toStartOf="@id/ic_close"
                        app:layout_constraintStart_toEndOf="@id/image_person_photo"
                        app:layout_constraintTop_toTopOf="@id/image_person_photo">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/textView_person_name"
                            style="@style/TextAppearance.Flashat.Subtitle2"
                            goneIfNull="@{viewModel.beneficiary.name"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="10dp"
                            android:text="@{viewModel.beneficiary.name}"
                            android:textColor="@color/textColorPrimary"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="@string/common_name" />


                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/textView_person_userName"
                            style="@style/TextAppearance.Flashat.Label.Small"
                            goneIfNull="@{viewModel.beneficiary.username}"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="10dp"
                            android:text="@{viewModel.beneficiary.username}"
                            android:textColor="@color/textColorPrimary"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/textView_person_name"
                            tools:text="@string/common_name" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ic_close"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/element_spacing"
                        android:src="@drawable/ic_close"
                        android:tint="@color/colorBusinessGrey"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>


            <View
                android:id="@+id/selection_divider"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginVertical="@dimen/extra_margin"
                android:background="@drawable/bg_business_horizontal_dotted_line"
                android:backgroundTint="@{isResident?@color/black:@color/white}"
                app:layout_constraintEnd_toEndOf="@id/text_search_beneficiary"
                app:layout_constraintStart_toStartOf="@id/text_search_beneficiary"
                app:layout_constraintTop_toBottomOf="@id/selected_beneficiary"
                tools:backgroundTint="@color/colorBusinessGrey" />




            <androidx.cardview.widget.CardView
                android:id="@+id/cardView_rating"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                goneIfNot="@{viewModel.shouldShowRatingCard}"
                android:layout_marginHorizontal="@dimen/activity_margin"
                android:layout_marginTop="@dimen/activity_margin"
                app:cardBackgroundColor="@{isResident?@color/colorBusinessGrey:@color/colorPrimary}"
                app:cardCornerRadius="10dp"
                app:cardElevation="5dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/selection_divider">

                <include
                    android:id="@+id/layout_list_loading_header"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    layout="@layout/layout_restore_rating_loading"
                    app:actionLoading="@{viewModel.getRestoreRatingShimmerLoading}" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    goneIf="@{!viewModel.getRestoreRatingShimmerLoading}"
                    android:background="@{isResident?@color/colorSurfaceSecondaryDarker:@color/colorPrimary}"
                    android:padding="@dimen/activity_margin">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/text_your_rating_title"
                        style="@style/TextAppearance.Flashat.Body2"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="@string/restore_rating_your_rating_title"
                        android:textColor="@{isResident?@color/black:@color/white}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/text_your_rating"
                        style="@style/TextAppearance.Flashat.Body2"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="@{viewModel.restoreRatingDetails==null?@string/id_card_visitor_rating:viewModel.restoreRatingDetails.convertedRating}"
                        android:textAlignment="textEnd"
                        android:textColor="@{isResident?@color/black:@color/white}"
                        app:layout_constraintBottom_toBottomOf="@id/text_your_rating_title"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/text_your_rating_title"
                        app:layout_constraintTop_toTopOf="@id/text_your_rating_title"
                        tools:text="90%"
                        tools:textColor="@color/black" />

                    <View
                        android:id="@+id/balance_bottom_divider_rating"
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_marginVertical="@dimen/line_spacing"
                        android:background="@drawable/bg_business_horizontal_dotted_line"
                        android:backgroundTint="@{isResident?@color/black:@color/white}"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/text_your_rating_title"
                        tools:backgroundTint="@color/colorBusinessGrey" />


                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/text_restore_rating_title"
                        style="@style/TextAppearance.Flashat.Body2"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/element_spacing"
                        android:text="@string/restore_rating_cost_to_restore_rating_title"
                        android:textColor="@{isResident?@color/black:@color/white}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/balance_bottom_divider_rating"
                        tools:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/text_restore_rating"
                        style="@style/TextAppearance.Flashat.Body2"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:textAlignment="textEnd"
                        android:textColor="@{isResident?@color/black:@color/white}"
                        app:flaxColor="@{isResident?@color/colorPrimary:@color/colorSecondary}"
                        app:flaxValue="@{viewModel.restoreRatingDetails.restoratingFlix}"
                        app:layout_constraintBottom_toBottomOf="@id/text_restore_rating_title"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/text_restore_rating_title"
                        app:layout_constraintTop_toTopOf="@id/text_restore_rating_title"
                        tools:text="FLiX 1000"
                        tools:textColor="@color/black" />


                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/cardView_main"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/activity_margin"
                android:layout_marginTop="@dimen/activity_margin"
                app:cardBackgroundColor="@{isResident?@color/colorBusinessGrey:@color/colorPrimary}"
                app:cardCornerRadius="10dp"
                app:cardElevation="5dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cardView_rating">

                <include
                    android:id="@+id/layout_list_loading"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    layout="@layout/layout_restore_rating_loading"
                    app:actionLoading="@{viewModel.getRestoreRatingShimmerLoading}"/>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    goneIf="@{!viewModel.getRestoreRatingShimmerLoading}"
                    android:background="@{isResident?@color/colorSurfaceSecondaryDarker:@color/colorPrimary}"
                    android:padding="@dimen/activity_margin">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/text_flix_balance_title"
                        style="@style/TextAppearance.Flashat.Body2"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="@string/restore_rating_your_flix_balance_title"
                        android:textColor="@{isResident?@color/black:@color/white}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/text_flix_balance"
                        style="@style/TextAppearance.Flashat.Body2"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:textAlignment="textEnd"
                        android:textColor="@{isResident?@color/black:@color/white}"
                        app:flaxColor="@{isResident?@color/colorPrimary:@color/colorSecondary}"
                        app:flaxValue="@{viewModel.restoreRatingDetails.flixBalance}"
                        app:layout_constraintBottom_toBottomOf="@id/text_flix_balance_title"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/text_flix_balance_title"
                        app:layout_constraintTop_toTopOf="@id/text_flix_balance_title"
                        tools:text="FLiX 100"
                        tools:textColor="@color/black" />

                    <View
                        android:id="@+id/balance_bottom_divider_flix"
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_marginVertical="@dimen/line_spacing"
                        android:background="@drawable/bg_business_horizontal_dotted_line"
                        android:backgroundTint="@{isResident?@color/black:@color/white}"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/text_flix_balance_title"
                        tools:backgroundTint="@color/colorBusinessGrey" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/text_coin_balance_title"
                        style="@style/TextAppearance.Flashat.Body2"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/element_spacing"
                        android:text="@string/restore_rating_your_coins_balance_title"
                        android:textColor="@{isResident?@color/black:@color/white}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/balance_bottom_divider_flix"
                        tools:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/text_coin_balance"
                        style="@style/TextAppearance.Flashat.Body2"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="@{viewModel.restoreRatingDetails==null?@string/id_card_visitor_rating:@string/restore_rating_coins(viewModel.restoreRatingDetails.coinBalance.toString())}"
                        android:textAlignment="textEnd"
                        android:textColor="@{isResident?@color/black:@color/white}"
                        app:layout_constraintBottom_toBottomOf="@id/text_coin_balance_title"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/text_coin_balance_title"
                        app:layout_constraintTop_toTopOf="@id/text_coin_balance_title"
                        tools:text="COiNS  20"
                        tools:textColor="@color/black" />

                    <View
                        android:id="@+id/balance_bottom_divider_coin"
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_marginVertical="@dimen/line_spacing"
                        android:background="@drawable/bg_business_horizontal_dotted_line"
                        android:backgroundTint="@{isResident?@color/black:@color/white}"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/text_coin_balance_title"
                        tools:backgroundTint="@color/colorBusinessGrey" />


                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/text_effective_flix_title"
                        style="@style/TextAppearance.Flashat.Body2"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/element_spacing"
                        android:text="@string/restore_rating_effective_flix_title"
                        android:textColor="@{isResident?@color/black:@color/white}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/balance_bottom_divider_coin"
                        tools:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/text_effective_flix_balance"
                        style="@style/TextAppearance.Flashat.Body2"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:textAlignment="textEnd"
                        android:textColor="@{isResident?@color/black:@color/white}"
                        app:flaxColor="@{isResident?@color/colorPrimary:@color/colorSecondary}"
                        app:flaxValue="@{viewModel.restoreRatingDetails.effectiveBalance}"
                        app:layout_constraintBottom_toBottomOf="@id/text_effective_flix_title"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/text_effective_flix_title"
                        app:layout_constraintTop_toTopOf="@id/text_effective_flix_title"
                        tools:text="FLiX 200"
                        tools:textColor="@color/black" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_note"
                style="@style/TextAppearance.Flashat.Label.Small"
                goneIf="@{!viewModel.getRestoreRatingShimmerLoading}"
                goneIfNot="@{!viewModel.restoreRatingDetails.enableRestoreButton &amp;&amp; viewModel.restoreRatingDetails!=null}"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/element_spacing"
                android:text="@string/restore_rating_inSufficient_balance"
                android:textColor="@color/colorError"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/cardView_main"
                app:layout_constraintStart_toStartOf="@id/cardView_main"
                app:layout_constraintTop_toBottomOf="@id/cardView_main" />


            <Button
                android:id="@+id/restore_rating_button"
                style="@style/Widget.Flashat.LargeRoundedButton"
                goneIf="@{!viewModel.getRestoreRatingShimmerLoading}"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/double_margin"
                android:layout_marginVertical="@dimen/double_margin"
                android:enabled="@{viewModel.restoreRatingDetails.enableRestoreButton&amp;&amp;viewModel.shouldShowRatingCard}"
                android:text="@string/common_accept"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/text_note" />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/restore_rating_history"
                style="@style/TextAppearance.Flashat.Body1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/double_margin"
                android:layout_marginVertical="@dimen/extra_margin"
                android:text="@string/history_restore_rating"
                android:textColor="@color/colorPrimary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/restore_rating_button"
                app:layout_constraintBottom_toBottomOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>