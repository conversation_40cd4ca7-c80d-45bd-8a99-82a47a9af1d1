<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="headerText"
            type="String" />
        <variable
            name="contentText"
            type="String" />
        <variable
            name="textColor"
            type="Integer" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/line_spacing">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guide_start"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/header"
            style="@style/TextAppearance.Flashat.Label.Smaller"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@{headerText}"
            android:textColor="@color/textColorPrimary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/guide_start"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Gift :" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/text_title_other"
            style="@style/TextAppearance.Flashat.Label.Smaller.Bold"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@{contentText}"
            android:textColor="@{textColor}"
            app:layout_constraintStart_toStartOf="@id/guide_start"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:gravity="end"
            tools:text="00"
            tools:textColor="@color/colorPrimary" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>