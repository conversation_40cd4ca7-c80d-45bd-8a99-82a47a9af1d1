<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.app.messej.data.model.enums.TheaterAudienceFee" />
        <import type="com.app.messej.data.model.enums.PodiumKind" />
        <import type="com.app.messej.data.model.enums.TheaterStageFee" />
        <import type="com.app.messej.data.model.enums.PodiumWhoCanJoin" />
        <import type="com.app.messej.data.model.enums.SpeakingJoiningFee" />

        <variable
            name="viewModel"
            type="com.app.messej.ui.home.publictab.podiums.create.CreatePodiumViewModel" />

    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:liftOnScroll="true"
            android:fitsSystemWindows="true"
            tools:showIn="@layout/fragment_create_podium"
            tools:visibility="visible">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/collapsing_toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?toolbarColor"
                android:fitsSystemWindows="true"
                app:contentScrim="@color/transparent"
                app:layout_scrollFlags="noScroll"
                app:statusBarScrim="@color/transparent"
                app:titleEnabled="false">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:fitsSystemWindows="true"
                    app:layout_collapseMode="parallax"
                    app:layout_collapseParallaxMultiplier="1.0">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/customActionBarHeight"
                        android:layout_marginTop="@dimen/element_spacing"
                        android:layout_marginBottom="-8dp"
                        android:adjustViewBounds="true"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/bg_toolbar_circle_expanded_left" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/customActionBarHeight"
                        android:adjustViewBounds="true"
                        android:paddingBottom="@dimen/element_spacing"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/bg_toolbar_circle_top_right" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.google.android.material.appbar.MaterialToolbar
                    android:id="@+id/toolbar"
                    style="@style/Widget.Flashat.Toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/customActionBarHeight"
                    android:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:title="@string/podium_title_advanced_settings"
                    tools:title="Page Title" />

            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:applySystemBarInsets="@{`ime|bottom`}"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <com.kennyc.view.MultiStateView
                android:id="@+id/multiStateView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:msv_loadingView="@layout/layout_create_huddle_eds_loading"
                app:msv_viewState="content">

                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/label_who_can_join"
                            style="@style/TextAppearance.Flashat.Label"
                            android:layout_width="match_parent"
                            goneIfNot="@{viewModel.whoCanJoinVisible}"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/double_margin"
                            android:layout_marginTop="@dimen/activity_margin"
                            android:text="@string/podium_who_can_join"
                            android:textColor="@color/textColorSecondary"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHorizontal_bias="0.5"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <com.google.android.flexbox.FlexboxLayout
                            android:id="@+id/radioWhoCanJoin"
                            android:layout_width="0dp"
                            goneIfNot="@{viewModel.whoCanJoinVisible}"
                            app:flexWrap="wrap"
                            android:layout_height="wrap_content"
                            app:justifyContent="space_between"
                            android:layout_marginHorizontal="@dimen/double_margin"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/label_who_can_join">

                            <androidx.appcompat.widget.AppCompatRadioButton
                                android:id="@+id/radioBtnAnyOne"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                app:layout_flexBasisPercent="45%"
                                android:checked="@{viewModel.whoCanJoinType==PodiumWhoCanJoin.ANY_ONE_CAN}"
                                android:onClick="@{() -> viewModel.setWhoCanJoin(PodiumWhoCanJoin.ANY_ONE_CAN)}"
                                android:text="@string/podium_join_with_rating_anyone"
                                android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                            <androidx.appcompat.widget.AppCompatRadioButton
                                android:id="@+id/radioAboveOrEqualNinety"
                                android:layout_width="0dp"
                                app:layout_flexBasisPercent="45%"
                                android:layout_height="wrap_content"
                                android:checked="@{viewModel.whoCanJoinType==PodiumWhoCanJoin.RATING_ABOVE_NINETY}"
                                android:onClick="@{() -> viewModel.setWhoCanJoin(PodiumWhoCanJoin.RATING_ABOVE_NINETY)}"
                                android:text="@string/podium_join_rating_above_ninety"
                                android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                            <androidx.appcompat.widget.AppCompatRadioButton
                                android:id="@+id/radioBtnOnlyHundred"
                                android:layout_width="0dp"
                                app:layout_flexBasisPercent="100%"
                                android:layout_height="wrap_content"
                                android:checked="@{viewModel.whoCanJoinType==PodiumWhoCanJoin.RATING_ONLY_HUNDRED}"
                                android:onClick="@{() -> viewModel.setWhoCanJoin(PodiumWhoCanJoin.RATING_ONLY_HUNDRED)}"
                                android:text="@string/podium_join_only_hundred"
                                android:textAppearance="@style/TextAppearance.Flashat.Body2" />
                        </com.google.android.flexbox.FlexboxLayout>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/label_who_can_comment"
                            style="@style/TextAppearance.Flashat.Label"
                            goneIfNot="@{viewModel.canCommentViewVisible}"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/double_margin"
                            android:layout_marginTop="@dimen/activity_margin"
                            android:text="@string/podium_who_can_comment"
                            android:textColor="@color/textColorSecondary"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHorizontal_bias="0.5"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/radioWhoCanJoin" />

                        <com.google.android.flexbox.FlexboxLayout
                            android:id="@+id/flex_layout_comment"
                            goneIfNot="@{viewModel.canCommentViewVisible}"
                            android:layout_width="0dp"
                            app:flexWrap="wrap"
                            android:layout_height="wrap_content"
                            app:justifyContent="space_between"
                            android:layout_marginTop="@dimen/element_spacing"
                            android:layout_marginHorizontal="@dimen/double_margin"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/label_who_can_comment">

                            <androidx.appcompat.widget.AppCompatRadioButton
                                android:id="@+id/radioBtnCommentAnyOne"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                app:layout_flexBasisPercent="45%"
                                android:checked="@{viewModel.whoCanComment==PodiumWhoCanJoin.ANY_ONE_CAN}"
                                android:onClick="@{() -> viewModel.setWhoCanComment(PodiumWhoCanJoin.ANY_ONE_CAN)}"
                                android:text="@string/podium_join_with_rating_anyone"
                                android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                            <androidx.appcompat.widget.AppCompatRadioButton
                                android:id="@+id/radioCommentAboveOrEqualNinety"
                                android:layout_width="0dp"
                                app:layout_flexBasisPercent="45%"
                                android:layout_height="wrap_content"
                                android:checked="@{viewModel.whoCanComment==PodiumWhoCanJoin.RATING_ABOVE_NINETY}"
                                android:onClick="@{() -> viewModel.setWhoCanComment(PodiumWhoCanJoin.RATING_ABOVE_NINETY)}"
                                android:text="@string/podium_join_rating_above_ninety"
                                android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                            <androidx.appcompat.widget.AppCompatRadioButton
                                android:id="@+id/radioCommentBtnOnlyHundred"
                                android:layout_width="0dp"
                                app:layout_flexBasisPercent="100%"
                                android:layout_height="wrap_content"
                                android:checked="@{viewModel.whoCanComment==PodiumWhoCanJoin.RATING_ONLY_HUNDRED}"
                                android:onClick="@{() -> viewModel.setWhoCanComment(PodiumWhoCanJoin.RATING_ONLY_HUNDRED)}"
                                android:text="@string/podium_join_only_hundred"
                                android:textAppearance="@style/TextAppearance.Flashat.Body2" />
                        </com.google.android.flexbox.FlexboxLayout>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/label_who_can_speak"
                            style="@style/TextAppearance.Flashat.Label"
                            goneIfNot="@{viewModel.canSpeakViewVisible}"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/double_margin"
                            android:layout_marginTop="@dimen/activity_margin"
                            android:text="@string/podium_who_can_speak"
                            android:textColor="@color/textColorSecondary"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHorizontal_bias="0.5"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/flex_layout_comment" />

                        <com.google.android.flexbox.FlexboxLayout
                            android:id="@+id/flex_layout_speak"
                            goneIfNot="@{viewModel.canSpeakViewVisible}"
                            android:layout_width="0dp"
                            app:flexWrap="wrap"
                            android:layout_height="wrap_content"
                            app:justifyContent="space_between"
                            android:layout_marginTop="@dimen/element_spacing"
                            android:layout_marginHorizontal="@dimen/double_margin"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/label_who_can_speak">

                            <androidx.appcompat.widget.AppCompatRadioButton
                                android:id="@+id/radioBtnSpeakAnyOne"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                app:layout_flexBasisPercent="45%"
                                android:checked="@{viewModel.whoCanSpeak==PodiumWhoCanJoin.ANY_ONE_CAN}"
                                android:onClick="@{() -> viewModel.setWhoCanSpeak(PodiumWhoCanJoin.ANY_ONE_CAN)}"
                                android:text="@string/podium_join_with_rating_anyone"
                                android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                            <androidx.appcompat.widget.AppCompatRadioButton
                                android:id="@+id/radioSpeakAboveOrEqualNinety"
                                android:layout_width="0dp"
                                app:layout_flexBasisPercent="45%"
                                android:layout_height="wrap_content"
                                android:checked="@{viewModel.whoCanSpeak==PodiumWhoCanJoin.RATING_ABOVE_NINETY}"
                                android:onClick="@{() -> viewModel.setWhoCanSpeak(PodiumWhoCanJoin.RATING_ABOVE_NINETY)}"
                                android:text="@string/podium_join_rating_above_ninety"
                                android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                            <androidx.appcompat.widget.AppCompatRadioButton
                                android:id="@+id/radioSpeakBtnOnlyHundred"
                                android:layout_width="0dp"
                                app:layout_flexBasisPercent="100%"
                                android:layout_height="wrap_content"
                                android:checked="@{viewModel.whoCanSpeak==PodiumWhoCanJoin.RATING_ONLY_HUNDRED}"
                                android:onClick="@{() -> viewModel.setWhoCanSpeak(PodiumWhoCanJoin.RATING_ONLY_HUNDRED)}"
                                android:text="@string/podium_join_only_hundred"
                                android:textAppearance="@style/TextAppearance.Flashat.Body2" />
                        </com.google.android.flexbox.FlexboxLayout>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/label_speaking_fee"
                            style="@style/TextAppearance.Flashat.Label"
                            goneIfNot="@{viewModel.speakingFeeVisible}"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/double_margin"
                            android:layout_marginTop="@dimen/activity_margin"
                            android:text="@string/podium_speaking_fee"
                            android:textColor="@color/textColorSecondary"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHorizontal_bias="0.5"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/flex_layout_speak" />

                        <com.google.android.flexbox.FlexboxLayout
                            android:id="@+id/flex_layout_speaking_fee"
                            goneIfNot="@{viewModel.speakingFeeVisible}"
                            android:layout_width="0dp"
                            app:flexWrap="wrap"
                            app:justifyContent="space_between"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/element_spacing"
                            android:layout_marginHorizontal="@dimen/double_margin"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/label_speaking_fee">

                            <androidx.appcompat.widget.AppCompatRadioButton
                                android:id="@+id/radioBtnSpeakFree"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                app:layout_flexBasisPercent="45%"
                                android:checked="@{viewModel.speakingFee == SpeakingJoiningFee.FREE}"
                                android:onClick="@{() -> viewModel.setSpeakingFee(SpeakingJoiningFee.FREE)}"
                                android:text="@string/podium_challenge_free"
                                android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                            <androidx.appcompat.widget.AppCompatRadioButton
                                android:id="@+id/radioSpeakWithCoins"
                                android:layout_width="0dp"
                                app:layout_flexBasisPercent="45%"
                                android:layout_height="wrap_content"
                                android:checked="@{viewModel.speakingFee == SpeakingJoiningFee.CUSTOM}"
                                android:onClick="@{() -> viewModel.setSpeakingFee(SpeakingJoiningFee.CUSTOM)}"
                                android:text="@string/podium_theater_fee_hint"
                                android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                            <com.google.android.material.textfield.TextInputLayout
                                android:id="@+id/textInputSpeakFee"
                                style="@style/Widget.Flashat.GreyTextInput"
                                goneIfNot="@{viewModel.speakingFee == SpeakingJoiningFee.CUSTOM}"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:importantForAutofill="yes"
                                app:hintAnimationEnabled="false"
                                app:hintEnabled="false"
                                app:layout_constraintTop_toBottomOf="@id/radioGroupStageFee">

                                <com.google.android.material.textfield.TextInputEditText
                                    style="@style/Widget.Flashat.GreyTextInput"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:hint="@string/podium_enter_fee_hint"
                                    android:inputType="number"
                                    android:maxLength="10"
                                    android:text="@={viewModel.speakingFeeString}" />

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/tvSpeakFeeErrorText"
                                    style="@style/TextAppearance.Flashat.Label"
                                    android:layout_width="wrap_content"
                                    android:layout_marginTop="@dimen/line_spacing"
                                    android:layout_height="wrap_content"
                                    tools:text="Please enter amount greater than 10"
                                    android:text="@{@string/podium_fee_error_text(SpeakingJoiningFee.MINIMUM_CUSTOM_FEE)}"
                                    android:textColor="@color/red"
                                    app:invisibleIf="@{viewModel.isSpeakingFeeValid || viewModel.isSpeakingFeeEmpty}" />
                            </com.google.android.material.textfield.TextInputLayout>

                        </com.google.android.flexbox.FlexboxLayout>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/label_fees_for_audience"
                            style="@style/TextAppearance.Flashat.Label"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            goneIfNot="@{viewModel.kind == PodiumKind.THEATER}"
                            android:layout_marginHorizontal="@dimen/double_margin"
                            android:layout_marginTop="@dimen/double_margin"
                            android:text="@string/podium_theater_fees_for_audience"
                            android:textColor="@color/textColorSecondary"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHorizontal_bias="0.5"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/flex_layout_speaking_fee" />

                        <com.google.android.flexbox.FlexboxLayout
                            android:id="@+id/radioAudienceFee"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            goneIfNot="@{viewModel.kind == PodiumKind.THEATER}"
                            android:layout_marginHorizontal="@dimen/double_margin"
                            app:flexWrap="wrap"
                            app:justifyContent="space_between"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/label_fees_for_audience">

                            <androidx.appcompat.widget.AppCompatRadioButton
                                android:id="@+id/radioBtnFree"
                                android:layout_width="0dp"
                                app:layout_flexBasisPercent="45%"
                                android:layout_height="wrap_content"
                                android:checked="@{viewModel.audienceFeeType==TheaterAudienceFee.FREE}"
                                android:onClick="@{() -> viewModel.setAudienceFee(TheaterAudienceFee.FREE)}"
                                android:text="@string/podium_challenge_free"
                                android:textAppearance="@style/TextAppearance.Flashat.Body2" />

<!--                            <androidx.appcompat.widget.AppCompatRadioButton-->
<!--                                android:id="@+id/radioBtnFiveCoins"-->
<!--                                android:layout_width="match_parent"-->
<!--                                android:layout_height="wrap_content"-->
<!--                                android:layout_marginStart="@dimen/activity_margin"-->
<!--                                android:checked="@{viewModel.audienceFeeType==TheaterAudienceFee.COINS_FIVE}"-->
<!--                                android:onClick="@{() -> viewModel.setAudienceFee(TheaterAudienceFee.COINS_FIVE)}"-->
<!--                                android:paddingStart="@dimen/line_spacing"-->
<!--                                android:paddingEnd="@dimen/activity_margin"-->
<!--                                android:text="@{@string/common_coins(TheaterAudienceFee.COINS_FIVE.amount)}"-->
<!--                                android:textAppearance="@style/TextAppearance.Flashat.Body2" />-->

<!--                            <androidx.appcompat.widget.AppCompatRadioButton-->
<!--                                android:id="@+id/radioBtnTenCoins"-->
<!--                                android:layout_width="match_parent"-->
<!--                                android:layout_height="wrap_content"-->
<!--                                android:layout_marginStart="@dimen/activity_margin"-->
<!--                                android:checked="@{viewModel.audienceFeeType==TheaterAudienceFee.COINS_TEN}"-->
<!--                                android:onClick="@{() -> viewModel.setAudienceFee(TheaterAudienceFee.COINS_TEN)}"-->
<!--                                android:paddingStart="@dimen/line_spacing"-->
<!--                                android:paddingEnd="@dimen/activity_margin"-->
<!--                                android:text="@{@string/common_coins(TheaterAudienceFee.COINS_TEN.amount)}"-->
<!--                                android:textAppearance="@style/TextAppearance.Flashat.Body2" />-->

                            <androidx.appcompat.widget.AppCompatRadioButton
                                android:id="@+id/radioBtnCustomAudienceFee"
                                android:layout_width="0dp"
                                app:layout_flexBasisPercent="45%"
                                android:layout_height="wrap_content"
                                android:checked="@{viewModel.AudienceFeeType==TheaterAudienceFee.CUSTOM}"
                                android:onClick="@{() -> viewModel.setAudienceFee(TheaterAudienceFee.CUSTOM)}"
                                android:text="@string/podium_theater_fee_hint"
                                android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                            <com.google.android.material.textfield.TextInputLayout
                                style="@style/Widget.Flashat.GreyTextInput"
                                goneIfNot="@{viewModel.AudienceFeeType==TheaterAudienceFee.CUSTOM}"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:importantForAutofill="yes"
                                app:hintAnimationEnabled="false"
                                app:hintEnabled="false">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/textInputAudienceFee"
                                    style="@style/Widget.Flashat.GreyTextInput"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:hint="@string/podium_enter_fee_hint"
                                    android:inputType="number"
                                    android:maxLength="10"
                                    android:text="@={viewModel.audienceFeeString}" />

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/tvAudienceFeeErrorText"
                                    style="@style/TextAppearance.Flashat.Label"
                                    goneIf="@{viewModel.isAudienceFeeValid || viewModel.isAudienceFeeEmpty}"
                                    android:layout_marginTop="@dimen/element_spacing"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{@string/podium_fee_error_text(TheaterAudienceFee.MINIMUM_CUSTOM_FEE)}"
                                    android:textColor="@color/red"
                                    tools:text="@string/podium_fee_error_text" />
                            </com.google.android.material.textfield.TextInputLayout>
                        </com.google.android.flexbox.FlexboxLayout>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/label_stage_fee"
                            style="@style/TextAppearance.Flashat.Label"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            goneIfNot="@{viewModel.kind == PodiumKind.THEATER}"
                            android:layout_marginHorizontal="@dimen/double_margin"
                            android:layout_marginTop="@dimen/activity_margin"
                            android:text="@string/podium_theater_stage_fee"
                            android:textColor="@color/textColorSecondary"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHorizontal_bias="0.5"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/radioAudienceFee" />

                        <com.google.android.flexbox.FlexboxLayout
                            android:id="@+id/radioGroupStageFee"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            goneIfNot="@{viewModel.kind == PodiumKind.THEATER}"
                            app:flexWrap="wrap"
                            app:justifyContent="space_between"
                            android:layout_marginHorizontal="@dimen/double_margin"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/label_stage_fee">

                            <androidx.appcompat.widget.AppCompatRadioButton
                                android:id="@+id/radioBtnHundredCoins"
                                android:layout_width="0dp"
                                app:layout_flexBasisPercent="45%"
                                android:layout_height="wrap_content"
                                android:checked="@{viewModel.stageFeeType==TheaterStageFee.COINS_ZERO}"
                                android:onClick="@{() -> viewModel.setStageFee(TheaterStageFee.COINS_ZERO)}"
                                android:text="@string/podium_challenge_free"
                                android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                            <androidx.appcompat.widget.AppCompatRadioButton
                                android:id="@+id/radioBtnCustomStageFee"
                                android:layout_width="0dp"
                                app:layout_flexBasisPercent="45%"
                                android:layout_height="wrap_content"
                                android:checked="@{viewModel.stageFeeType==TheaterStageFee.CUSTOM}"
                                android:onClick="@{() -> viewModel.setStageFee(TheaterStageFee.CUSTOM)}"
                                android:text="@string/podium_theater_fee_hint"
                                android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                            <com.google.android.material.textfield.TextInputLayout
                                android:id="@+id/textInputStageFee"
                                style="@style/Widget.Flashat.GreyTextInput"
                                goneIfNot="@{viewModel.stageFeeType==TheaterStageFee.CUSTOM}"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:importantForAutofill="yes"
                                app:hintAnimationEnabled="false"
                                app:hintEnabled="false">

                                <com.google.android.material.textfield.TextInputEditText
                                    style="@style/Widget.Flashat.GreyTextInput"
                                    goneIfNot="@{radioBtnCustomStageFee.checked}"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:hint="@string/podium_enter_fee_hint"
                                    android:inputType="number"
                                    android:maxLength="10"
                                    android:text="@={viewModel.stageFeeString}" />

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/tvStageFeeErrorText"
                                    style="@style/TextAppearance.Flashat.Label"
                                    goneIf="@{viewModel.isStageFeeValid || viewModel.isStageFeeEmpty}"
                                    android:layout_marginTop="@dimen/element_spacing"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{@string/podium_fee_error_text(TheaterStageFee.MINIMUM_CUSTOM_FEE)}"
                                    android:textColor="@color/red"
                                    android:visibility="gone"
                                    tools:visibility="visible"
                                    tools:text="@string/podium_fee_error_text" />
                            </com.google.android.material.textfield.TextInputLayout>

                        </com.google.android.flexbox.FlexboxLayout>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/label_joining_fee"
                            style="@style/TextAppearance.Flashat.Label"
                            goneIfNot="@{viewModel.joiningFeeVisible}"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/double_margin"
                            android:layout_marginTop="@dimen/activity_margin"
                            android:text="@string/podium_joining_fee"
                            android:textColor="@color/textColorSecondary"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHorizontal_bias="0.5"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/radioGroupStageFee" />

                        <com.google.android.flexbox.FlexboxLayout
                            android:id="@+id/flex_layout_joining_fee"
                            goneIfNot="@{viewModel.joiningFeeVisible}"
                            android:layout_width="0dp"
                            app:flexWrap="wrap"
                            android:layout_height="wrap_content"
                            app:justifyContent="space_between"
                            android:layout_marginTop="@dimen/element_spacing"
                            android:layout_marginHorizontal="@dimen/double_margin"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/label_joining_fee">

                            <androidx.appcompat.widget.AppCompatRadioButton
                                android:id="@+id/radioBtnJoinFree"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                app:layout_flexBasisPercent="45%"
                                android:checked="@{viewModel.joiningFee == SpeakingJoiningFee.FREE}"
                                android:onClick="@{() -> viewModel.setJoiningFee(SpeakingJoiningFee.FREE)}"
                                android:text="@string/podium_challenge_free"
                                android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                            <androidx.appcompat.widget.AppCompatRadioButton
                                android:id="@+id/radioJoinWithCoins"
                                android:layout_width="0dp"
                                app:layout_flexBasisPercent="45%"
                                android:layout_height="wrap_content"
                                android:checked="@{viewModel.joiningFee == SpeakingJoiningFee.CUSTOM}"
                                android:onClick="@{() -> viewModel.setJoiningFee(SpeakingJoiningFee.CUSTOM)}"
                                android:text="@string/podium_theater_fee_hint"
                                android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                            <com.google.android.material.textfield.TextInputLayout
                                android:id="@+id/textInputJoinFee"
                                style="@style/Widget.Flashat.GreyTextInput"
                                goneIfNot="@{viewModel.joiningFee == SpeakingJoiningFee.CUSTOM}"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:importantForAutofill="yes"
                                app:hintAnimationEnabled="false"
                                app:hintEnabled="false"
                                app:layout_constraintTop_toBottomOf="@id/radioGroupStageFee">

                                <com.google.android.material.textfield.TextInputEditText
                                    style="@style/Widget.Flashat.GreyTextInput"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:hint="@string/podium_enter_fee_hint"
                                    android:inputType="number"
                                    android:maxLength="10"
                                    android:text="@={viewModel.joiningFeeString}" />

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/tvJoinFeeErrorText"
                                    style="@style/TextAppearance.Flashat.Label"
                                    android:layout_width="wrap_content"
                                    tools:text="Please enter amount greater than 10"
                                    android:layout_marginTop="@dimen/line_spacing"
                                    android:layout_height="wrap_content"
                                    android:text="@{@string/podium_fee_error_text(SpeakingJoiningFee.MINIMUM_CUSTOM_FEE)}"
                                    android:textColor="@color/red"
                                    app:invisibleIf="@{viewModel.isJoiningFeeValid || viewModel.isJoiningFeeEmpty}" />
                            </com.google.android.material.textfield.TextInputLayout>

                        </com.google.android.flexbox.FlexboxLayout>

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnUpdate"
                            style="@style/Widget.Flashat.LargeRoundedButton"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/extra_margin"
                            android:layout_marginHorizontal="@dimen/double_margin"
                            android:checkable="true"
                            android:text="@string/common_update"
                            android:enabled="@{viewModel.advancedSettingsUpdateButtonVisible}"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/flex_layout_joining_fee"
                            tools:text="@string/common_update" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnCancel"
                            style="@style/Widget.Flashat.LargeRoundedButton.Outline.Primary"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginVertical="@dimen/line_spacing"
                            android:layout_marginHorizontal="@dimen/double_margin"
                            android:text="@string/common_cancel"
                            app:layout_constraintEnd_toEndOf="@id/btnUpdate"
                            app:layout_constraintStart_toStartOf="@id/btnUpdate"
                            app:layout_constraintTop_toBottomOf="@+id/btnUpdate"
                            tools:text="@string/common_cancel" />

                    </androidx.constraintlayout.widget.ConstraintLayout>
                </ScrollView>
            </com.kennyc.view.MultiStateView>


        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>