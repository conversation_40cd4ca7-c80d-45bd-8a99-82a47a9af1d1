<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="64dp"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ic_first"
        android:layout_width="match_parent"
        android:src="@drawable/ic_empty_gift"
        app:layout_constraintTop_toTopOf="parent"
        android:paddingVertical="@dimen/activity_margin"
        android:layout_height="wrap_content"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ic_second"
        android:layout_width="match_parent"
        android:src="@drawable/ic_empty_gift"
        app:layout_constraintTop_toBottomOf="@id/ic_first"
        android:paddingVertical="@dimen/activity_margin"
        android:layout_height="wrap_content"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ic_third"
        android:layout_width="match_parent"
        android:src="@drawable/ic_empty_gift"
        app:layout_constraintTop_toBottomOf="@id/ic_second"
        android:paddingVertical="@dimen/activity_margin"
        android:layout_height="wrap_content"/>

</androidx.constraintlayout.widget.ConstraintLayout>