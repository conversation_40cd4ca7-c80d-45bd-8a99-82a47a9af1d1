<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable name="viewModel" type="com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel" />
        <variable name="collapseActions" type="Boolean" />

        <variable name="hasChallengeOverlay" type="Boolean" />
        <variable name="speakersCollapsed" type="Boolean" />
        <import type="com.app.messej.data.model.enums.PodiumKind"/>
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/main_layout"
        android:background="@color/colorAlwaysDarkSurface"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cslParentLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fitsSystemWindows="true"
            android:background="@color/colorAlwaysDarkSurface">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <include
                    android:id="@+id/main_screen"
                    layout="@layout/item_podium_alone_main"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.kennyc.view.MultiStateView
                android:id="@+id/podium_header_multiStateView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_podium_video_top_overlay"
                android:paddingBottom="@dimen/extra_margin"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:msv_loadingView="@layout/layout_eds_state_loading_podium_header"
                app:msv_viewState="content">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/podium_header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.makeramen.roundedimageview.RoundedImageView
                        android:id="@+id/podium_dp"
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:layout_margin="@dimen/activity_margin"
                        android:scaleType="centerCrop"
                        android:clickable="true"
                        android:foreground="?attr/selectableItemBackground"
                        app:imageUrl="@{viewModel.podium.profileDp}"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.0"
                        app:placeholder="@{@drawable/im_podium_placeholder}"
                        app:riv_corner_radius="10dp"
                        tools:src="@drawable/im_podium_placeholder" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/podium_name"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/activity_margin"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="@{viewModel.podium.name}"
                        android:textAppearance="@style/TextAppearance.Flashat.Headline5"
                        android:textColor="@color/textColorOnPrimary"
                        app:layout_constraintEnd_toStartOf="@+id/close_button"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintStart_toEndOf="@+id/podium_dp"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toTopOf="@+id/podium_user_name"
                        app:layout_constraintVertical_chainStyle="packed"
                        tools:text="Let the Games Begin" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/podium_user_name"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/activity_margin"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="@{viewModel.mainScreenSpeaker.speaker.username}"
                        android:textAppearance="@style/TextAppearance.Flashat.Body2"
                        android:textColor="@color/textColorOnPrimary"
                        app:layout_constraintEnd_toStartOf="@+id/close_button"
                        app:layout_constraintStart_toEndOf="@+id/podium_dp"
                        app:layout_constraintTop_toBottomOf="@id/podium_name"
                        app:layout_constraintBottom_toTopOf="@+id/counter_holder"
                        tools:text="john_doe" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/counter_holder"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/line_spacing"
                        app:layout_constraintEnd_toEndOf="@id/podium_name"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/podium_user_name"
                        app:layout_constraintStart_toStartOf="@+id/podium_name">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/duration_counter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_podium_stat_chip"
                            android:paddingHorizontal="@dimen/element_spacing"
                            android:paddingTop="2dp"
                            android:paddingBottom="2dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/dur_img"
                                android:layout_width="14dp"
                                android:layout_height="wrap_content"
                                android:adjustViewBounds="true"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:srcCompat="@drawable/ic_access_time"
                                app:tint="@color/colorSecondary" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/line_spacing"
                                android:text="@{viewModel.liveTimer}"
                                android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                                android:textColor="@color/textColorOnPrimary"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@+id/dur_img"
                                app:layout_constraintTop_toTopOf="parent"
                                tools:text="00:25" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/live_counter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/element_spacing"
                            android:background="@drawable/bg_podium_stat_chip"
                            android:paddingHorizontal="@dimen/element_spacing"
                            android:paddingTop="2dp"
                            android:paddingBottom="2dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/duration_counter"
                            app:layout_constraintTop_toTopOf="parent">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/live_img"
                                android:layout_width="10dp"
                                android:layout_height="wrap_content"
                                android:adjustViewBounds="true"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:srcCompat="@drawable/bg_generic_circle"
                                app:tint="@color/colorPass" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/line_spacing"
                                android:text="@{viewModel.podium.liveUsersFormatted}"
                                android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                                android:textColor="@color/textColorOnPrimary"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@+id/live_img"
                                app:layout_constraintTop_toTopOf="parent"
                                tools:text="123" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/like_counter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/element_spacing"
                            android:background="@drawable/bg_podium_stat_chip"
                            android:paddingHorizontal="@dimen/element_spacing"
                            android:paddingTop="2dp"
                            android:paddingBottom="2dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/live_counter"
                            app:layout_constraintTop_toTopOf="parent">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/like_img"
                                android:layout_width="14dp"
                                android:layout_height="wrap_content"
                                android:adjustViewBounds="true"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:srcCompat="@drawable/ic_podium_liked"
                                app:tint="@color/textColorOnPrimary" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/line_spacing"
                                android:text="@{viewModel.podium.likesFormatted}"
                                android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                                android:textColor="@color/textColorOnPrimary"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@+id/like_img"
                                app:layout_constraintTop_toTopOf="parent"
                                tools:text="12K" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/close_button"
                        style="@style/Widget.Flashat.Button.TextButton.IconOnly.OnPrimary"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:icon="@drawable/ic_close"
                        app:layout_constraintBottom_toBottomOf="@+id/counter_holder"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/podium_name" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.kennyc.view.MultiStateView>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/action_decor_holder_top"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="-24dp"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/podium_header_multiStateView">

            </androidx.appcompat.widget.LinearLayoutCompat>

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/welcome_message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/activity_margin"
                app:layout_constrainedWidth="true"
                app:cardBackgroundColor="@color/colorAlwaysDarkSurface"
                app:cardCornerRadius="8dp"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/action_decor_holder_top"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="@dimen/activity_margin"
                    android:paddingVertical="@dimen/element_spacing"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    tools:text="This is a podium welcome message that got a little too long to be showed properly"
                    android:textColor="@color/textColorOnPrimaryLight"
                    android:textAlignment="center"
                    android:text="@{viewModel.podium.bio}"
                    android:maxLines="2"
                    android:ellipsize="end"/>

            </com.google.android.material.card.MaterialCardView>

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline_chat"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintGuide_percent="0.6" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline_chat_bg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintGuide_percent="0.55" />

            <androidx.compose.ui.platform.ComposeView
                android:id="@+id/anthem_overlay"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/podium_header_multiStateView"
                app:layout_constraintBottom_toTopOf="@+id/guideline_chat_bg"
                android:background="@color/colorPodiumAnthemBG"
                tools:visibility="visible" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@drawable/bg_podium_video_bottom_overlay"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/guideline_chat_bg"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <com.kennyc.view.MultiStateView
                android:id="@+id/live_chat_multiStateView"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintVertical_bias="1.0"
                app:layout_constrainedHeight="true"
                app:layout_constraintBottom_toTopOf="@id/chat_input_holder"
                app:layout_constraintEnd_toStartOf="@+id/actions_holder"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/guideline_chat"
                app:msv_emptyView="@layout/layout_list_state_empty"
                app:msv_errorView="@layout/layout_list_state_error"
                app:msv_viewState="content">

                <com.app.messej.ui.home.publictab.podiums.live.LiveChatRecyclerView
                    android:id="@+id/live_chat"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="@dimen/line_spacing"
                    android:requiresFadingEdge="vertical"
                    android:fadingEdgeLength="200dp"
                    app:reverseLayout="true"
                    tools:itemCount="6"
                    tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:listitem="@layout/item_podium_live_chat"/>

            </com.kennyc.view.MultiStateView>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/actions_holder"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintVertical_bias="1.0"
                app:layout_constraintBottom_toTopOf="@id/chat_input_holder"
                app:layout_constraintEnd_toEndOf="parent"
                android:animateLayoutChanges="true">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/action_collapse"
                    style="@style/Widget.Flashat.Podium.ActionFAB.Secondary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/element_spacing"
                    android:layout_marginVertical="@dimen/line_spacing"
                    app:icon="@{collapseActions?@drawable/ic_caret_up:@drawable/ic_caret_down}"
                    tools:icon="@drawable/ic_caret_down"
                    app:layout_constraintBottom_toTopOf="@+id/actions"
                    app:layout_constraintEnd_toEndOf="parent" />

                <LinearLayout
                    android:id="@+id/actions"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingBottom="@dimen/element_spacing"
                    android:clipToPadding="false"
                    android:orientation="vertical"
                    app:goneIf="@{collapseActions}"
                    android:gravity="end"
                    app:layout_constraintVertical_bias="1.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/action_camera_flip"
                        style="@style/Widget.Flashat.Podium.ActionFAB.Secondary"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="@dimen/line_spacing"
                        android:layout_marginHorizontal="@dimen/element_spacing"
                        android:visibility="gone"
                        tools:visibility="visible"
                        app:goneIfNot="@{viewModel.myVideoIsTurnedOn}"
                        app:icon="@drawable/ic_flash_record_flip"/>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/action_video_toggle"
                        style="@style/Widget.Flashat.Podium.ActionFAB.Secondary"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="@dimen/line_spacing"
                        android:layout_marginHorizontal="@dimen/element_spacing"
                        android:visibility="gone"
                        tools:visibility="visible"
                        app:goneIfNot="@{viewModel.showVideoToggle}"
                        app:icon="@{viewModel.myVideoIsTurnedOn?@drawable/ic_podium_video_off:@drawable/ic_podium_video_on}"
                        tools:icon="@drawable/ic_podium_video_on" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/action_mute_toggle"
                        style="@style/Widget.Flashat.Podium.ActionFAB.FullIcon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="@dimen/line_spacing"
                        android:layout_marginHorizontal="@dimen/element_spacing"
                        android:visibility="gone"
                        tools:visibility="visible"
                        app:goneIfNot="@{viewModel.showMuteToggle}"
                        app:icon="@{viewModel.meAsSpeaker.muted?@drawable/ic_podium_mic_off:@drawable/ic_podium_mic_on}"
                        tools:icon="@drawable/ic_podium_mic_on" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/action_share"
                        style="@style/Widget.Flashat.Podium.ActionFAB.Secondary"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="@dimen/line_spacing"
                        android:layout_marginHorizontal="@dimen/element_spacing"
                        app:goneIfNot="@{viewModel.iAmManager}"
                        tools:visibility="visible"
                        app:icon="@drawable/ic_podium_share"/>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/like_container"
                        android:clipChildren="false"
                        android:clipToPadding="false"
                        app:goneIfNot="@{viewModel.showLikeAction}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:applySystemBarInsets="@{`bottom`}">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/action_like"
                            style="@style/Widget.Flashat.PaidLikeButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:icon="@{viewModel.podium.likesDisabled?@drawable/ic_podium_like_disable:@drawable/ic_podium_like}"
                            tools:icon="@drawable/ic_podium_like"
                            android:enabled="@{!viewModel.likeButtonEnabled}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <include
                android:id="@+id/likes_container"
                layout="@layout/item_podium_likes_container" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/chat_input_holder"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:elevation="6dp"
                android:background="@color/colorSurface"
                app:applySystemBarInsets="@{`ime`}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/chat_input"
                    style="@style/Widget.Flashat.GreyTextInput.Small"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="@dimen/element_spacing"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:layout_marginEnd="@dimen/element_spacing"
                    app:hintAnimationEnabled="false"
                    app:hintEnabled="false"
                    app:counterEnabled="true"
                    app:counterMaxLength="150"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/chat_send_holder"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/input_comment"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/podium_comment_hint"
                        android:textColorHint="@color/textColorAlwaysDarkHint"
                        android:textColor="@color/textColorAlwaysDarkPrimary"
                        android:enabled="@{viewModel.canSendChats}"
                        android:inputType="textShortMessage|textMultiLine"
                        android:maxLines="1"
                        android:maxLength="150"
                        tools:text="Hello World"
                        android:text="@={viewModel.chatText}" />

                </com.google.android.material.textfield.TextInputLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/chat_send_holder"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/more_button"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/gift_send_button"
                        style="@style/Widget.Flashat.Button.AloneGiftButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:enabled="@{viewModel.podium.podiumGiftPaused!=true}"
                        tools:enabled="true"
                        app:dimIfNot="@{viewModel.podium.podiumGiftPaused!=true}"
                        app:goneIf="@{viewModel.chatText.trim().length>0}"
                        android:text="@{viewModel.podium.giftCountFormatted}"
                        tools:text="16.7K"
                        app:icon="@drawable/ic_podium_speaker_gift_circle"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/chat_send_button"
                        style="@style/Widget.Flashat.Button.AloneSendButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:goneIfNot="@{viewModel.chatText.trim().length>0 &amp;&amp; viewModel.canSendChats}"
                        app:icon="@drawable/ic_chat_send"
                        app:iconTint="@color/colorPrimary"
                        android:visibility="invisible"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/more_button"
                    style="@style/Widget.Flashat.Button.TextButton.IconOnly"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/line_spacing"
                    app:icon="@drawable/ic_more_vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>
