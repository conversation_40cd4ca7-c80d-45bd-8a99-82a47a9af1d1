<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="speaker"
            type="com.app.messej.data.model.AbstractUser" />
        <variable
            name="stats"
            type="com.app.messej.data.model.api.podium.PodiumUserLikesCoinsResponse" />
        <variable
            name="userTribeName"
            type="String" />
        <variable
            name="isBirthDayPodium"
            type="Boolean" />
        <variable
            name="isPodiumManager"
            type="Boolean" />
        <variable
            name="hideSendGift"
            type="Boolean" />
        <variable
            name="podiumName"
            type="String" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/activity_margin"
        android:animateLayoutChanges="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/handle"
        tools:showIn="@layout/fragment_podium_speaker_actions_bottom_sheet">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5"/>

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/user_dp"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginHorizontal="@dimen/activity_margin"
            android:layout_marginVertical="@dimen/activity_margin"
            android:scaleType="centerCrop"
            app:imageUrl="@{speaker.thumbnail}"
            app:layout_constraintBaseline_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:placeholder="@{@drawable/im_user_placeholder_square}"
            app:riv_oval="true"
            tools:src="@drawable/im_user_placeholder_square" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/superstar_premium_badge"
            android:layout_width="@dimen/premium_badge_size"
            android:layout_height="@dimen/premium_badge_size"
            android:elevation="4dp"
            app:layout_constraintStart_toStartOf="@id/user_dp"
            app:layout_constraintTop_toTopOf="@id/user_dp"
            app:userBadgeOnPrimary="@{speaker.userBadge}"
            tools:src="@drawable/ic_user_badge_premium" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/user_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/activity_margin"
            android:layout_marginEnd="@dimen/element_spacing"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@{speaker.name}"
            android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
            android:textColor="@color/textColorSecondary"
            app:layout_constrainedWidth="true"
           app:layout_constraintEnd_toStartOf="@id/send_gift"
            app:layout_constraintStart_toEndOf="@+id/user_dp"
            app:layout_constraintTop_toTopOf="@+id/user_dp"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="Esther Lopez Desperanzo" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/send_gift"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="@dimen/extra_margin"
            android:adjustViewBounds="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/user_dp"
            android:layout_marginEnd="@dimen/element_spacing"
            app:goneIfNot="@{hideSendGift}"
            app:srcCompat="@drawable/ic_podium_speaker_gift_circle" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/message"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="4"
            android:text="@{speaker.username}"
            android:textAppearance="@style/TextAppearance.Flashat.Label.Small"
            android:textColor="@color/textColorSecondary"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="@+id/user_name"
            app:layout_constraintStart_toStartOf="@+id/user_name"
            app:layout_constraintTop_toBottomOf="@+id/user_name"
            tools:text="esther.lopz" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/tribe_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:srcCompat="@drawable/ic_tribe_rounded_rectangle"
            goneIfNot="@{speaker.premiumUser}"
            app:layout_constraintTop_toBottomOf="@id/message"
            app:layout_constraintStart_toStartOf="@+id/user_name"
            android:layout_marginTop="@dimen/line_spacing"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tribe_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.Flashat.Label.Small"
            app:layout_constraintTop_toTopOf="@id/tribe_image"
            app:layout_constraintBottom_toBottomOf="@id/tribe_image"
            app:layout_constraintStart_toEndOf="@id/tribe_image"
            app:layout_constraintEnd_toEndOf="@id/message"
            android:textColor="@color/colorPrimary"
            android:layout_marginStart="@dimen/element_spacing"
            android:ellipsize="end"
            android:maxLines="1"
            goneIfNot="@{speaker.premiumUser}"
            android:text="@{userTribeName}"
            tools:text="DreamerBy BY" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/total_coins_mini"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/element_spacing"
            android:background="@drawable/bg_podium_about_stat_chip"
            android:paddingHorizontal="@dimen/element_spacing"
            android:paddingVertical="@dimen/line_spacing"
            app:goneIfNull="@{stats}"
            app:layout_constraintBottom_toBottomOf="@+id/user_flix_rate"
            app:layout_constraintStart_toEndOf="@+id/user_flix_rate"
            app:layout_constraintTop_toTopOf="@+id/user_flix_rate"
            tools:layout_editor_absoluteX="64dp"
            tools:layout_editor_absoluteY="64dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/total_coins_img_mini"
                android:layout_width="14dp"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/total_coins_count_mini"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_coin" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/total_coins_count_mini"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/line_spacing"
                android:text="@{isBirthDayPodium &amp;&amp; !isPodiumManager ? stats.coinsSentFormated : stats.coinsFormatted}"
                android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                android:textColor="@color/textColorSecondary"
                app:layout_constraintBottom_toBottomOf="@id/total_coins_img_mini"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/total_coins_img_mini"
                app:layout_constraintTop_toTopOf="@id/total_coins_img_mini"
                tools:text="50" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/total_likes_mini"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/element_spacing"
            android:background="@drawable/bg_podium_about_stat_chip"
            android:paddingHorizontal="@dimen/element_spacing"
            android:paddingVertical="@dimen/line_spacing"
            app:goneIf="@{isBirthDayPodium || stats == null}"
            app:layout_constraintStart_toEndOf="@+id/total_coins_mini"
            app:layout_constraintTop_toTopOf="@+id/total_coins_mini"
            tools:layout_editor_absoluteX="131dp"
            tools:layout_editor_absoluteY="64dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/total_likes_img_mini"
                android:layout_width="14dp"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/total_likes_count_mini"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_podium_like" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/total_likes_count_mini"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/line_spacing"
                android:text="@{stats.likesFormatted}"
                android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                android:textColor="@color/textColorSecondary"
                app:layout_constraintBottom_toBottomOf="@id/total_likes_img_mini"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/total_likes_img_mini"
                app:layout_constraintTop_toTopOf="@id/total_likes_img_mini"
                tools:text="50" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/total_followers_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/element_spacing"
            app:goneIfNull="@{stats}"
            android:background="@drawable/bg_podium_about_stat_chip"
            android:paddingHorizontal="@dimen/element_spacing"
            android:paddingVertical="@dimen/line_spacing"
            app:layout_constraintStart_toEndOf="@+id/total_likes_mini"
            app:layout_constraintTop_toTopOf="@+id/total_coins_mini"
            tools:layout_editor_absoluteX="131dp"
            tools:layout_editor_absoluteY="64dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/total_followers_img"
                android:layout_width="14dp"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/text_total_followers"
                app:flow_horizontalAlign="start"
                app:layout_constrainedWidth="true"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/colorPrimary"
                app:srcCompat="@drawable/ic_home_public_tab_followers_grey" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_total_followers"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/line_spacing"
                android:text="@string/home_private_tab_followers"
                android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                android:textColor="@color/colorPrimary"
                app:layout_constraintBottom_toBottomOf="@id/total_followers_img"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/total_followers_img"
                app:layout_constraintTop_toTopOf="@id/total_followers_img"
                tools:text="@string/home_private_tab_followers" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/total_followers_count_podium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/line_spacing"
                android:text="@{stats.followersCount.toString()}"
                android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                android:textColor="@color/textColorSecondary"
                app:layout_constraintBottom_toBottomOf="@id/total_followers_img"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/text_total_followers"
                app:layout_constraintTop_toTopOf="@id/total_followers_img"
                tools:text="50" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/user_flix_rate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_podium_transparent_chip"
            android:layout_marginTop="@dimen/element_spacing"
            android:paddingHorizontal="@dimen/element_spacing"
            android:paddingVertical="@dimen/element_spacing"
            android:textAppearance="@style/TextAppearance.Flashat.Label.Tiny.Bold"
            android:textColor="@color/colorPrimary"
            app:goneIfNull="@{stats}"
            app:layout_constraintStart_toStartOf="@+id/user_dp"
            app:layout_constraintTop_toBottomOf="@+id/tribe_image"
            tools:text="The President 100%" />

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_today"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/element_spacing"
            android:layout_marginTop="@dimen/element_spacing"
            app:cardBackgroundColor="@color/colorSurfaceSecondary"
            android:layout_marginBottom="@dimen/element_spacing"
            app:cardCornerRadius="@dimen/element_spacing"
            app:cardElevation="@dimen/element_spacing"
            app:goneIfNull="@{stats}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/guideline_center"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/user_flix_rate">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <include
                    android:id="@+id/header_today"
                    layout="@layout/single_podium_statistics_header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:bgColor="@{speaker.premiumUser?@color/colorHuddleAmbassadorBackground:@color/colorPodiumSpeakerResident}"
                    app:headerText="@{@string/podium_speaker_title_today}"
                    app:podiumName="@{podiumName}"
                    app:userIconTint="@{speaker.premiumUser?@color/colorPrimary:@color/textColorOnSecondary}" />

                <View
                    android:id="@+id/header_view"
                    android:layout_width="0dp"
                    android:layout_height="0.5dp"
                    android:layout_marginTop="1dp"
                    android:background="@color/textColorBusinessSecondary"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/header_today"
                    app:layout_constraintTop_toBottomOf="@id/header_today" />

                <include
                    android:id="@+id/item_today_gift_received"
                    layout="@layout/single_podium_statistics_item"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:contentText="@{@string/common_coins(stats.podiumStats.today.giftReceivedFormatted)}"
                    app:headerText="@{@string/podium_stat_gift_received}"
                    app:layout_constraintTop_toBottomOf="@id/header_view"
                    app:textColor="@{speaker.premiumUser?@color/colorPrimary:@color/textColorSecondary}" />

                <View
                    android:id="@+id/gift_received_view"
                    android:layout_width="0dp"
                    android:layout_height="0.5dp"
                    android:layout_marginTop="1dp"
                    android:background="@color/textColorBusinessSecondary"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/header_today"
                    app:layout_constraintTop_toBottomOf="@id/item_today_gift_received" />

                <include
                    android:id="@+id/item_today_gift_gained"
                    layout="@layout/single_podium_statistics_item"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:contentText="@{@string/common_coins(stats.podiumStats.today.giftSentFormatted)}"
                    app:headerText="@{@string/podium_stat_gift_given}"
                    app:layout_constraintTop_toBottomOf="@id/gift_received_view"
                    app:textColor="@{speaker.premiumUser?@color/colorPrimary:@color/textColorSecondary}" />

                <View
                    android:id="@+id/gift_gained_view"
                    android:layout_width="0dp"
                    android:layout_height="0.5dp"
                    android:layout_marginTop="1dp"
                    android:background="@color/textColorBusinessSecondary"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/header_today"
                    app:layout_constraintTop_toBottomOf="@id/item_today_gift_gained" />

                <include
                    android:id="@+id/item_today_podium_time"
                    layout="@layout/single_podium_statistics_item"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:contentText="@{stats.podiumStats.today.podiumTime}"
                    app:headerText="@{@string/podium_stat_podium_time}"
                    app:layout_constraintTop_toBottomOf="@id/gift_gained_view"
                    app:textColor="@{speaker.premiumUser?@color/colorPrimary:@color/textColorSecondary}" />


            </androidx.constraintlayout.widget.ConstraintLayout>


        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_all_time"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/element_spacing"
            app:cardBackgroundColor="@color/colorSurfaceSecondary"
            android:layout_marginBottom="@dimen/element_spacing"
            app:cardCornerRadius="@dimen/element_spacing"
            app:cardElevation="@dimen/element_spacing"
            app:goneIfNull="@{stats}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/guideline_center"
            app:layout_constraintTop_toTopOf="@+id/card_today">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <include
                    android:id="@+id/header_year"
                    layout="@layout/single_podium_statistics_header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:bgColor="@{speaker.premiumUser?@color/colorHuddleAmbassadorBackground:@color/colorPodiumSpeakerResident}"
                    app:headerText="@{@string/podium_speaker_title_all_time}"
                    app:podiumName="@{podiumName}"
                    app:userIconTint="@{speaker.premiumUser?@color/colorPrimary:@color/textColorOnSecondary}" />

                <View
                    android:id="@+id/header_all_view"
                    android:layout_width="0dp"
                    android:layout_height="0.5dp"
                    android:layout_marginTop="1dp"
                    android:background="@color/textColorBusinessSecondary"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/header_year"
                    app:layout_constraintTop_toBottomOf="@id/header_year" />

                <include
                    android:id="@+id/item_year_gift_received"
                    layout="@layout/single_podium_statistics_item"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:contentText="@{@string/common_coins(stats.podiumStats.year.giftReceivedFormatted)}"
                    app:headerText="@{@string/podium_stat_gift_received}"
                    app:layout_constraintTop_toBottomOf="@id/header_all_view"
                    app:textColor="@{speaker.premiumUser?@color/colorPrimary:@color/textColorSecondary}" />

                <View
                    android:id="@+id/gift_received_all_view"
                    android:layout_width="0dp"
                    android:layout_height="0.5dp"
                    android:layout_marginTop="1dp"
                    android:background="@color/textColorBusinessSecondary"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/header_all_view"
                    app:layout_constraintTop_toBottomOf="@id/item_year_gift_received" />

                <include
                    android:id="@+id/item_year_gift_gained"
                    layout="@layout/single_podium_statistics_item"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:contentText="@{@string/common_coins(stats.podiumStats.year.giftSentFormatted)}"
                    app:headerText="@{@string/podium_stat_gift_given}"
                    app:layout_constraintTop_toBottomOf="@id/item_year_gift_received"
                    app:textColor="@{speaker.premiumUser?@color/colorPrimary:@color/textColorSecondary}" />

                <View
                    android:id="@+id/gift_gained_all_view"
                    android:layout_width="0dp"
                    android:layout_height="0.5dp"
                    android:layout_marginTop="1dp"
                    android:background="@color/textColorBusinessSecondary"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/header_all_view"
                    app:layout_constraintTop_toBottomOf="@id/item_year_gift_gained" />

                <include
                    android:id="@+id/item_year_podium_time"
                    layout="@layout/single_podium_statistics_item"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:contentText="@{stats.podiumStats.year.podiumTime}"
                    app:headerText="@{@string/podium_stat_podium_time}"
                    app:layout_constraintTop_toBottomOf="@id/item_year_gift_gained"
                    app:textColor="@{speaker.premiumUser?@color/colorPrimary:@color/textColorSecondary}" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>