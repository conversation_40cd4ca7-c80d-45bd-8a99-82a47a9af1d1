<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:showIn="@layout/fragment_podium_live_lecture"
    >
    <data>
        <variable name="speaker" type="com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel.ActiveSpeakerUIModel" />
        <variable name="showCameraToggle" type="Boolean" />
        <variable name="showVideo" type="Boolean" />
        <variable name="showLikes" type="Boolean" />
        <variable name="isSelf" type="Boolean" />
        <variable name="iAmManager" type="Boolean" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:cardBackgroundColor="@color/colorSurfaceSecondary">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/placeholder_plus_button"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/colorSurface"
            android:clickable="true"
            android:foreground="?attr/selectableItemBackground"
            tools:visibility="gone"
            app:goneIfNotNull="@{speaker}">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/placeholder_image"
                android:layout_width="60dp"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_podium_speaker_empty"
                tools:srcCompat="@drawable/ic_podium_theater_stage_placeholder" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/placeholder_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:textAppearance="@style/TextAppearance.Flashat.Label"
                android:layout_marginTop="@dimen/activity_margin"
                android:textColor="@color/colorSecondary"
                android:textAllCaps="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/placeholder_image"
                tools:text="TAP FOR MIC" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/main_screen"
            android:visibility="gone"
            tools:visibility="visible"
            app:goneIfNull="@{speaker}"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <include
                android:id="@+id/speaker_header"
                layout="@layout/item_podium_speaker_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:visibility="visible"
                app:speaker="@{speaker}"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/click_target"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/speaker_header">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/video_holder"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:goneIfNot="@{showVideo}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent">

                    <!--                <include layout="@layout/item_podium_main_screen_video_surface" />-->

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:goneIf="@{showVideo}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/user_dp_bg"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:scaleType="centerCrop"
                        app:imageUrl="@{speaker.speaker.thumbnail}"
                        app:placeholder="@{@drawable/im_user_placeholder_square_always_dark}"
                        app:blurRadius="@{60}"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        android:foreground="#99000000"
                        tools:src="@drawable/im_user_placeholder_square_always_dark" />

                    <com.makeramen.roundedimageview.RoundedImageView
                        android:id="@+id/chat_flag"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/challenge_gift_bar_height"
                        android:adjustViewBounds="true"
                        app:imageRes="@{speaker.countryFlag}"
                        tools:visibility="visible"
                        android:layout_margin="@dimen/element_spacing"
                        app:riv_border_width="0dp"
                        app:riv_corner_radius="2dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:src="@drawable/flag_france" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:layout_margin="3dp"
                        android:background="@drawable/ic_podium_speaker_likes"
                        tools:text="88"
                        android:text="@{speaker.speaker.likesGivenByUser.toString()}"
                        android:visibility="gone"
                        tools:visibility="visible"
                        app:goneIfNot="@{showLikes}"
                        android:textAppearance="@style/TextAppearance.Flashat.Label.Smaller"
                        android:textColor="@color/colorSecondary"
                        android:textAlignment="center"
                        android:paddingTop="4dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/user_bubble"
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        app:layout_constraintHeight_max="100dp"
                        android:layout_margin="2dp"
                        android:animateLayoutChanges="true"
                        app:layout_constraintVertical_bias="0.45"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <com.makeramen.roundedimageview.RoundedImageView
                            android:id="@+id/user_dp"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:scaleType="centerCrop"
                            android:layout_margin="@dimen/line_spacing"
                            app:imageUrl="@{speaker.speaker.thumbnail}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintDimensionRatio="W,1"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:placeholder="@{@drawable/im_user_placeholder_square}"
                            app:riv_oval="true"
                            tools:src="@drawable/im_user_placeholder_square" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="0dp"
                            android:layout_height="0dp"
                            app:goneIfNot="@{speaker.currentlySpeaking}"
                            app:animateIfSpeaking="@{speaker.currentlySpeaking}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/bg_podium_speaking_circle" />

                        <androidx.constraintlayout.widget.Guideline
                            android:id="@+id/guideline"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            app:layout_constraintGuide_percent="0.25"/>

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/premium_badge"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:adjustViewBounds="true"
                            android:elevation="4dp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:localeAwareTranslationX="@{@dimen/line_spacing}"
                            android:translationY="@dimen/line_spacing"
                            app:layout_constraintEnd_toEndOf="@id/guideline"
                            app:userBadgeOnPrimary="@{speaker.speaker.userBadge}"
                            tools:src="@drawable/ic_user_badge_premium" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/footer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_main_speaker_bottom_gradient"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/action_holder"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:goneIf="@{isSelf}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/mic_icon"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginHorizontal="@dimen/element_spacing"
                        android:src="@{speaker.muted?@drawable/ic_podium_mic_off:@drawable/ic_podium_mic_on}"
                        app:goneIfNot="@{speaker.online}"
                        tools:src="@drawable/ic_podium_mic_off"
                        tools:visibility="visible"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/offline_icon"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_margin="@dimen/line_spacing"
                        app:goneIf="@{speaker.online}"
                        tools:visibility="gone"
                        android:src="@drawable/ic_podium_offline"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"/>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:layout_marginVertical="@dimen/element_spacing"
                    app:layout_goneMarginEnd="@dimen/element_spacing"
                    android:text="@{speaker.speaker.name}"
                    android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                    android:textColor="@color/textColorOnPrimary"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/action_holder"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="John Doe" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/self_action_holder"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:goneIfNot="@{isSelf}"
                android:animateLayoutChanges="true"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toTopOf="@id/footer">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/camera_flip_button"
                    style="@style/Widget.Flashat.MainScreenButton"
                    android:layout_marginHorizontal="2dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:goneIfNot="@{showVideo}"
                    app:icon="@drawable/ic_flash_record_flip"
                    tools:visibility="visible"
                    app:layout_constraintEnd_toStartOf="@id/camera_toggle_button"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/camera_toggle_button"
                    style="@style/Widget.Flashat.MainScreenButton"
                    android:layout_marginHorizontal="2dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:goneIfNot="@{showCameraToggle}"
                    app:icon="@{showVideo?@drawable/ic_podium_video_on:@drawable/ic_podium_video_off}"
                    tools:icon="@drawable/ic_podium_video_off"
                    tools:visibility="visible"
                    app:layout_constraintEnd_toStartOf="@id/mic_button"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/mic_button"
                    style="@style/Widget.Flashat.MainScreenButton"
                    android:layout_marginHorizontal="2dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:icon="@{speaker.muted?@drawable/ic_podium_mic_speaker_off:@drawable/ic_podium_mic_speaker_on}"
                    tools:icon="@drawable/ic_podium_mic_speaker_on"
                    tools:visibility="visible"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:goneIfNot="@{speaker.currentlySpeaking}"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:elevation="@dimen/activity_margin"
                app:srcCompat="@drawable/bg_podium_speaking_square" />

            <FrameLayout
                android:id="@+id/challengeDecoration"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:visibility="gone"
                tools:visibility="visible"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toTopOf="@+id/footer">

            </FrameLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>