<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:showIn="@layout/layout_business_statement">

    <data>
        <variable name="statement" type="com.app.messej.data.model.entity.BusinessStatement" />
        <variable name="itemTextColor" type="Integer" />
        <variable name="headerTextColor" type="Integer" />
        <variable name="flaxColor" type="Integer" />
    </data>

<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_left_1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent=".08" />

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:adjustViewBounds="true"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/bg_business_card_gradient" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/text_pp_statement"
        style="@style/TextAppearance.Flashat.Subtitle2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/activity_margin"
        android:layout_marginTop="@dimen/activity_margin"
        android:text="@string/title_business_active_pp_statement"
        android:textColor="@{headerTextColor}"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_this_month"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/text_pp_statement">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_left"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent=".08" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/title_refunded"
            style="@style/TextAppearance.Flashat.Business.Caption"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_margin"
            android:text="@string/title_business_refunded"
            android:textColor="@{itemTextColor}"
            goneIf="@{statement.grandTotalRefunded == 0}"
            app:layout_constraintEnd_toStartOf="@id/refunded_amount"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toEndOf="@id/guideline_left"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/title_business_refunded" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/refunded_amount"
            style="@style/TextAppearance.Flashat.Business.Label"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/activity_margin"
            android:textAlignment="textEnd"
            android:textColor="@{itemTextColor}"
            goneIf="@{statement.grandTotalRefunded == 0}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/title_refunded"
            app:layout_constraintTop_toTopOf="@+id/title_refunded"
            app:flaxValue="@{statement.grandTotalRefunded}"
            app:flaxColor="@{flaxColor}"
            tools:text="@tools:sample/us_phones" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/title_generated"
            style="@style/TextAppearance.Flashat.Business.Caption"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_margin"
            android:text="@string/title_business_generated"
            android:textColor="@{itemTextColor}"
            app:layout_constraintEnd_toStartOf="@+id/generated_amount"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="@id/guideline_left"
            app:layout_constraintTop_toBottomOf="@id/title_refunded"
            tools:text="@string/title_business_generated" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/generated_amount"
            style="@style/TextAppearance.Flashat.Business.Label"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/activity_margin"
            android:textAlignment="textEnd"
            android:textColor="@{itemTextColor}"
            app:flaxValue="@{statement.grandTotalGenerated}"
            app:flaxColor="@{flaxColor}"
            app:layout_constraintBottom_toBottomOf="@+id/title_generated"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintStart_toEndOf="@+id/title_generated"
            app:layout_constraintTop_toTopOf="@+id/title_generated"
            tools:text="@tools:sample/us_zipcodes" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/title_rewarded"
            style="@style/TextAppearance.Flashat.Business.Caption"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_margin"
            android:text="@string/title_business_rewarded"
            android:textColor="@{itemTextColor}"
            app:layout_constraintEnd_toStartOf="@+id/rewarded_amount"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="@id/guideline_left"
            app:layout_constraintTop_toBottomOf="@id/title_generated"
            tools:text="@string/title_business_rewarded" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/rewarded_amount"
            style="@style/TextAppearance.Flashat.Business.Label"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/activity_margin"
            android:textAlignment="textEnd"
            android:textColor="@{itemTextColor}"
            app:flaxValue="@{statement.grandTotalRewarded}"
            app:flaxColor="@{flaxColor}"
            app:layout_constraintBottom_toBottomOf="@+id/title_rewarded"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintStart_toEndOf="@+id/title_rewarded"
            app:layout_constraintTop_toTopOf="@+id/title_rewarded"
            tools:text="@tools:sample/us_zipcodes" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/title_sold_gifts"
            style="@style/TextAppearance.Flashat.Business.Caption"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_margin"
            android:text="@string/title_business_sold_gift"
            android:textColor="@{itemTextColor}"
            app:layout_constraintEnd_toStartOf="@id/sold_gift_points"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="@id/guideline_left"
            app:layout_constraintTop_toBottomOf="@id/rewarded_amount"
            tools:text="@string/title_business_sold_gift" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/sold_gift_points"
            style="@style/TextAppearance.Flashat.Business.Label"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/activity_margin"
            android:textAlignment="textEnd"
            android:textColor="@{itemTextColor}"
            app:flaxValue="@{statement.soldGiftFlax}"
            app:flaxColor="@{flaxColor}"
            app:layout_constraintBottom_toBottomOf="@+id/title_sold_gifts"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintStart_toEndOf="@+id/title_sold_gifts"
            app:layout_constraintTop_toTopOf="@+id/title_sold_gifts"
            tools:text="@tools:sample/us_zipcodes" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/title_received_flax"
            style="@style/TextAppearance.Flashat.Business.Caption"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_margin"
            android:text="@string/statement_title_received_flax"
            android:textColor="@{itemTextColor}"
            app:layout_constraintEnd_toStartOf="@+id/received_flax"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="@id/guideline_left"
            app:layout_constraintTop_toBottomOf="@id/sold_gift_points"
            tools:text="@string/statement_title_received_flax" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/received_flax"
            style="@style/TextAppearance.Flashat.Business.Label"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/activity_margin"
            android:textAlignment="textEnd"
            android:textColor="@{itemTextColor}"
            app:flaxValue="@{statement.receivedFlax}"
            app:flaxColor="@{flaxColor}"
            app:layout_constraintBottom_toBottomOf="@+id/title_received_flax"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintStart_toEndOf="@+id/title_received_flax"
            app:layout_constraintTop_toTopOf="@+id/title_received_flax"
            tools:text="@tools:sample/us_zipcodes" />



        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/title_purchased_flax"
            style="@style/TextAppearance.Flashat.Business.Caption"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_margin"
            android:text="@string/statement_title_purchased_flax"
            android:textColor="@{itemTextColor}"
            app:layout_constraintEnd_toStartOf="@+id/purchased_flax"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="@id/title_received_flax"
            app:layout_constraintTop_toBottomOf="@id/received_flax"
            tools:text="@string/statement_title_purchased_flax" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/purchased_flax"
            style="@style/TextAppearance.Flashat.Business.Label"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/activity_margin"
            android:textAlignment="textEnd"
            android:textColor="@{itemTextColor}"
            app:flaxValue="@{statement.purchasedFlax}"
            app:flaxColor="@{flaxColor}"
            app:layout_constraintBottom_toBottomOf="@+id/title_purchased_flax"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintStart_toEndOf="@+id/title_purchased_flax"
            app:layout_constraintTop_toTopOf="@+id/title_purchased_flax"
            tools:text="@tools:sample/us_zipcodes" />


        <View
            android:id="@+id/divider_total"
            android:layout_width="0dp"
            android:layout_height=".5dp"
            android:layout_marginStart="@dimen/business_horizontal_divider_start_margin"
            android:layout_marginTop="10dp"
            android:backgroundTint="@{itemTextColor}"
            android:background="@drawable/bg_business_horizontal_dotted_line"
            app:layout_constraintEnd_toEndOf="@id/purchased_flax"
            app:layout_constraintStart_toStartOf="@id/purchased_flax"
            app:layout_constraintTop_toBottomOf="@id/purchased_flax" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/title_total_credit"
            style="@style/TextAppearance.Flashat.Subtitle2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/element_spacing"
            android:layout_marginBottom="@dimen/element_spacing"
            android:text="@string/title_business_total"
            android:textColor="@{headerTextColor}"
            app:layout_constraintEnd_toStartOf="@+id/total_amount"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="@id/guideline_left"
            app:layout_constraintTop_toBottomOf="@id/divider_total"
            tools:text="@string/title_business_total" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/total_amount"
            style="@style/TextAppearance.Flashat.Business.Label"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/activity_margin"
            android:textAlignment="textEnd"
            android:textColor="@{itemTextColor}"
            app:flaxValue="@{statement.grandTotal}"
            app:flaxColor="@{flaxColor}"
            app:layout_constraintBottom_toBottomOf="@+id/title_total_credit"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintStart_toEndOf="@+id/title_total_credit"
            app:layout_constraintTop_toTopOf="@+id/title_total_credit"
            tools:text="@tools:sample/us_zipcodes" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/background_image"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:srcCompat="@drawable/bg_business_mask_group" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title_sent_flax"
        style="@style/TextAppearance.Flashat.Business.Caption"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/activity_margin"
        android:text="@string/statement_title_sent_flax"
        android:textColor="@{itemTextColor}"
        app:layout_constraintEnd_toStartOf="@id/sent_flax"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toEndOf="@id/guideline_left_1"
        app:layout_constraintTop_toBottomOf="@id/layout_this_month"
        tools:text="@string/statement_title_sent_flax" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/sent_flax"
        style="@style/TextAppearance.Flashat.Business.Label"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/activity_margin"
        android:textAlignment="textEnd"
        android:textColor="@color/textColorBusinessRed"
        app:flaxValue="@{statement.sentFlax}"
        app:flaxColor="@{flaxColor}"
        app:layout_constraintBottom_toBottomOf="@+id/title_sent_flax"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintStart_toEndOf="@+id/title_sent_flax"
        app:layout_constraintTop_toTopOf="@+id/title_sent_flax"
        tools:text="@tools:sample/us_zipcodes" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title_gifts_purchase"
        style="@style/TextAppearance.Flashat.Business.Caption"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/activity_margin"
        android:text="@string/title_business_gifts_purchase"
        android:textColor="@{itemTextColor}"
        app:layout_constraintEnd_toStartOf="@id/gift_purchase_points"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="@id/title_sent_flax"
        app:layout_constraintTop_toBottomOf="@id/title_sent_flax"
        tools:text="@string/title_business_gifts_purchase" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/gift_purchase_points"
        style="@style/TextAppearance.Flashat.Business.Label"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/activity_margin"
        android:textAlignment="textEnd"
        android:textColor="@color/textColorBusinessRed"
        app:flaxValue="@{statement.purchasedGiftFlax}"
        app:flaxColor="@{flaxColor}"
        app:layout_constraintBottom_toBottomOf="@+id/title_gifts_purchase"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintStart_toEndOf="@+id/title_gifts_purchase"
        app:layout_constraintTop_toTopOf="@+id/title_gifts_purchase"
        tools:text="@tools:sample/us_zipcodes" />



    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title_flix_utilized"
        style="@style/TextAppearance.Flashat.Business.Caption"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/activity_margin"
        android:text="@string/title_flix_utilized"
        android:textColor="@{itemTextColor}"
        app:layout_constraintEnd_toStartOf="@id/gift_purchase_points"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="@id/title_gifts_purchase"
        app:layout_constraintTop_toBottomOf="@id/title_gifts_purchase"
        tools:text="@string/title_flix_utilized" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/flix_utilized"
        style="@style/TextAppearance.Flashat.Business.Label"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/activity_margin"
        android:textAlignment="textEnd"
        android:textColor="@color/textColorBusinessRed"
        app:flaxValue="@{statement.podiumCameraPurchase}"
        app:flaxColor="@{flaxColor}"
        app:layout_constraintBottom_toBottomOf="@+id/title_flix_utilized"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintStart_toEndOf="@+id/title_flix_utilized"
        app:layout_constraintTop_toTopOf="@+id/title_flix_utilized"
        tools:text="@tools:sample/us_zipcodes" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title_others"
        style="@style/TextAppearance.Flashat.Business.Caption"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/activity_margin"
        android:text="@string/title_others"
        android:textColor="@{itemTextColor}"
        app:layout_constraintEnd_toStartOf="@id/gift_purchase_points"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="@id/title_flix_utilized"
        app:layout_constraintTop_toBottomOf="@id/title_flix_utilized"
        tools:text="@string/title_others" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/others"
        style="@style/TextAppearance.Flashat.Business.Label"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/activity_margin"
        android:textAlignment="textEnd"
        android:textColor="@color/textColorBusinessRed"
        app:flaxValue="@{statement.cancelledDeductions}"
        app:flaxColor="@{flaxColor}"
        app:layout_constraintBottom_toBottomOf="@+id/title_others"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintStart_toEndOf="@+id/title_others"
        app:layout_constraintTop_toTopOf="@+id/title_others"
        tools:text="@tools:sample/us_zipcodes" />



    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title_pending"
        style="@style/TextAppearance.Flashat.Business.Caption"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/activity_margin"
        android:text="@string/title_business_pending"
        android:textColor="@{itemTextColor}"
        app:layout_constraintEnd_toStartOf="@+id/pending_amount"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="@id/title_sent_flax"
        app:layout_constraintTop_toBottomOf="@id/title_others"
        tools:text="@string/title_business_pending" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/pending_amount"
        style="@style/TextAppearance.Flashat.Business.Label"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/activity_margin"
        android:textAlignment="textEnd"
        android:textColor="@color/textColorBusinessRed"
        app:flaxValue="@{statement.totalPendingPP}"
        app:flaxColor="@{flaxColor}"
        app:layout_constraintBottom_toBottomOf="@+id/title_pending"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintStart_toEndOf="@+id/title_pending"
        app:layout_constraintTop_toTopOf="@+id/title_pending"
        tools:text="@tools:sample/us_zipcodes" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title_withdrawn"
        style="@style/TextAppearance.Flashat.Business.Caption"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/activity_margin"
        android:text="@string/title_business_withdrawn"
        android:textColor="@{itemTextColor}"
        app:layout_constraintEnd_toStartOf="@+id/withdrawn_amount"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="@id/title_sent_flax"
        app:layout_constraintTop_toBottomOf="@id/title_pending"
        tools:text="@string/title_business_withdrawn" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/withdrawn_amount"
        style="@style/TextAppearance.Flashat.Business.Label"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/activity_margin"
        android:textAlignment="textEnd"
        android:textColor="@color/textColorBusinessRed"
        app:flaxValue="@{statement.grandTotalWithdrawn}"
        app:flaxColor="@{flaxColor}"
        app:layout_constraintBottom_toBottomOf="@+id/title_withdrawn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintStart_toEndOf="@+id/title_withdrawn"
        app:layout_constraintTop_toTopOf="@+id/title_withdrawn"
        tools:text="@tools:sample/us_zipcodes" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title_total_debit"
        style="@style/TextAppearance.Flashat.Subtitle2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/activity_margin"
        android:text="@string/_title_business_total_debit"
        android:textColor="@{headerTextColor}"
        app:layout_constraintEnd_toStartOf="@+id/total_debit"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="@id/title_sent_flax"
        app:layout_constraintTop_toBottomOf="@id/title_withdrawn"
        tools:text="@string/_title_business_total_debit" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/total_debit"
        style="@style/TextAppearance.Flashat.Business.Label"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/activity_margin"
        android:textAlignment="textEnd"
        android:textColor="@color/textColorBusinessRed"
        app:flaxValue="@{statement.grandTotalDebit}"
        app:flaxColor="@{flaxColor}"
        app:layout_constraintBottom_toBottomOf="@+id/title_total_debit"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintStart_toEndOf="@+id/title_total_debit"
        app:layout_constraintTop_toTopOf="@+id/title_total_debit"
        tools:text="@tools:sample/us_zipcodes" />


    <View
        android:id="@+id/balance_top_divider"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/activity_margin"
        android:background="@drawable/bg_business_horizontal_dotted_line"
        android:backgroundTint="@{itemTextColor}"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_total_debit" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title_available_balance"
        style="@style/TextAppearance.Flashat.Subtitle2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/activity_margin"
        android:layout_marginTop="@dimen/activity_margin"
        android:text="@string/title_business_available_balance"
        android:textColor="@{headerTextColor}"
        app:layout_constraintEnd_toStartOf="@+id/total_debit"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/balance_top_divider"
        tools:text="@string/title_business_available_balance" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/available_balance"
        style="@style/TextAppearance.Flashat.Subtitle1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/activity_margin"
        android:textAlignment="textEnd"
        android:textColor="@{itemTextColor}"
        app:flaxValue="@{statement.availableBalance}"
        app:flaxColor="@{flaxColor}"
        app:layout_constraintBottom_toBottomOf="@+id/title_available_balance"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintStart_toEndOf="@+id/title_available_balance"
        app:layout_constraintTop_toTopOf="@+id/title_available_balance"
        tools:text="@tools:sample/us_zipcodes" />

    <View
        android:id="@+id/balance_bottom_divider"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginVertical="@dimen/activity_margin"
        android:background="@drawable/bg_business_horizontal_dotted_line"
        android:backgroundTint="@{itemTextColor}"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_available_balance" />

</androidx.constraintlayout.widget.ConstraintLayout>

</layout>