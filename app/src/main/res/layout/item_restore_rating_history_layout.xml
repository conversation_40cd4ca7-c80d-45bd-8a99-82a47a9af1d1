<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="buyFlaxHistory"
            type="com.app.messej.data.model.BuyFlaxRates" />

        <variable
            name="viewModel"
            type="com.app.messej.ui.home.gift.FlaxPurchaseHistoryViewModel" />
        <import type="com.app.messej.data.model.enums.FlixPurchaseTypesTab"/>

        <variable
            name="content"
            type="String" />
    </data>


    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/line_spacing"
        android:layout_marginVertical="@dimen/line_spacing"
        app:cardBackgroundColor="@color/colorSurfaceSecondary"
        app:cardCornerRadius="@dimen/line_spacing"
        app:cardElevation="@dimen/line_spacing">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/image_person_photo"
                style="@style/TextAppearance.Flashat.Label.Small"
                android:layout_width="35dp"
                android:layout_height="35dp"
                android:layout_marginHorizontal="@dimen/element_spacing"
                android:layout_marginVertical="@dimen/element_spacing"
                android:background="@drawable/ic_flax_coin"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:background="@drawable/ic_flax_coin" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/purchasedName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toEndOf="@id/image_person_photo"
                app:layout_constraintTop_toTopOf="@id/image_person_photo"
                android:layout_marginHorizontal="@dimen/activity_margin">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/flax_amount"
                    style="@style/TextAppearance.Flashat.Subtitle1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{buyFlaxHistory.flax.toString()}"
                    android:textColor="@color/textColorPrimary"
                    app:flow_horizontalAlign="start"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintEnd_toStartOf="@id/coin"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="100" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/coin"
                    style="@style/TextAppearance.Flashat.Business.FlaxRate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/line_spacing"
                    android:text="@string/flax"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/flax_amount"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/activity_margin"
                app:layout_constraintBottom_toBottomOf="@id/image_person_photo"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/image_person_photo"
                app:layout_constraintTop_toBottomOf="@id/purchasedName">


                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/purchasedByName"
                    style="@style/TextAppearance.Flashat.Subtitle2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    app:flow_horizontalAlign="start"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/textview_flax_purchase_date"
                    app:layout_constraintTop_toTopOf="parent"
                    android:text="@{content}"
                    tools:text="Restored By sholinnnnnnnnnnnnnnnnnnn" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/textview_flax_purchase_date"
                    style="@style/TextAppearance.Flashat.Label.Small"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/element_spacing"
                    android:text="@{buyFlaxHistory.getParsedTime}"
                    android:textColor="@color/textColorSecondaryLight"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="20/10/2023 | 1.30" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</layout>