<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable name="speaker" type="com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel.ActiveSpeakerUIModel" />
        <variable name="citizenship" type="com.app.messej.data.model.enums.UserCitizenship" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/holder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        tools:showIn="@layout/item_podium_speaker">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/user_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/line_spacing"
            android:paddingVertical="1dp"
            android:textAppearance="@style/TextAppearance.Flashat.Podium.SpeakerTile"
            android:textColor="@color/textColorOnPrimary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/coins_received"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Manager" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/coins_received"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/line_spacing"
            android:paddingVertical="1dp"
            android:text="@{speaker.speaker.inactive?@string/common_percentage_value(speaker.speaker.userRatingPercent):speaker.coinsToDisplay}"
            android:textAlignment="viewEnd"
            app:goneIf="@{speaker.speaker.citizenship ==citizenship.GOLDEN}"
            android:textAppearance="@style/TextAppearance.Flashat.Label.Smaller"
            android:textColor="@color/textColorOnPrimary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/admin_icon"
            app:layout_constraintStart_toEndOf="@id/user_title"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/common_percentage_value" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/admin_icon"
            android:layout_width="9dp"
            android:layout_height="9dp"
            android:layout_marginEnd="@dimen/line_spacing"
            android:visibility="gone"
            app:goneIfNot="@{speaker.admin}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/bg_generic_circle"
            app:tint="@color/colorPodiumSpeakerAdmin"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>