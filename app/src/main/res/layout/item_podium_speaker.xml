<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    >
    <data>
        <import type="com.app.messej.data.model.enums.AssemblySpeakingStatus"/>
        <variable name="speaker" type="com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel.ActiveSpeakerUIModel" />
        <variable name="showControls" type="Boolean" />
        <variable name="showLikes" type="Boolean" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        tools:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:background="@color/colorSurface"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/user_dp_bg"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleType="centerCrop"
                app:imageUrl="@{speaker.speaker.thumbnail}"
                app:placeholder="@{@drawable/im_user_placeholder_square}"
                app:blurRadius="@{30}"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:foreground="#99000000"
                tools:src="@drawable/im_user_placeholder_square" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/speaker_tile_boarder"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:goneIfNot="@{speaker.currentlySpeaking}"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:elevation="@dimen/activity_margin"
                app:srcCompat="@drawable/bg_podium_speaking_square" />

            <include
                android:id="@+id/speaker_header"
                layout="@layout/item_podium_speaker_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:speaker="@{speaker}"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/user_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/line_spacing"
                android:ellipsize="end"
                android:maxLines="1"
                android:layout_marginBottom="6dp"
                android:text="@{speaker.speaker.name}"
                android:textAppearance="@style/TextAppearance.Flashat.Label.Smaller"
                android:textColor="@color/textColorOnPrimary"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="@+id/speaker_status_icons"
                app:layout_constraintEnd_toStartOf="@+id/speaker_status_icons"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                tools:text="John Doe" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/speaker_status_icons"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/mic_icon"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_margin="@dimen/line_spacing"
                    app:goneIfNot="@{speaker.online &amp;&amp; !showControls}"
                    tools:visibility="gone"
                    android:src="@{speaker.muted?@drawable/ic_podium_mic_off:@drawable/ic_podium_mic_on}"
                    tools:src="@drawable/ic_podium_mic_speaker_off"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/offline_icon"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_margin="@dimen/line_spacing"
                    app:goneIf="@{speaker.online}"
                    tools:visibility="visible"
                    android:src="@drawable/ic_podium_offline"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/user_bubble"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                app:layout_constraintHeight_max="100dp"
                android:layout_marginTop="6dp"
                android:layout_marginBottom="2dp"
                app:goneIf="@{showControls}"
                android:animateLayoutChanges="true"
                app:layout_constraintBottom_toTopOf="@+id/user_name"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/speaker_header">

                <com.makeramen.roundedimageview.RoundedImageView
                    android:id="@+id/user_dp"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:layout_margin="@dimen/line_spacing"
                    app:imageUrl="@{speaker.speaker.thumbnail}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="W,1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:placeholder="@{@drawable/im_user_placeholder_square}"
                    app:riv_oval="true"
                    tools:src="@drawable/im_user_placeholder_square" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:visibility="gone"
                    app:goneIfNot="@{speaker.currentlySpeaking}"
                    app:animateIfSpeaking="@{speaker.currentlySpeaking}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/bg_podium_speaking_circle"/>

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.25" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/premium_badge"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:adjustViewBounds="true"
                    android:elevation="4dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:localeAwareTranslationX="@{@dimen/line_spacing}"
                    android:translationY="@dimen/line_spacing"
                    app:layout_constraintEnd_toEndOf="@id/guideline"
                    app:userBadgeOnPrimary="@{speaker.speaker.userBadge}"
                    tools:src="@drawable/ic_user_badge_premium" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/chat_flag"
                android:layout_width="wrap_content"
                android:layout_height="10dp"
                android:adjustViewBounds="true"
                app:imageRes="@{speaker.countryFlag}"
                android:layout_margin="@dimen/line_spacing"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/speaker_header"
                tools:srcCompat="@drawable/flag_france" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_host"
                android:layout_width="wrap_content"
                android:layout_height="10dp"
                android:textAppearance="@style/TextAppearance.Flashat.Podium.SpeakerTile"
                android:adjustViewBounds="true"
                android:text="@string/podium_speaker_host"
                app:goneIfNot="@{speaker.manager}"
                app:orGoneIf="@{showLikes}"
                android:visibility="gone"
                tools:text="Host"
                android:textColor="@color/white"
                android:layout_margin="@dimen/line_spacing"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/speaker_header"
                tools:srcCompat="@drawable/flag_france" />

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_margin="3dp"
                android:background="@drawable/ic_podium_speaker_likes"
                android:textSize="7sp"
                tools:text="88"
                android:text="@{speaker.speaker.likesGivenByUser.toString()}"
                android:visibility="visible"
                app:goneIfNot="@{showLikes}"
                android:textAppearance="@style/TextAppearance.Flashat.Label.Tiny"
                android:textColor="@color/colorSecondary"
                android:textAlignment="center"
                android:paddingTop="4dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/speaker_header"/>

            <FrameLayout
                android:id="@+id/challengeDecoration"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toTopOf="@+id/user_name">

            </FrameLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_button_holder"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:goneIfNot="@{showControls}"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="@+id/user_dp_bg"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/mic_button"
                    style="@style/Widget.Flashat.MainScreenButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="2dp"
                    app:icon="@{speaker.muted?@drawable/ic_podium_mic_speaker_off:@drawable/ic_podium_mic_speaker_on}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:icon="@drawable/ic_podium_mic_speaker_on"
                    tools:visibility="visible" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/action_more"
                    style="@style/Widget.Flashat.MainScreenButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="2dp"
                    app:icon="@drawable/ic_caret_down_rounded"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/mic_button"
                    tools:icon="@drawable/ic_caret_down_rounded"
                    tools:visibility="visible" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/timer_holder"
                app:goneIfNot="@{speaker.showAssemblyTimer}"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:alpha=".9"
                android:background="@drawable/bg_podium_challenge_timer"
                android:padding="@dimen/element_spacing"
                android:paddingHorizontal="@dimen/element_spacing"
                android:paddingVertical="@dimen/line_spacing"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.464">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/timer"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{(speaker.assemblySpeakingStatus == AssemblySpeakingStatus.PAUSED)? @string/podium_assembly_paused : speaker.assemblyTimeRemaining.toString()}"
                    android:textAllCaps="true"
                    android:textAppearance="@style/TextAppearance.Flashat.Podium.Assembly.Timer"
                    android:textColor="@{(speaker.assemblySpeakingStatus == AssemblySpeakingStatus.WAITING)? @color/colorPodiumBoardTimer : ((speaker.assemblySpeakingStatus == AssemblySpeakingStatus.PAUSED) ? @color/colorError : @color/colorPodiumBoardTimer)}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="00:00" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>