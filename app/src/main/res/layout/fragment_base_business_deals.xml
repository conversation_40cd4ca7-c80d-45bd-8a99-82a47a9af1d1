<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="com.app.messej.ui.home.businesstab.BusinessDealsListViewModel" />
        <import type="com.app.messej.data.model.enums.TransactionTab" />
        <variable name="isResident" type="Boolean" />
        <variable name="isVisitor" type="Boolean" />
        <variable name="cardBackground" type="Integer" />
        <variable name="textColor" type="Integer" />
        <variable name="mainTextColor" type="Integer" />
        <variable name="viewAllTextColor" type="Integer" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.cardview.widget.CardView
            android:id="@+id/cardView_main"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            android:layout_marginVertical="4dp"
            app:cardBackgroundColor="@{cardBackground}"
            app:cardCornerRadius="10dp"
            app:cardElevation="5dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <include
                    android:id="@+id/deals_header_layout"
                    layout="@layout/layout_deals_header"
                    app:dealsViewModel="@{viewModel}"
                    app:isResident="@{isResident}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:mainTextColor="@{mainTextColor}"
                    app:textColor="@{textColor}"
                    app:cardBackground="@{cardBackground}"/>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layout_button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/activity_margin"
                    android:background="@{cardBackground}"
                    app:layout_constraintTop_toBottomOf="@id/deals_header_layout">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/element_spacing"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/bg_deals_icon_top_left" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/element_spacing"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:srcCompat="@drawable/bg_deals_icon_bottom_left" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/element_spacing"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/bg_deals_icon_top_right" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/element_spacing"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:srcCompat="@drawable/bg_deals_icon_bottom_right" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/button_buy_flix"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="@dimen/element_spacing"
                        android:clickable="true"
                        app:layout_constraintTop_toTopOf="parent">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/image_buy_flix"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/extra_margin"
                            android:layout_marginVertical="@dimen/element_spacing"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_buy_flix" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/text_buy_flix_now"
                            style="@style/TextAppearance.Flashat.Body1"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/activity_margin"
                            android:text="@string/title_buy_flix"
                            android:textColor="@{textColor}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/goto_buy_flix"
                            app:layout_constraintStart_toEndOf="@id/image_buy_flix"
                            app:layout_constraintTop_toTopOf="parent" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/goto_buy_flix"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/activity_margin"
                            android:tint="@{textColor}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_business_right_arrow" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="2px"
                            android:layout_marginHorizontal="@dimen/activity_margin"
                            android:background="@color/colorDividerLight"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/button_buy_coin"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="@dimen/element_spacing"
                        android:clickable="true"
                        app:layout_constraintTop_toBottomOf="@id/button_buy_flix">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/image_buy_coin"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/extra_margin"
                            android:layout_marginVertical="@dimen/element_spacing"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_coin" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/text_buy_coin_now"
                            style="@style/TextAppearance.Flashat.Body1"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/activity_margin"
                            android:text="@string/title_buy_coins"
                            android:textColor="@{textColor}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/goto_buy_coin"
                            app:layout_constraintStart_toEndOf="@id/image_buy_coin"
                            app:layout_constraintTop_toTopOf="parent" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/goto_buy_coin"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/activity_margin"
                            android:tint="@{textColor}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_business_right_arrow" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="2px"
                            android:layout_marginHorizontal="@dimen/activity_margin"
                            android:background="@color/colorDividerLight"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/button_send_flax"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/element_spacing"
                        android:clickable="true"
                        goneIf="@{isVisitor || viewModel.goldenUser}"
                        app:layout_constraintTop_toBottomOf="@id/button_buy_coin">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/image_send_flax"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/extra_margin"
                            android:layout_marginVertical="@dimen/element_spacing"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_flax_coin" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/text_send_flax"
                            style="@style/TextAppearance.Flashat.Body1"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/activity_margin"
                            android:text="@string/title_send_flax"
                            android:textColor="@{textColor}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/goto_send_flax"
                            app:layout_constraintStart_toEndOf="@id/image_send_flax"
                            app:layout_constraintTop_toTopOf="parent" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/goto_send_flax"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/activity_margin"
                            android:tint="@{textColor}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_business_right_arrow" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="2px"
                            android:layout_marginHorizontal="@dimen/activity_margin"
                            android:background="@color/colorDividerLight"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/button_con_coin_to_flax"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/element_spacing"
                        android:clickable="true"
                        app:layout_constraintTop_toBottomOf="@id/button_send_flax">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/image_con_coin_to_flax"
                            android:layout_width="40dp"
                            android:layout_height="wrap_content"
                            android:adjustViewBounds="true"
                            android:layout_marginHorizontal="@dimen/extra_margin"
                            android:layout_marginVertical="@dimen/element_spacing"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_coin_to_flax" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/text_con_coin_to_flax"
                            style="@style/TextAppearance.Flashat.Body1"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/activity_margin"
                            android:text="@string/gift_convert_coins_to_flax"
                            android:textColor="@{textColor}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/goto_con_coin_to_flax"
                            app:layout_constraintStart_toEndOf="@id/image_con_coin_to_flax"
                            app:layout_constraintTop_toTopOf="parent" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/goto_con_coin_to_flax"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/activity_margin"
                            android:tint="@{textColor}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_business_right_arrow" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="2px"
                            android:layout_marginHorizontal="@dimen/activity_margin"
                            android:background="@color/colorDividerLight"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/button_con_flax_to_coin"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/element_spacing"
                        android:clickable="true"
                        app:layout_constraintTop_toBottomOf="@id/button_con_coin_to_flax">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/image_con_flax_to_coin"
                            android:layout_width="40dp"
                            android:layout_height="wrap_content"
                            android:adjustViewBounds="true"
                            android:layout_marginHorizontal="@dimen/extra_margin"
                            android:layout_marginVertical="@dimen/element_spacing"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_flax_to_coin" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/text_con_flax_to_coin"
                            style="@style/TextAppearance.Flashat.Body1"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/activity_margin"
                            android:text="@string/gift_convert_flax_to_coins"
                            android:textColor="@{textColor}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/goto_con_flax_to_coin"
                            app:layout_constraintStart_toEndOf="@id/image_con_flax_to_coin"
                            app:layout_constraintTop_toTopOf="parent" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/goto_con_flax_to_coin"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/activity_margin"
                            android:tint="@{textColor}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_business_right_arrow" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="2px"
                            android:layout_marginHorizontal="@dimen/activity_margin"
                            android:background="@color/colorDividerLight"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent" />


                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/button_sell_flax"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/element_spacing"
                        goneIf="@{isVisitor}"
                        android:clickable="true"
                        app:layout_constraintTop_toBottomOf="@id/button_con_flax_to_coin">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/image_sell_flax"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/extra_margin"
                            android:layout_marginVertical="@dimen/element_spacing"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_sell_flix" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/text_sell_flax"
                            style="@style/TextAppearance.Flashat.Body1"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/activity_margin"
                            android:text="@string/title_withdraw_your_flax"
                            android:textColor="@{textColor}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/goto_sell_flax"
                            app:layout_constraintStart_toEndOf="@id/image_sell_flax"
                            app:layout_constraintTop_toTopOf="parent" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/goto_sell_flax"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/activity_margin"
                            android:tint="@{textColor}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_business_right_arrow" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="2px"
                            android:layout_marginHorizontal="@dimen/activity_margin"
                            android:background="@color/colorDividerLight"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/button_restore_ratings"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/element_spacing"
                       android:paddingBottom="@dimen/element_spacing"
                        android:clickable="true"
                        goneIf="@{isResident||isVisitor}"
                        app:layout_constraintTop_toBottomOf="@id/button_sell_flax">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/image_restore_ratings"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/extra_margin"
                            android:layout_marginVertical="@dimen/element_spacing"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_restore_rating" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/text_restore_ratings"
                            style="@style/TextAppearance.Flashat.Body1"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/activity_margin"
                            android:text="@string/restore_rating_header"
                            android:textColor="@{textColor}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/goto_restore_ratings"
                            app:layout_constraintStart_toEndOf="@id/image_restore_ratings"
                            app:layout_constraintTop_toTopOf="parent" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/goto_restore_ratings"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/activity_margin"
                            android:tint="@{textColor}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_business_right_arrow" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="2px"
                            android:layout_marginHorizontal="@dimen/activity_margin"
                            android:background="@color/colorDividerLight"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/button_transactions"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/element_spacing"
                        android:clickable="true"
                        app:layout_constraintTop_toBottomOf="@id/button_restore_ratings">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/image_transactions"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/extra_margin"
                            android:layout_marginVertical="@dimen/element_spacing"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_deals_transactions" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/text_restore_ratings1"
                            style="@style/TextAppearance.Flashat.Body1"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/activity_margin"
                            android:text="@string/transactions"
                            android:textColor="@{textColor}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/goto_transactions"
                            app:layout_constraintStart_toEndOf="@id/image_transactions"
                            app:layout_constraintTop_toTopOf="parent" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/goto_transactions"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/activity_margin"
                            android:tint="@{textColor}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_business_right_arrow" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="2px"
                            android:layout_marginHorizontal="@dimen/activity_margin"
                            android:background="@color/colorDividerLight"
                            android:visibility="invisible"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>



                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/upgrade_button"
                        style="@style/Widget.Flashat.MiniRoundedButton.CustomBG"
                        goneIfNot="@{isResident||isVisitor}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="@dimen/element_spacing"
                        android:layout_marginStart="@dimen/element_spacing"
                        android:layout_marginEnd="@dimen/activity_margin"
                        android:background="@drawable/bg_drawer_upgrade_button"
                        android:text="@string/common_upgrade_now"
                        android:visibility="gone"
                        android:textAllCaps="false"
                        app:icon="@drawable/ic_drawer_crown"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/button_transactions" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.cardview.widget.CardView>

<!--
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/transaction_layout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            goneIf="@{isVisitor}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cardView_main">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/textview_transactions"
                style="@style/TextAppearance.Flashat.Subtitle1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:layout_marginVertical="10dp"
                android:text="@string/transactions"
                android:textColor="@color/textColorPrimary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/textview_view_all"
                style="@style/TextAppearance.Flashat.Label.Small"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:layout_marginVertical="10dp"
                android:text="@string/title_business_view_all"
                android:textColor="@{viewAllTextColor}"
                android:visibility="gone"
                app:goneIf="@{viewModel.count==0}"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/layout_Transaction_tabs"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/video_trim_seekbar_corner_radius"
                android:clipToPadding="false"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/element_spacing"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/textview_transactions">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_Flax"
                    style="@style/Widget.Flashat.HomeInnerTabButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/line_spacing"
                    android:checkable="true"
                    android:checked="@{viewModel.currentTab==TransactionTab.TAB_FLAX}"
                    android:text="@string/flax"
                    android:textAllCaps="false" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_coins"
                    style="@style/Widget.Flashat.HomeInnerTabButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/line_spacing"
                    android:checkable="true"
                    android:checked="@{viewModel.currentTab==TransactionTab.TAB_COINS}"
                    android:text="@string/coins"
                    android:textAllCaps="false" />
            </androidx.appcompat.widget.LinearLayoutCompat>
        </androidx.constraintlayout.widget.ConstraintLayout>
-->

<!--
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/transaction_pager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            goneIf="@{isVisitor}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/transaction_layout" />
-->
    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>