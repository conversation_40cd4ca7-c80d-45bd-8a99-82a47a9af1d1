<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <import type="com.app.messej.data.model.enums.UserCitizenship" />

        <variable
            name="citizenship"
            type="com.app.messej.data.model.enums.UserCitizenship" />

        <variable
            name="profile"
            type="com.app.messej.data.model.CurrentUser.Profile" />

        <variable
            name="account"
            type="com.app.messej.data.model.api.profile.AccountDetailsResponse" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/profile_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/close_button"
            style="@style/Widget.Flashat.Button.TextButton.IconOnly.Small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/element_spacing"
            android:layout_marginEnd="@dimen/element_spacing"
            app:icon="@drawable/ic_close"
            app:iconSize="24dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:enabled="true" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/profile_holder"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/element_spacing"
            android:clickable="true"
            android:foreground="?attr/selectableItemBackground"
            android:minWidth="240dp"
            android:paddingBottom="@dimen/element_spacing"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/close_button">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_citizenship"
                style="@style/TextAppearance.Flashat.Subtitle1"
                android:layout_width="wrap_content"
                android:paddingVertical="@dimen/line_spacing"
                android:background="@drawable/bg_citizenship_left"
                android:layout_height="wrap_content"
                android:textAlignment="center"
                tools:text="Visitor"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                />

            <com.makeramen.roundedimageview.RoundedImageView
                android:id="@+id/superstar_dp"
                android:layout_width="@dimen/drawer_header_dp_size"
                android:layout_height="@dimen/drawer_header_dp_size"
                android:layout_marginStart="@dimen/extra_margin"
                android:layout_marginTop="@dimen/activity_margin"
                android:elevation="2dp"
                android:scaleType="centerCrop"
                app:imageUrl="@{profile.thumbnail}"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/text_citizenship"
                app:placeholder="@{@drawable/im_user_placeholder_opaque}"
                app:riv_corner_radius="@dimen/drawer_header_dp_size"
                tools:src="@color/textColorHint" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/welcomeText"
                style="@style/TextAppearance.Flashat.Label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/activity_margin"
                android:text="@string/drawer_header_welcome"
                android:textColor="@color/colorPrimary"
                app:layout_constraintBottom_toTopOf="@id/superstar_name"
                app:layout_constraintEnd_toStartOf="@id/upgrade_button"
                app:layout_constraintStart_toEndOf="@id/superstar_dp"
                app:layout_constraintTop_toTopOf="@id/superstar_dp"
                app:layout_constraintVertical_chainStyle="packed" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/superstar_name"
                style="@style/TextAppearance.Flashat.Headline6"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/line_spacing"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="@{profile.name}"
                android:textColor="@color/textColorSecondary"
                app:layout_constraintBottom_toBottomOf="@+id/superstar_dp"
                app:layout_constraintEnd_toStartOf="@id/upgrade_button"
                app:layout_constraintStart_toStartOf="@+id/welcomeText"
                app:layout_constraintTop_toBottomOf="@+id/welcomeText"
                tools:text="Mathew Varghese Puthiyaparambil" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/upgrade_button"
                style="@style/Widget.Flashat.MiniRoundedButton.CustomBG"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/element_spacing"
                android:layout_marginEnd="@dimen/activity_margin"
                android:background="@drawable/bg_drawer_upgrade_button"
                android:text="@string/common_upgrade_now"
                android:textAllCaps="false"
                app:icon="@drawable/ic_drawer_crown"
                app:layout_constraintBottom_toBottomOf="@+id/superstar_dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/superstar_name"
                app:layout_constraintTop_toTopOf="@+id/superstar_dp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/upgrade_prompt"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/activity_margin"
                android:layout_marginTop="@dimen/activity_margin"
                android:layout_marginEnd="@dimen/activity_margin"
                android:text="@string/home_drawer_upgrade_prompt"
                android:textAppearance="@style/TextAppearance.Flashat.Label.Small"
                android:textColor="@color/textColorSecondaryLight"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/superstar_dp"
                app:layout_constraintTop_toBottomOf="@+id/superstar_dp" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_rating_progress"
            android:layout_width="match_parent"
            app:layout_constraintTop_toBottomOf="@id/view"
            android:layout_marginTop="@dimen/line_spacing"
            android:layout_marginHorizontal="11dp"
            goneIfNot="@{account.haveSocialBalance}"
            android:layout_height="wrap_content">

            <com.google.android.material.progressindicator.LinearProgressIndicator
                android:id="@+id/progress_rating"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:trackCornerRadius="@dimen/line_spacing"
                android:max="100"
                app:trackThickness="6dp"
                android:progress="@{account.flaxRatePercentage}"
                app:indicatorColor="@color/colorSecondary"
                app:trackColor="@color/colorAlwaysLightPrimaryLighter"
                app:layout_constraintTop_toTopOf="parent"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_rating"
                style="@style/TextAppearance.Flashat.Body2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/line_spacing"
                android:text="@string/id_card_rating"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/progress_rating"
                android:textColor="@color/textColorPrimary" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_rating_percentage"
                style="@style/TextAppearance.Flashat.Body2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/line_spacing"
                tools:text="73%"
                android:text="@{@string/common_percentage_value(account.flaxRatePercentage == null ? 0 : account.flaxRatePercentage)}"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/progress_rating"
                android:textColor="@color/textColorPrimary" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_flaxBalance"
            goneIfNot="@{account.citizenship==UserCitizenship.RESIDENT || account.citizenship==UserCitizenship.VISITOR}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/element_spacing"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_rating_progress">

            <LinearLayout
                android:layout_width="match_parent"
                android:baselineAligned="false"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/element_spacing"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_height="wrap_content">

                <include
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    app:goneIf="@{account.haveSocialBalance}"
                    app:firstText="@{@string/id_card_rating}"
                    app:count="@{@string/common_percentage_value(account.flaxRatePercentage)}"
                    app:isVisitor="@{true}"
                    layout="@layout/item_settings_bottom_sheet_rounded_view"/>

                <include
                    android:id="@+id/layout_flix_balance"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    app:firstText="@{account.haveSocialBalance ? @string/drawer_header_primary_flix_balance : @string/drawer_header_pp_balance}"
                    app:count="@{account.activePointsString}"
                    app:isVisitor="@{true}"
                    android:layout_weight="1"
                    layout="@layout/item_settings_bottom_sheet_rounded_view"/>

                <include
                    android:id="@+id/layout_social_balance"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    app:goneIfNot="@{account.haveSocialBalance}"
                    app:count="@{account.socialBalanceString}"
                    app:firstText="@{@string/common_social_support_balance}"
                    app:isVisitor="@{true}"
                    layout="@layout/item_settings_bottom_sheet_rounded_view"/>


            </LinearLayout>


<!--            <LinearLayout-->
<!--                android:layout_width="0dp"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:gravity="center_horizontal|center_vertical"-->
<!--                app:layout_constraintStart_toStartOf="parent"-->
<!--                app:layout_constraintEnd_toEndOf="parent"-->
<!--                app:layout_constraintTop_toTopOf="parent"-->
<!--                app:layout_constraintBottom_toBottomOf="parent">-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/text_flax_balance"-->
<!--                    style="@style/TextAppearance.Flashat.Body2"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:clickable="true"-->
<!--                    android:text="@string/drawer_header_pp_balance" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/text_flax_points"-->
<!--                    style="@style/TextAppearance.Flashat.Headline4"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    tools:text="848"-->
<!--                    android:layout_marginStart="@dimen/activity_margin"-->
<!--                    android:text="@{account.activePointsString}" />-->

<!--            </LinearLayout>-->
        </androidx.constraintlayout.widget.ConstraintLayout>


        <com.google.android.material.button.MaterialButton
            android:id="@+id/followers_button"
            style="@style/Widget.Flashat.DrawerHeaderButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="32dp"
            android:layout_marginTop="@dimen/activity_margin"
            android:layout_marginBottom="@dimen/activity_margin"
            android:backgroundTint="@null"
            android:paddingEnd="@dimen/activity_margin"
            android:text="@string/home_private_tab_followers"
            android:textAllCaps="false"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/following_button"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_flaxBalance" />

        <com.google.android.material.button.MaterialButton

            android:id="@+id/following_button"
            style="@style/Widget.Flashat.DrawerHeaderButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="32dp"
            android:paddingEnd="@dimen/activity_margin"
            android:text="@string/home_private_tab_following"
            android:textAllCaps="false"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="@+id/followers_button"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/followers_button"
            app:layout_constraintTop_toTopOf="@+id/followers_button"
            app:layout_constraintVertical_bias="0.0" />


        <View
            android:id="@+id/view"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            goneIf="@{account.haveSocialBalance}"
            android:background="@color/colorDividerLight"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/profile_holder" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>