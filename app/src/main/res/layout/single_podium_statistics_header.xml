<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="headerText"
            type="String" />

        <variable
            name="userIconTint"
            type="Integer" />
        <variable
            name="bgColor"
            type="Integer" />
        <variable
            name="podiumName"
            type="String" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/line_spacing"
        android:padding="2dp">
        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guide_start"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.3"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/header"
            style="@style/TextAppearance.Flashat.Label.Small.Bold"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@{headerText}"
            android:textColor="@color/colorPrimaryColorDarkest1"
            android:layout_marginHorizontal="@dimen/element_spacing"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="@id/guide_start"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Today" />

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_header"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:cardCornerRadius="@dimen/element_spacing"
            app:cardElevation="@dimen/element_spacing"
            android:layout_marginTop="@dimen/line_spacing"
            android:layout_marginStart="@dimen/element_spacing"
            app:layout_constraintStart_toEndOf="@id/guide_start"
            app:layout_constraintBottom_toBottomOf="@id/header"
            app:layout_constraintTop_toTopOf="@id/header"
            app:layout_constraintEnd_toEndOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="2dp"
                android:background="@{bgColor}"
                tools:background="@color/colorHuddleAmbassadorBackground">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/img_podium"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_single_podium_icon"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@{userIconTint}" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_title_other"
                    style="@style/TextAppearance.Flashat.Label.Tiny.Bold"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@{podiumName}"
                    tools:text="@string/let_the_game_beginss"
                    android:layout_marginStart="@dimen/line_spacing"
                    android:textColor="@{userIconTint}"
                    app:layout_constraintBottom_toBottomOf="@id/img_podium"
                    app:layout_constraintStart_toEndOf="@id/img_podium"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/img_podium"
                    tools:textColor="@color/colorPrimary" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>