<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <import type="com.app.messej.data.model.enums.PodiumKind"/>
        <variable name="viewModel" type="com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel" />
    </data>

    <com.kennyc.view.MultiStateView
        android:id="@+id/podium_header_multiStateView"
        android:background="@color/colorPrimary"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:msv_loadingView="@layout/layout_eds_state_loading_podium_header"
        app:msv_viewState="content"
        tools:showIn="@layout/fragment_podium_live_lecture"
        app:applySystemBarInsets="@{`top`}" >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/podium_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">


<!--        <androidx.constraintlayout.widget.ConstraintLayout-->
<!--            android:id="@+id/podium_header_views"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            app:layout_constraintStart_toStartOf="parent"-->
<!--            app:layout_constraintTop_toTopOf="parent">-->

            <com.makeramen.roundedimageview.RoundedImageView
                android:id="@+id/podium_dp"
                android:layout_width="54dp"
                android:layout_height="54dp"
                android:layout_margin="12sp"
                android:scaleType="centerCrop"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:imageUrl="@{viewModel.podium.profileDp}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0"
                app:placeholder="@{@drawable/im_podium_placeholder}"
                app:riv_corner_radius="10dp"
                tools:src="@drawable/im_podium_placeholder" />

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:padding="3dp"
                android:layout_marginTop="-5dp"
                android:layout_marginStart="-5dp"
                app:tint="@color/colorPrimary"
                android:background="@drawable/bg_podium_dp_icon"
                app:layout_constraintStart_toStartOf="@id/podium_dp"
                app:layout_constraintTop_toTopOf="@id/podium_dp"
                app:srcCompat="@drawable/ic_drawer_settings" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/podium_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/activity_margin"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@{viewModel.podium.name}"
                android:textAppearance="@style/TextAppearance.Flashat.Headline5"
                android:textColor="@color/textColorOnPrimary"
                app:layout_constraintBottom_toTopOf="@+id/counter_holder"
                app:layout_constraintEnd_toStartOf="@+id/yalla_btn"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/podium_dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed"
                tools:text="Let the Games Begin" />


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/counter_holder"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/line_spacing"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/yalla_btn"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="@+id/podium_name"
                app:layout_constraintTop_toBottomOf="@+id/podium_name">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/duration_counter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_podium_stat_chip"
                    android:paddingHorizontal="@dimen/element_spacing"
                    android:paddingTop="2dp"
                    android:paddingBottom="2dp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/dur_img"
                        android:layout_width="14dp"
                        android:layout_height="wrap_content"
                        android:adjustViewBounds="true"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/ic_access_time"
                        app:tint="@color/colorSecondary" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/line_spacing"
                        android:text="@{viewModel.liveTimer}"
                        android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                        android:textColor="@color/textColorOnPrimary"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/dur_img"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="00:25" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/live_counter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:background="@drawable/bg_podium_stat_chip"
                    android:paddingHorizontal="@dimen/element_spacing"
                    android:paddingTop="2dp"
                    android:paddingBottom="2dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/duration_counter"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_goneMarginStart="0dp">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/live_img"
                        android:layout_width="10dp"
                        android:layout_height="wrap_content"
                        android:adjustViewBounds="true"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/bg_generic_circle"
                        app:tint="@color/colorPass" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/line_spacing"
                        android:text="@{viewModel.podium.liveUsersFormatted}"
                        android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                        android:textColor="@color/textColorOnPrimary"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/live_img"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="123" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/like_counter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:background="@drawable/bg_podium_stat_chip"
                    android:paddingHorizontal="@dimen/element_spacing"
                    app:goneIfNot="@{viewModel.isLikeCountVisible}"
                    android:paddingTop="2dp"
                    android:paddingBottom="2dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/gift_counter"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/like_img"
                        android:layout_width="14dp"
                        android:layout_height="wrap_content"
                        android:adjustViewBounds="true"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/ic_podium_liked"
                        app:tint="@color/textColorOnPrimary" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/line_spacing"
                        android:text="@{viewModel.podium.likesFormatted}"
                        android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                        android:textColor="@color/textColorOnPrimary"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/like_img"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="12K" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/gift_counter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:background="@drawable/bg_podium_stat_chip"
                    android:paddingHorizontal="@dimen/element_spacing"
                    android:paddingTop="2dp"
                    app:goneIfNot="@{viewModel.isGiftCountVisible}"
                    tools:visibility="visible"
                    android:paddingBottom="2dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/live_counter"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_goneMarginStart="0dp">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/gift_img"
                        android:layout_width="14dp"
                        android:layout_height="14dp"
                        android:adjustViewBounds="true"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/yellow_gift" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/line_spacing"
                        android:text="@{viewModel.managerReceivedCoins}"
                        android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                        android:textColor="@color/textColorOnPrimary"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/gift_img"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="20K" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/coin_counter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:background="@drawable/bg_podium_stat_chip"
                    android:paddingHorizontal="@dimen/element_spacing"
                    android:paddingTop="2dp"
                    android:paddingBottom="2dp"
                    app:goneIfNot="@{viewModel.isCoinCountVisible}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/like_counter"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/coin_img"
                        android:layout_width="14dp"
                        android:layout_height="wrap_content"
                        android:adjustViewBounds="true"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/ic_podium_coin_count"
                        app:tint="@color/colorSecondary" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/line_spacing"
                        android:text="@{viewModel.podium.totalCoinsFormatted}"
                        android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                        android:textColor="@color/textColorOnPrimary"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/coin_img"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="200" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/yalla_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:goneIfNot="@{viewModel.podium.hasYallaGuys}"
                android:foreground="?attr/selectableItemBackground"
                android:alpha="@{viewModel.podium.allowYallaGuys==false?0.5f:1.0f}"
                tools:alpha="1.0"
                android:paddingHorizontal="@dimen/line_spacing"
                android:paddingVertical="@dimen/element_spacing"
                android:layout_marginEnd="@dimen/element_spacing"
                app:layout_constraintStart_toEndOf="@id/podium_name"
                app:layout_constraintEnd_toStartOf="@id/ic_share"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/yalla_ic"
                    android:layout_width="50dp"
                    android:layout_height="wrap_content"
                    android:adjustViewBounds="true"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_yalla_gamepad" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAppearance="@style/TextAppearance.Flashat.Body2.Bold"
                    tools:text="12"
                    android:text="@{viewModel.yallaGuysCount}"
                    android:textColor="@color/colorSecondary"
                    app:layout_constraintTop_toBottomOf="@id/yalla_ic"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ic_share"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:adjustViewBounds="true"
                app:goneIfNot="@{viewModel.podium.actionBarShareIconVisible}"
                android:layout_marginHorizontal="@dimen/element_spacing"
                android:foreground="?attr/selectableItemBackground"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/yalla_btn"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:tint="@color/colorSecondary"
                app:srcCompat="@drawable/ic_share_filled" />

        </androidx.constraintlayout.widget.ConstraintLayout>
<!--        </androidx.constraintlayout.widget.ConstraintLayout>-->

    </com.kennyc.view.MultiStateView>
</layout>