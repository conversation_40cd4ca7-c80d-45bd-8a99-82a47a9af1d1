<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="speaker"
            type="com.app.messej.data.model.api.podium.PodiumSpeaker" />

        <variable
            name="canSwitch"
            type="Boolean" />

        <variable
            name="canShare"
            type="Boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorSurface">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/scrollViewPlayerStats"
            android:layout_width="match_parent"
            android:layout_height="500dp"
            app:layout_constraintBottom_toTopOf="@id/layoutMaidanStatsButtons"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.compose.ui.platform.ComposeView
                android:id="@+id/maidan_player_stats_compose_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </androidx.core.widget.NestedScrollView>

        <LinearLayout
            android:id="@+id/layoutMaidanStatsButtons"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="100dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/scrollViewPlayerStats">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:foreground="?attr/selectableItemBackground"
                app:goneIfNot="@{canShare}">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnExit"
                    style="@style/Widget.Flashat.MediumRoundedButton.Inverse"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/activity_margin"
                    android:paddingHorizontal="@dimen/double_margin"
                    android:text="@string/common_exit"
                    app:icon="@drawable/ic_maidan_exit"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/btnShare"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnShare"
                    style="@style/Widget.Flashat.MediumRoundedButton.Primary"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/activity_margin"
                    android:paddingHorizontal="@dimen/double_margin"
                    android:text="@string/common_share"
                    app:icon="@drawable/ic_share_filled"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/btnExit"
                    app:layout_constraintTop_toTopOf="parent" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_switch"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIfNot="@{canSwitch}">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSwitch"
                    style="@style/Widget.Flashat.MediumRoundedButton.Primary"
                    android:layout_width="0dp"
                    app:goneIfNot="@{canSwitch}"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/activity_margin"
                    android:text="@string/podium_maidan_switch"
                    app:icon="@drawable/ic_podium_maidan_switch"
                    app:iconSize="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
