<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="com.app.messej.ui.home.publictab.authorities.stateAffairs.StateAffairsMainViewModel" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true">

        <include
            android:id="@+id/custom_action_bar"
            layout="@layout/item_custom_action_bar_rating"
            tools:visibility="gone" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/card_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:id="@+id/president_layout"
                        app:layout_constraintTop_toTopOf="parent">
                        <include
                            android:id="@+id/president_card"
                            layout="@layout/item_user_id_card_common_view"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/line_spacing"
                            app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:id="@+id/president__empty_layout"
                        app:layout_constraintTop_toTopOf="parent">
                    <include
                        android:id="@+id/president_card_empty"
                        layout="@layout/layout_empty_president"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/line_spacing"
                        app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>


                <include
                    android:id="@+id/strongest_tribes"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    layout="@layout/single_state_affairs_item"
                    app:isList="@{true}"
                    app:headerText="@{@string/state_affair_header_strongest_tribes}"
                    app:headerTextColor="@{@color/colorStrongestTribes}"
                    app:layout_constraintTop_toBottomOf="@id/card_layout"/>

                <include
                    android:id="@+id/flashat_ministers"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isList="@{true}"
                    layout="@layout/single_state_affairs_item"
                    app:headerText="@{@string/state_affair_header_flashat_ministers(viewModel.ministerCount.toString())}"
                    app:headerTextColor="@{@color/colorFlashatMinisters}"
                    app:layout_constraintTop_toBottomOf="@id/strongest_tribes"/>

                <include
                    android:id="@+id/flashat_ambassadors"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isList="@{true}"
                    layout="@layout/single_state_affairs_item"
                    app:headerText="@{@string/state_affair_header_flashat_ambassadors(viewModel.ambassadorCount.toString())}"
                    app:headerTextColor="@{@color/colorFlashatAmbassadors}"
                    app:layout_constraintTop_toBottomOf="@id/flashat_ministers"/>

                <include
                    android:id="@+id/flashat_officers"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isList="@{true}"
                    layout="@layout/single_state_affairs_item"
                    app:headerText="@{@string/state_affair_header_flashat_officers(viewModel.officerCount.toString())}"
                    app:headerTextColor="@{@color/colorFlashatOfficers}"
                    app:layout_constraintTop_toBottomOf="@id/flashat_ambassadors"/>

                <include
                    android:id="@+id/most_generous"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isList="@{true}"
                    layout="@layout/single_state_affairs_item"
                    app:headerText="@{@string/state_affair_header_most_generous_flashaters}"
                    app:headerTextColor="@{@color/textColorPrimary}"
                    app:layout_constraintTop_toBottomOf="@id/flashat_officers"/>

                <include
                    android:id="@+id/most_popular"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isList="@{true}"
                    layout="@layout/single_state_affairs_item"
                    app:headerText="@{@string/state_affair_header_most_popular_flashaters}"
                    app:headerTextColor="@{@color/textColorPrimary}"
                    app:layout_constraintTop_toBottomOf="@id/most_generous"/>

                <include
                    android:id="@+id/most_skillFull"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isList="@{true}"
                    layout="@layout/single_state_affairs_item"
                    app:headerText="@{@string/state_affair_header_most_skillfull_flashaters}"
                    app:headerTextColor="@{@color/textColorPrimary}"
                    app:layout_constraintTop_toBottomOf="@id/most_popular"/>

                <include
                    android:id="@+id/state_statistics"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isList="@{false}"
                    layout="@layout/single_state_affairs_item"
                    app:headerText="@{@string/state_affair_header_flashat_state_statistics}"
                    app:headerTextColor="@{@color/textColorPrimary}"
                    app:layout_constraintTop_toBottomOf="@id/most_skillFull"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>


    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>