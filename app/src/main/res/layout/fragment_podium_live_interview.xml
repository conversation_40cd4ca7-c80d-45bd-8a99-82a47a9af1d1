<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable name="viewModel" type="com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/main_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:applySystemBarInsets="@{`ime`}"
                android:background="@color/colorSurface">

                <include layout="@layout/layout_podium_header"
                    android:id="@+id/header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:viewModel="@{viewModel}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.compose.ui.platform.ComposeView
                    android:id="@+id/ticker"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:composableName="com.app.messej.ui.home.promobar.PromoBarKt.PromoBarPreviewSingle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/header"/>

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:id="@+id/action_decor_holder_top"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/element_spacing"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ticker">

                </androidx.appcompat.widget.LinearLayoutCompat>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/speaker_section"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/action_decor_holder_top">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/speakers_group"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        app:layout_constraintDimensionRatio="h,6:5"
                        tools:translationX="0dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent">

                        <androidx.constraintlayout.widget.Guideline
                            android:id="@+id/speaker_split_line"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            app:layout_constraintGuide_percent="0.5" />

                        <include
                            android:id="@+id/main_screen"
                            layout="@layout/item_podium_speaker_main"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            tools:visibility="visible"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@+id/speaker_split_line" />

                        <include
                            android:id="@+id/main_screen_two"
                            layout="@layout/item_podium_speaker_main"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            tools:visibility="visible"
                            app:iAmManager="@{viewModel.iAmManager}"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/speaker_split_line" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.kennyc.view.MultiStateView
                    android:id="@+id/live_chat_multiStateView"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toTopOf="@id/footer"
                    app:layout_constraintEnd_toStartOf="@+id/actions_holder"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/speaker_section"
                    app:msv_emptyView="@layout/layout_list_state_empty"
                    app:msv_errorView="@layout/layout_list_state_error"
                    app:msv_viewState="content">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/live_chat"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="@dimen/line_spacing"
                        tools:itemCount="6"
                        tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:listitem="@layout/item_podium_live_chat"/>

                </com.kennyc.view.MultiStateView>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/actions_holder"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintVertical_bias="1.0"
                    app:layout_constraintBottom_toTopOf="@id/footer"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:animateLayoutChanges="true">

                    <LinearLayout
                        android:id="@+id/actions"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingBottom="@dimen/element_spacing"
                        android:clipToPadding="false"
                        android:orientation="vertical"
                        android:gravity="end"
                        app:layout_constraintVertical_bias="1.0"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/action_share"
                            style="@style/Widget.Flashat.Podium.ActionFAB.Secondary"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginVertical="@dimen/line_spacing"
                            android:layout_marginHorizontal="@dimen/element_spacing"
                            android:enabled="@{!viewModel.flashatAnthem.playing}"
                            tools:visibility="visible"
                            app:icon="@drawable/ic_podium_share"/>
                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <include
                    android:id="@+id/likes_container"
                    layout="@layout/item_podium_likes_container" />

                <androidx.compose.ui.platform.ComposeView
                    android:id="@+id/anthem_overlay"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/speaker_section"
                    app:layout_constraintBottom_toBottomOf="@+id/speaker_section"
                    android:background="@color/colorPodiumAnthemBG"
                    tools:visibility="visible" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/footer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent">

                    <com.kennyc.view.MultiStateView
                        android:id="@+id/chat_textBox_multiStateView"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:background="@color/colorSurface"
                        android:paddingTop="@dimen/activity_margin"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/like_container"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:msv_emptyView="@layout/layout_podium_chat_paused_empty"
                        app:msv_errorView="@layout/layout_list_state_error"
                        app:msv_viewState="content"
                        tools:visibility="visible">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/chat_input_holder"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:elevation="6dp"
                            app:applySystemBarInsets="@{`bottom`}"
                            app:goneIfNot="@{viewModel.canSendChats}">

                            <com.google.android.material.textfield.TextInputLayout
                                android:id="@+id/chat_input"
                                style="@style/Widget.Flashat.GreyTextInput.Small"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/element_spacing"
                                android:layout_marginEnd="@dimen/line_spacing"
                                app:counterEnabled="true"
                                app:counterMaxLength="150"
                                app:hintAnimationEnabled="false"
                                app:hintEnabled="false"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toStartOf="@+id/chat_send_button"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/input_comment"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:hint="@string/chat_input_hint"
                                    android:inputType="textShortMessage|textMultiLine"
                                    android:longClickable="false"
                                    android:maxLength="150"
                                    android:maxLines="1"
                                    android:text="@={viewModel.chatText}"
                                    android:textIsSelectable="false" />

                            </com.google.android.material.textfield.TextInputLayout>

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/chat_send_button"
                                style="@style/Widget.Flashat.Button.TextButton.IconOnly.Small"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:enabled="@{viewModel.chatText.trim().length>0}"
                                app:icon="@drawable/ic_chat_send"
                                app:iconTint="@color/colorPrimary"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@id/chat_input"
                                app:layout_constraintTop_toTopOf="@id/chat_input" />
                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.kennyc.view.MultiStateView>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/like_container"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@color/colorSurface"
                        android:clipChildren="false"
                        android:clipToPadding="false"
                        android:paddingTop="@dimen/line_spacing"
                        android:paddingBottom="@dimen/element_spacing"
                        app:applySystemBarInsets="@{`bottom`}"
                        app:goneIfNot="@{viewModel.showLikeAction}"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/chat_textBox_multiStateView"
                        app:layout_constraintTop_toTopOf="parent">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/action_like"
                            style="@style/Widget.Flashat.PaidLikeButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:enabled="@{!viewModel.likeButtonEnabled}"
                            app:icon="@{viewModel.podium.likesDisabled?@drawable/ic_podium_like_disable:@drawable/ic_podium_like}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:icon="@drawable/ic_podium_like" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>
