<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable name="viewModel" type="com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel" />

        <variable name="hasChallengeOverlay" type="Boolean" />
        <variable name="speakersCollapsed" type="Boolean" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/main_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:applySystemBarInsets="@{`ime|bottom`}"
                android:background="@color/colorSurface">

                <include layout="@layout/layout_podium_header"
                    android:id="@+id/header"
                    app:viewModel="@{viewModel}"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.compose.ui.platform.ComposeView
                    android:id="@+id/ticker"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:composableName="com.app.messej.ui.home.promobar.PromoBarKt.PromoBarPreviewSingle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/header"/>

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:id="@+id/action_decor_holder_top"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingTop="2dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ticker">

                </androidx.appcompat.widget.LinearLayoutCompat>

                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/speaker_top_barrier"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:barrierAllowsGoneWidgets="true"
                    app:barrierDirection="bottom"
                    app:constraint_referenced_ids="action_decor_holder_top"/>

                <androidx.compose.ui.platform.ComposeView
                    android:id="@+id/spekers_compose_view"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintDimensionRatio="1:1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/speaker_top_barrier"
                    app:layout_constraintBottom_toTopOf="@id/speaker_bottom_barrier"/>

<!--                <androidx.constraintlayout.widget.Guideline-->
<!--                    android:id="@+id/speaker_split_line"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    app:layout_constraintGuide_percent="0.65"-->
<!--                    android:orientation="horizontal"/>-->

                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/speaker_bottom_barrier"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:barrierAllowsGoneWidgets="true"
                    app:barrierDirection="bottom"
                    app:constraint_referenced_ids="spekers_compose_view"/>

                <com.kennyc.view.MultiStateView
                    android:id="@+id/live_chat_multiStateView"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toTopOf="@id/footer"
                    app:layout_constraintEnd_toStartOf="@id/top_gifters_holder"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/speaker_bottom_barrier"
                    app:msv_emptyView="@layout/layout_list_state_empty"
                    app:msv_errorView="@layout/layout_list_state_error"
                    app:msv_viewState="content">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/live_chat"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="@dimen/line_spacing"
                        tools:itemCount="6"
                        tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:listitem="@layout/item_podium_live_chat"/>
                </com.kennyc.view.MultiStateView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/top_gifters_holder"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_marginTop="@dimen/line_spacing"
                    android:layout_marginEnd="@dimen/line_spacing"
                    android:layout_marginBottom="@dimen/line_spacing"
                    app:cardElevation="0dp"
                    app:cardCornerRadius="4dp"
                    app:cardBackgroundColor="@color/colorSurfaceSecondaryDark"
                    app:goneIf="@{viewModel.challengeActive}"
                    app:layout_constraintBottom_toTopOf="@+id/footer"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/speaker_bottom_barrier">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent">

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_view_top_gifters"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/line_spacing"
                            app:cardBackgroundColor="@color/colorSurfaceSecondaryDarker"
                            app:cardElevation="0dp"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_margin="@dimen/element_spacing"
                                android:text="@string/podium_gifters_title"
                                android:textAlignment="center"
                                android:textAllCaps="true"
                                android:textAppearance="@style/TextAppearance.Flashat.Label.Smaller.Bold"
                                android:textColor="@color/colorPrimary" />

                        </com.google.android.material.card.MaterialCardView>

                        <com.kennyc.view.MultiStateView
                            android:id="@+id/top_gifters_multiStateView"
                            android:layout_width="match_parent"
                            android:layout_height="0dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/card_view_top_gifters"
                            app:msv_emptyView="@layout/layout_eds_empty_podium_birthday_top_gifters"
                            app:msv_errorView="@layout/layout_list_state_error"
                            app:msv_loadingView="@layout/layout_eds_state_loading_podium_waiters"
                            app:msv_viewState="content">

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/top_gifters"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:minWidth="64dp"
                                android:orientation="vertical"
                                tools:itemCount="4"
                                tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                                tools:listitem="@layout/item_podium_birthday_top_gifters" />
                        </com.kennyc.view.MultiStateView>

                    </androidx.constraintlayout.widget.ConstraintLayout>


                </com.google.android.material.card.MaterialCardView>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/chat_overlay"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:visibility="visible"
                    app:layout_constraintEnd_toStartOf="@id/top_gifters_holder"
                    app:layout_constraintBottom_toBottomOf="@id/live_chat_multiStateView"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/live_chat_multiStateView" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/footer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/colorSurface"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent">

                    <com.kennyc.view.MultiStateView
                        android:id="@+id/chat_textBox_multiStateView"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:background="@color/colorSurface"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/gift_container"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:msv_emptyView="@layout/layout_podium_chat_paused_empty"
                        app:msv_errorView="@layout/layout_list_state_error"
                        app:msv_viewState="content">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/chat_input_holder"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:elevation="6dp"
                            app:goneIfNot="@{viewModel.canSendChats}">

                            <com.google.android.material.textfield.TextInputLayout
                                android:id="@+id/chat_input"
                                style="@style/Widget.Flashat.GreyTextInput"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/element_spacing"
                                android:layout_marginEnd="@dimen/line_spacing"
                                app:hintAnimationEnabled="false"
                                app:hintEnabled="false"
                                app:counterEnabled="true"
                                app:counterMaxLength="150"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toStartOf="@+id/chat_send_button"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/input_comment"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:hint="@string/chat_input_hint"
                                    android:inputType="textShortMessage|textMultiLine"
                                    android:focusableInTouchMode="@{!viewModel.challengeRunning &amp;&amp; viewModel.canCommentByUserRating}"
                                    android:clickable="true"
                                    android:maxLines="1"
                                    android:maxLength="150"
                                    android:text="@={viewModel.chatText}"
                                    android:longClickable="false"
                                    android:textIsSelectable="false"/>

                            </com.google.android.material.textfield.TextInputLayout>

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/chat_send_button"
                                style="@style/Widget.Flashat.Button.TextButton.IconOnly"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                app:icon="@drawable/ic_chat_send"
                                app:iconTint="@color/colorPrimary"
                                android:enabled="@{viewModel.chatText.trim().length>0}"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@id/chat_input"
                                app:layout_constraintTop_toTopOf="@id/chat_input" />
                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.kennyc.view.MultiStateView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/gift_container"
                        android:layout_width="67dp"
                        android:layout_height="67dp"
                        android:layout_margin="@dimen/line_spacing"
                        goneIf="@{viewModel.podium.manager}"
                        app:cardElevation="0dp"
                        app:cardBackgroundColor="@color/colorSurfaceSecondaryDark"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintEnd_toEndOf="parent">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/action_gift"
                                android:layout_width="50dp"
                                android:layout_height="50dp"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:srcCompat="@drawable/yellow_gift" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.google.android.material.card.MaterialCardView>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>
