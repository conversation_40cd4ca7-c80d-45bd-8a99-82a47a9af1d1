<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    >
    <data>
        <variable
            name="chat"
            type="com.app.messej.ui.home.publictab.podiums.model.PodiumLiveChatUIModel.MaidanContribution" />
        <variable
            name="dayNight"
            type="Boolean" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/activity_margin"
            android:layout_marginVertical="@dimen/element_spacing"
            app:cardCornerRadius="6dp"
            app:cardBackgroundColor="@{chat.specialGift ?@color/colorMaidanGiftBackground :@color/transparent}"
            tools:cardBackgroundColor="@color/colorMaidanGiftBackground"
            app:strokeColor="@{chat.isBirthdayPodium?@color/colorPrimary :@color/transparent}"
            tools:strokeColor="@color/colorPrimary"
            app:strokeWidth="1dp"
            app:cardElevation="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">


            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:padding="@dimen/element_spacing"
                android:padding="@{chat.specialGift?@dimen/element_spacing:@dimen/zero_dp}">

                <com.makeramen.roundedimageview.RoundedImageView
                    android:id="@+id/user_dp"
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:scaleType="centerCrop"
                    app:imageUrl="@{chat.senderDetails.thumbnail}"
                    app:layout_constraintBaseline_toBottomOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:placeholder="@{@drawable/im_user_placeholder_square}"
                    app:riv_oval="true"
                    tools:src="@drawable/im_user_placeholder_square" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/superstar_premium_badge"
                    android:layout_width="10dp"
                    android:layout_height="10dp"
                    android:elevation="4dp"
                    app:layout_constraintStart_toStartOf="@id/user_dp"
                    app:layout_constraintTop_toTopOf="@id/user_dp"
                    app:userBadgeOnPrimary="@{chat.senderDetails.userBadge}"
                    tools:src="@drawable/ic_user_badge_premium" />


                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/like_icon"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginHorizontal="@dimen/element_spacing"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/user_dp"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:src="@drawable/ic_gift_square" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/header"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    app:layout_constraintBottom_toTopOf="@+id/message"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@id/like_icon"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/username"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textAppearance="@style/TextAppearance.Flashat.Label.Small"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintEnd_toStartOf="@+id/flag"
                        app:layout_constraintHorizontal_bias="0.0"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="John Snow"
                        tools:textColor="@color/colorLiveChatNewUser" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/flag"
                        android:layout_width="18dp"
                        android:layout_height="18dp"
                        android:layout_marginStart="@dimen/element_spacing"
                        android:layout_marginEnd="@dimen/activity_margin"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintStart_toEndOf="@+id/username"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:srcCompat="@drawable/flag_india" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/message"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/activity_margin"
                    android:textAppearance="@style/TextAppearance.Flashat.Label.Small"
                    android:textColor="@{chat.giftType?@color/colorSecondary:@color/textColorSecondary}"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toStartOf="@id/header"
                    app:layout_constraintTop_toBottomOf="@+id/header"
                    tools:text="26/1200"
                    tools:textColor="@color/textColorSecondary" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>