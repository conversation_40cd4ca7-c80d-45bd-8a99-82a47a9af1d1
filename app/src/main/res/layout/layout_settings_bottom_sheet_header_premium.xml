<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable name="profile" type="com.app.messej.data.model.CurrentUser.Profile" />
        <variable name="account" type="com.app.messej.data.model.api.profile.AccountDetailsResponse" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:id="@+id/profile_layout"
            android:background="@color/colorPrimary">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/appCompatImageView"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginTop="@dimen/element_spacing"
                android:adjustViewBounds="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/bg_drawer_circle_left" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/appCompatImageView4"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:adjustViewBounds="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/bg_drawer_circle_right" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/close_button"
                style="@style/Widget.Flashat.Button.TextButton.IconOnly.Small"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/element_spacing"
                android:layout_marginEnd="@dimen/element_spacing"
                app:icon="@drawable/ic_close"
                app:iconSize="24dp"
                app:iconTint="@color/textColorOnPrimary"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:enabled="true" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/profile_holder"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/element_spacing"
                android:paddingBottom="@dimen/activity_margin"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/close_button">

                <com.makeramen.roundedimageview.RoundedImageView
                    android:id="@+id/superstar_dp"
                    android:layout_width="@dimen/drawer_header_dp_size"
                    android:layout_height="@dimen/drawer_header_dp_size"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:elevation="2dp"
                    android:scaleType="centerCrop"
                    app:imageUrl="@{profile.thumbnail}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:placeholder="@{@drawable/im_user_placeholder_opaque}"
                    app:riv_border_color="#db91e2"
                    app:riv_border_width="3dp"
                    app:riv_corner_radius="@dimen/drawer_header_dp_size"
                    tools:src="@drawable/im_user_placeholder_opaque" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/superstar_premium_badge"
                    android:layout_width="@dimen/drawer_header_premium_badge_size"
                    android:layout_height="@dimen/drawer_header_premium_badge_size"
                    android:elevation="4dp"
                    app:layout_constraintStart_toStartOf="@id/superstar_dp"
                    app:layout_constraintTop_toTopOf="@id/superstar_dp"
                    app:userBadge="@{profile.userBadge}"
                    tools:src="@drawable/ic_user_badge_premium_on_primary" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/welcomeText"
                    style="@style/TextAppearance.Flashat.Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/activity_margin"
                    android:text="@string/drawer_header_welcome"
                    android:textColor="@color/textColorOnPrimary"
                    app:layout_constraintBottom_toTopOf="@+id/superstar_name"
                    app:layout_constraintStart_toEndOf="@id/superstar_dp"
                    app:layout_constraintTop_toTopOf="@+id/superstar_dp"
                    app:layout_constraintVertical_chainStyle="packed" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/superstar_name"
                    style="@style/TextAppearance.Flashat.Headline5"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@{profile.name}"
                    android:textColor="@color/textColorOnPrimary"
                    app:layout_constraintStart_toStartOf="@+id/welcomeText"
                    app:layout_constraintEnd_toStartOf="@id/text_citizenship"
                    app:layout_constraintTop_toBottomOf="@+id/welcomeText"
                    tools:text="Mathew Varghese Puthiyaparambil" />


                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_citizenship"
                    style="@style/TextAppearance.Flashat.Subtitle1"
                    android:layout_width="wrap_content"
                    android:paddingEnd="@dimen/element_spacing"
                    android:paddingStart="24dp"
                    android:paddingVertical="@dimen/line_spacing"
                    android:background="@drawable/bg_citizenship_text_right"
                    android:layout_height="wrap_content"
                    android:textAlignment="center"
                    tools:text="Visitor"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/welcomeText"
                    />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:id="@+id/divider"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginStart="@dimen/element_spacing"
                android:layout_marginEnd="@dimen/element_spacing"
                goneIf="@{account.haveSocialBalance}"
                android:background="@color/colorAlwaysLightPrimaryLighter"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/profile_holder" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_rating_progress"
                android:layout_width="match_parent"
                app:layout_constraintTop_toBottomOf="@id/divider"
                android:layout_marginTop="@dimen/line_spacing"
                android:layout_marginHorizontal="11dp"
                goneIfNot="@{account.haveSocialBalance}"
                android:layout_height="wrap_content">

                <com.google.android.material.progressindicator.LinearProgressIndicator
                    android:id="@+id/progress_rating"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:trackCornerRadius="@dimen/line_spacing"
                    android:max="100"
                    app:trackThickness="6dp"
                    android:progress="@{account.flaxRatePercentage}"
                    app:indicatorColor="@color/colorSecondary"
                    app:trackColor="@color/colorAlwaysLightPrimaryLighter"
                    app:layout_constraintTop_toTopOf="parent"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_rating"
                    style="@style/TextAppearance.Flashat.Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/line_spacing"
                    android:text="@string/id_card_rating"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/progress_rating"
                    android:textColor="@color/textColorOnPrimary" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_rating_percentage"
                    style="@style/TextAppearance.Flashat.Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/line_spacing"
                    tools:text="73%"
                    android:text="@{@string/common_percentage_value(account.flaxRatePercentage == null ? 0 : account.flaxRatePercentage)}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/progress_rating"
                    android:textColor="@color/textColorOnPrimary" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:baselineAligned="false"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/element_spacing"
                android:paddingVertical="@dimen/element_spacing"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layout_rating_progress"
                app:layout_constraintVertical_bias="1.0"
                tools:layout_editor_absoluteX="0dp">

                <include
                    layout="@layout/item_settings_bottom_sheet_rounded_view"
                    android:layout_width="0dp"
                    app:goneIf="@{account.haveSocialBalance}"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    app:count="@{@string/common_percentage_value(account.flaxRatePercentage == null ? 0 : account.flaxRatePercentage)}"
                    app:firstText="@{@string/id_card_rating}" />

                <include
                    android:id="@+id/layout_flix_balance"
                    layout="@layout/item_settings_bottom_sheet_rounded_view"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:layout_weight="1"
                    app:count="@{account.activePointsString}"
                    app:firstText="@{account.haveSocialBalance ? @string/drawer_header_primary_flix_balance : @string/drawer_header_pp_balance}" />

                <include
                    android:id="@+id/layout_social_balance"
                    layout="@layout/item_settings_bottom_sheet_rounded_view"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    app:goneIfNot="@{account.haveSocialBalance}"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:layout_weight="1"
                    app:count="@{account.socialBalanceString}"
                    app:firstText="@{@string/common_social_support_balance}" />

            </LinearLayout>

            <!--            <androidx.appcompat.widget.AppCompatTextView-->
<!--                android:id="@+id/pp_title"-->
<!--                style="@style/TextAppearance.Flashat.Body2"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginHorizontal="@dimen/extra_margin"-->
<!--                android:layout_marginVertical="@dimen/activity_margin"-->
<!--                android:text="@string/drawer_header_pp_balance"-->
<!--                android:textColor="@color/textColorOnPrimary"-->
<!--                app:layout_constraintStart_toStartOf="parent"-->
<!--                app:layout_constraintTop_toBottomOf="@id/divider"-->
<!--                app:layout_constraintBottom_toBottomOf="parent"-->
<!--                />-->

<!--            <androidx.appcompat.widget.AppCompatTextView-->
<!--                android:id="@+id/pp_points_text"-->
<!--                style="@style/TextAppearance.Flashat.Headline4"-->
<!--                android:layout_width="0dp"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginHorizontal="@dimen/extra_margin"-->
<!--                android:layout_marginVertical="@dimen/activity_margin"-->
<!--                tools:text="848"-->
<!--                android:text="@{account.activePointsString}"-->
<!--                android:textColor="@color/textColorOnPrimary"-->
<!--                app:layout_constraintTop_toBottomOf="@id/divider"-->
<!--                app:layout_constraintBottom_toBottomOf="parent"-->
<!--                app:layout_constraintEnd_toEndOf="parent"-->
<!--                />-->

        </androidx.constraintlayout.widget.ConstraintLayout>

       <!-- <HorizontalScrollView
            android:layout_width="match_parent"
            app:layout_constraintTop_toBottomOf="@id/view"
            android:scrollbars="none"
            android:layout_height="wrap_content">-->

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:paddingHorizontal="@dimen/line_spacing"
                android:paddingVertical="@dimen/element_spacing"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/view">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/e_tribe_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="10sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:paddingVertical="@dimen/element_spacing"
                    style="@style/Widget.Flashat.DrawerHeaderButton"
                    android:textColor="@color/textColorPrimary"
                    app:iconTint="@null"
                    android:backgroundTint="@color/colorSurfaceSecondary"
                    android:text="@string/e_tribe_title"
                    android:textAllCaps="false"
                    app:icon="@drawable/ic_tribe_rounded_rectangle"
                    app:iconGravity="end"
                    app:iconSize="14dp"
                    app:iconPadding="@dimen/line_spacing" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/followers_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/line_spacing"
                    android:textSize="10sp"
                    style="@style/Widget.Flashat.DrawerHeaderButton"
                    android:paddingVertical="@dimen/element_spacing"
                    android:backgroundTint="@color/colorSurfaceSecondary"
                    android:text="@string/home_private_tab_followers"
                    android:textColor="@color/textColorPrimary"
                    android:textAllCaps="false"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toEndOf="@id/e_tribe_button" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/following_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/line_spacing"
                    android:paddingVertical="@dimen/element_spacing"
                    android:text="@string/home_private_tab_following"
                    android:textSize="10sp"
                    android:textAllCaps="false"
                    android:textColor="@color/textColorPrimary"
                    android:backgroundTint="@color/colorSurfaceSecondary"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/followers_button"
                    style="@style/Widget.Flashat.DrawerHeaderButton" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        <!--</HorizontalScrollView>-->

        <View
            android:id="@+id/view"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorDividerLight"
            app:layout_constraintTop_toBottomOf="@id/profile_layout"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>