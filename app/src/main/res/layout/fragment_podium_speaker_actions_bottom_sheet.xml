<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable name="viewModel" type="com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel" />

        <import type="com.app.messej.data.model.enums.UserCitizenship"/>

        <variable name="speaker" type="com.app.messej.data.model.api.podium.PodiumSpeaker" />
        <variable name="isSelf" type="Boolean" />

        <variable name="isFollowed" type="Boolean" />

        <import type="com.app.messej.data.model.enums.PodiumKind"/>
        <import type="com.app.messej.data.model.enums.PodiumBlockFrom"/>
    </data>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorSurface"
        android:animateLayoutChanges="true"
        android:paddingBottom="80dp">

        <View
            android:id="@+id/handle"
            android:layout_width="60dp"
            android:layout_height="8dp"
            android:layout_marginTop="@dimen/activity_margin"
            android:background="@drawable/ic_bottom_sheet_handle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <include
            android:id="@+id/user_info"
            layout="@layout/layout_podium_bottom_sheet_user_info"
            app:userTribeName="@{speaker.tribeName}"
            app:podiumName="@{viewModel.podium.name}"
            app:hideSendGift="@{viewModel.canSendAndReceiveGifts(speaker) &amp;&amp; !viewModel.podiumKind.birthDayPodium}"
            app:speaker="@{speaker}"
            app:isPodiumManager="@{viewModel.isManager(speaker.id)}"
            app:isBirthDayPodium="@{viewModel.podiumKind.birthDayPodium}"/>

        <View
            android:id="@+id/line"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_marginTop="@dimen/activity_margin"
            android:background="@color/colorDividerLight"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/user_info" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_extend_speaking_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="@{viewModel.currentAssemblySpeaker.speakingTimeLimit > 60 ? false : true}"
                android:foreground="?attr/selectableItemBackground"
                app:goneIfNot="@{viewModel.podiumKind==PodiumKind.ASSEMBLY &amp;&amp; viewModel.iAmManager &amp;&amp; viewModel.currentAssemblySpeaker.id == speaker.id &amp;&amp; !isSelf}">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/extend_speaking_image_view"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_podium_speaker_extend_time" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/title_extend_speaking_time"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:text="@string/title_extend_speaking_time"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/extend_icon_layout"
                    app:layout_constraintStart_toEndOf="@id/extend_speaking_image_view"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:id="@+id/extend_icon_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:orientation="horizontal"
                    android:layout_marginEnd="@dimen/activity_margin"
                    android:alpha="@{viewModel.currentAssemblySpeaker.speakingTimeLimit > 60 ? 0.5f : 1.0f}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/element_spacing"
                        tools:text="60"
                        android:textAppearance="@style/TextAppearance.Flashat.Body2" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/extend_speak_add_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/element_spacing"
                        app:srcCompat="@drawable/ic_extend_speak_time" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_pause_gift"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIfNot="@{viewModel.shouldShowPauseGift(speaker.id, speaker.citizenship.isVisitor,speaker.citizenship.isGolden)}">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_pause_gift"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_pause_gift" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/title_pause_gift"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:text="@{viewModel.isUserGiftPaused(speaker.id)?@string/podium_resume_gift:@string/podium_pause_gift}"
                    tools:text="@string/podium_pause_gift"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/goto_pause_gift"
                    app:layout_constraintStart_toEndOf="@id/image_pause_gift"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/goto_pause_gift"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_caret_right" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_follow"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIf="@{isSelf || isFollowed == null || viewModel.user.superStarId == speaker.id}"
                app:orGoneIf="@{viewModel.podiumKind==PodiumKind.THEATER}">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_follow"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:srcCompat="@drawable/ic_podium_speaker_follow" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_follow"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:layout_marginEnd="@dimen/activity_margin"
                    android:text="@{isFollowed?@string/user_action_unfollow:@string/user_action_follow}"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/image_follow"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="@string/user_action_follow" />


                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_mute"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIfNot="@{viewModel.canMuteUnmuteSpeaker(speaker)}">

            <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_mute"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_podium_speaker_mute" />


                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_mute"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:layout_marginEnd="@dimen/activity_margin"
                    android:text="@{speaker.muted? @string/podium_action_unmute : @string/podium_action_mute}"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/loader5"
                    app:layout_constraintStart_toEndOf="@id/image_mute"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="Mute" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />


                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/loader5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/activity_margin"
                    android:indeterminate="true"
                    android:visibility="gone"
                    app:goneIfNot="@{viewModel.muteToggleLoading}"
                    app:indicatorSize="20dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:trackThickness="2dp" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_end"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIfNot="@{viewModel.canEndSpeakingSession(speaker)}">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_end"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_podium_speaker_end" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_end_speaking"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:text="@{(viewModel.podiumKind == PodiumKind.THEATER &amp;&amp; isSelf)?@string/podium_theater_step_down:@string/podium_action_end}"
                    tools:text="@string/podium_action_end"
                    android:layout_marginEnd="@dimen/activity_margin"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    android:textColor="@color/colorError"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/loader7"
                    app:layout_constraintStart_toEndOf="@id/image_end"
                    app:layout_constraintTop_toTopOf="parent" />


                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/loader7"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/activity_margin"
                    android:indeterminate="true"
                    android:visibility="gone"
                    app:goneIfNot="@{viewModel.endSpeakingSessionLoading}"
                    app:indicatorSize="20dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:trackThickness="2dp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_main_screen"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIfNot="@{(viewModel.iAmElevated || (viewModel.isPresidentUser&amp;&amp;speaker.citizenship.isPresident)) &amp;&amp; viewModel.canShowInMainScreen(speaker)}">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_main"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_podium_speaker_main_screen" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:layout_marginEnd="@dimen/activity_margin"
                    android:text="@string/podium_live_action_show_main_screen"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/loader_main_screen"
                    app:layout_constraintStart_toEndOf="@id/image_main"
                    app:layout_constraintTop_toTopOf="parent"/>

                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/loader_main_screen"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:indeterminate="true"
                    app:indicatorSize="20dp"
                    app:goneIfNot="@{viewModel.showInMainScreenLoading}"
                    app:trackThickness="2dp"
                    android:visibility="gone"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_appoint_admin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIfNot="@{viewModel.canAppointAsAdmin(speaker.id,speaker.premiumUser)}">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_appoint_admin"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:srcCompat="@drawable/ic_podium_speaker_appoint_as_admin" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_appoint_admin"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:text="@string/podium_live_action_make_admin"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/loader2"
                    app:layout_constraintStart_toEndOf="@id/image_appoint_admin"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/loader2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:indeterminate="true"
                    app:indicatorSize="20dp"
                    app:trackThickness="2dp"
                    app:goneIfNot="@{viewModel.appointAsAdminLoading}"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_dismiss_admin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIfNot="@{viewModel.canDismissAdmin(speaker.id)}">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_dismiss_admin"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:srcCompat="@drawable/ic_podium_speaker_dismiss_as_admin" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_dismiss_admin"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:text="@string/podium_action_dismiss_admin"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/image_dismiss_admin"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/loader3" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/loader3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:indeterminate="true"
                    app:indicatorSize="20dp"
                    app:trackThickness="2dp"
                    app:goneIfNot="@{viewModel.dismissAsAdminLoading}"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:goneIfNot="@{viewModel.canWithdrawAsAdmin(speaker.id)}">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/action_withdraw_admin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:foreground="?attr/selectableItemBackground"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/loader9"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/image_withdraw_admin"
                        android:layout_width="24dp"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="@dimen/activity_margin"
                        android:layout_marginStart="@dimen/extra_margin"
                        android:adjustViewBounds="true"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/ic_podium_speaker_withdraw_admin" />


                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/text_withdraw_admin"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="@string/podium_action_withdraw_admin"
                        android:layout_marginEnd="@dimen/activity_margin"
                        android:textAppearance="@style/TextAppearance.Flashat.Body1"
                        app:layout_constraintBottom_toBottomOf="parent"
                        android:layout_marginStart="@dimen/element_spacing"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/image_withdraw_admin"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>


                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/loader9"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/activity_margin"
                    android:indeterminate="true"
                    android:visibility="gone"
                    app:goneIfNot="@{viewModel.dismissAsAdminLoading}"
                    app:indicatorSize="20dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:trackThickness="2dp" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_cancel_admin_invite"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:goneIfNot="@{viewModel.canCancelAdminInvite(speaker.id)}">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_cancel_admin_invite"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:srcCompat="@drawable/ic_podium_speaker_cancel_admin_invite" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_cancel_admin_invite"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/huddle_cancel_admin_invite"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/loader4"
                    app:layout_constraintStart_toEndOf="@id/image_cancel_admin_invite"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/loader4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:indeterminate="true"
                    app:indicatorSize="20dp"
                    app:trackThickness="2dp"
                    app:goneIfNot="@{viewModel.cancelAdminInviteLoading}"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_private_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                app:goneIf="@{isSelf}"
                app:orGoneIf="@{viewModel.podiumKind==PodiumKind.THEATER}"
                android:foreground="?attr/selectableItemBackground">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_private_message"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_podium_speaker_message" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_private_message"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:text="@string/private_messages_eds_action"
                    android:layout_marginEnd="@dimen/activity_margin"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/image_private_message"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground">
                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_info"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_podium_speaker_info" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_info"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:text="@string/podium_action_id_card"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/goto_info"
                    app:layout_constraintStart_toEndOf="@id/image_info"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/goto_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_caret_right" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_block"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIfNot="@{viewModel.canBlock(speaker.id)}"
                >

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_block"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:src="@{viewModel.podiumKind==PodiumKind.THEATER?@drawable/ic_podium_speaker_block_theater:@drawable/ic_podium_speaker_block}"
                    tools:srcCompat="@drawable/ic_podium_speaker_block_theater" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_block"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/user_action_block"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    android:textColor="@color/colorError"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/image_block"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/loader6" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/loader6"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:indeterminate="true"
                    app:goneIfNot="@{viewModel.userBlockLoading}"
                    app:orGoneIfNot="@{viewModel.userBlockLoadingSource==null}"
                    app:indicatorSize="20dp"
                    app:trackThickness="2dp"
                    android:visibility="gone"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_block_theater_stg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIfNot="@{viewModel.canBlock(speaker.id)}"
                app:orGoneIfNot="@{viewModel.podiumKind==PodiumKind.THEATER}"
                >

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_block_stg"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:srcCompat="@drawable/ic_podium_speaker_block_theater_stage" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_block_th"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@{speaker.blockedFromStage?@string/podium_theater_unblock_stage:@string/podium_theater_block_stage}"
                    tools:text="@string/podium_theater_block_stage"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    android:textColor="@color/colorError"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/image_block_stg"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/loader8" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/loader8"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:indeterminate="true"
                    app:goneIfNot="@{viewModel.userBlockLoading}"
                    app:orGoneIfNot="@{viewModel.userBlockLoadingSource==PodiumBlockFrom.STAGE}"
                    app:indicatorSize="20dp"
                    app:trackThickness="2dp"
                    android:visibility="gone"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_block_theater_aud"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIfNot="@{viewModel.canBlock(speaker.id)}"
                app:orGoneIfNot="@{viewModel.podiumKind==PodiumKind.THEATER}"
                >

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_block_aud"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:srcCompat="@drawable/ic_podium_speaker_block_theater_audience" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_block_aud"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@{speaker.blockedFromAudience?@string/podium_theater_unblock_audience:@string/podium_theater_block_audience}"
                    tools:text="@string/podium_theater_block_audience"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    android:textColor="@color/colorError"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/image_block_aud"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/loader10" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorDividerLight"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/loader10"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:indeterminate="true"
                    app:goneIfNot="@{viewModel.userBlockLoading}"
                    app:orGoneIfNot="@{viewModel.userBlockLoadingSource==PodiumBlockFrom.AUDIENCE}"
                    app:indicatorSize="20dp"
                    app:trackThickness="2dp"
                    android:visibility="gone"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_report_user"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIfNot="@{viewModel.canReport(speaker)}">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_report_user"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:background="@drawable/ic_dot"
                    android:backgroundTint="@color/colorPrimary"
                    android:padding="2dp"
                    app:tint="@color/textColorOnPrimary"
                    app:srcCompat="@drawable/ic_report_outline_cut" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_report_user"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/report_user"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    android:textColor="@color/colorError"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/image_report_user"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/action_ban_user"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                app:goneIfNot="@{viewModel.canBan(speaker)}">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_ban_user"
                    android:layout_width="24dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:layout_marginVertical="@dimen/activity_margin"
                    android:adjustViewBounds="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:background="@drawable/ic_dot"
                    android:backgroundTint="@color/colorPrimary"
                    android:padding="2dp"
                    app:tint="@color/textColorOnPrimary"
                    app:srcCompat="@drawable/ic_ban_user_round" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_ban_user"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/report_user_ban"
                    android:textAppearance="@style/TextAppearance.Flashat.Body1"
                    android:textColor="@color/colorError"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/image_ban_user"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>
</layout>

