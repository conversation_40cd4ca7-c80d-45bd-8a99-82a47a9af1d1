<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.app.messej.ui.home.publictab.podiums.manage.PodiumAboutViewModel" />

        <variable
            name="adminListExpanded"
            type="Boolean" />

        <variable
            name="CompactedUser"
            type="Boolean" />
    </data>

    <com.kennyc.view.MultiStateView
        android:id="@+id/multiStateViewShimmer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorSurface"
        app:msv_errorView="@layout/layout_list_state_error"
        app:msv_emptyView="@layout/layout_list_state_empty"
        app:msv_loadingView="@layout/layout_eds_podium_about_state_loading"
        app:msv_viewState="content">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/parent_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="80dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/action_close"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    style="@style/Widget.Flashat.Button.TextButton.IconOnly"
                    app:icon="@drawable/ic_close"
                    android:layout_margin="@dimen/element_spacing"
                    app:iconTint="@color/textColorSecondaryLight"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/handle"
                    android:layout_width="60dp"
                    android:layout_height="6dp"
                    android:background="@drawable/ic_bottom_sheet_handle"
                    app:layout_constraintBottom_toBottomOf="@+id/action_close"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/action_close" />


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/podium_info_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/action_close">

                    <com.makeramen.roundedimageview.RoundedImageView
                        android:id="@+id/podium_dp"
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:layout_margin="@dimen/activity_margin"
                        android:scaleType="centerCrop"
                        app:imageUrl="@{viewModel.podium.thumbnail}"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:placeholder="@{@drawable/im_podium_placeholder}"
                        app:riv_corner_radius="10dp"
                        tools:src="@drawable/im_podium_placeholder" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/podium_name"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/activity_margin"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="@{viewModel.podium.name}"
                        android:textAppearance="@style/TextAppearance.Flashat.Headline5"
                        android:textColor="@color/colorPrimary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/podium_dp"
                        app:layout_constraintTop_toTopOf="@+id/podium_dp"
                        tools:text="Let the Games Begin" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/created_since"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="@string/podium_about_created_since"
                        android:textAppearance="@style/TextAppearance.Flashat.Caption"
                        android:textColor="@color/colorPrimary"
                        app:layout_constraintStart_toStartOf="@+id/podium_name"
                        app:layout_constraintTop_toBottomOf="@id/podium_name"
                        app:layout_constraintVertical_chainStyle="packed" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/calender_img"
                        android:layout_width="14dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/element_spacing"
                        android:adjustViewBounds="true"
                        app:layout_constraintBottom_toBottomOf="@+id/created_since"
                        app:layout_constraintStart_toEndOf="@id/created_since"
                        app:layout_constraintTop_toTopOf="@+id/created_since"
                        app:layout_constraintVertical_chainStyle="packed"
                        app:tint="@color/textColorSecondary"
                        app:srcCompat="@drawable/ic_calendar_podium_about" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/created_date"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/element_spacing"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="@{viewModel.podium.timePublishedFormatted}"
                        android:textAppearance="@style/TextAppearance.Flashat.Caption"
                        android:textColor="@color/textColorSecondary"
                        app:layout_constraintBaseline_toBaselineOf="@id/created_since"
                        app:layout_constraintEnd_toEndOf="@+id/podium_name"
                        app:layout_constraintStart_toEndOf="@+id/calender_img"
                        app:layout_constraintVertical_chainStyle="packed"
                        tools:text="12/12/12" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/about_podium"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/line_spacing"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="@{viewModel.podium.description}"
                        android:textAppearance="@style/TextAppearance.Flashat.Caption"
                        android:textColor="@color/textColorSecondary"
                        app:layout_constraintBottom_toBottomOf="@id/podium_dp"
                        app:layout_constraintEnd_toEndOf="@+id/podium_name"
                        app:layout_constraintStart_toStartOf="@+id/podium_name"
                        app:layout_constraintTop_toBottomOf="@+id/created_since"
                        app:layout_constraintVertical_chainStyle="packed"
                        tools:text="Lets talk about future of the society" />


                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/admins_and_manager_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/activity_margin"
                    app:cardBackgroundColor="@color/colorSurfaceSecondary"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="1dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/podium_info_layout">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/admins_and_manager_holder"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/layout_header"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:layout_constraintTop_toTopOf="parent">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/manager_admins_heading"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_margin="@dimen/activity_margin"
                                android:text="@string/podium_about_manager_and_admins"
                                android:textAppearance="@style/TextAppearance.Flashat.Headline6"
                                android:textColor="@color/colorPrimary"
                                app:layout_constraintBottom_toBottomOf="@+id/expand_arrow"
                                app:layout_constraintEnd_toStartOf="@+id/expand_arrow"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="@+id/expand_arrow" />

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/expand_arrow"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                style="@style/Widget.Flashat.Button.TextButton.IconOnly"
                                app:icon="@{adminListExpanded?@drawable/ic_caret_up_rounded:@drawable/ic_caret_down_rounded}"
                                tools:icon="@drawable/ic_caret_up_rounded"
                                app:iconTint="@color/textColorSecondary"
                                app:visibleIf="@{viewModel.showAdminList}"
                                tools:visibility="visible"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />
                        </androidx.constraintlayout.widget.ConstraintLayout>


                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/manager_info_fixed"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/activity_margin"
                            android:foreground="?android:attr/selectableItemBackground"
                            android:clickable="true"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/layout_header">

                            <com.makeramen.roundedimageview.RoundedImageView
                                android:id="@+id/superstar_dp"
                                android:layout_width="@dimen/superstar_list_dp_size"
                                android:layout_height="@dimen/superstar_list_dp_size"
                                android:elevation="2dp"
                                android:scaleType="centerCrop"
                                app:imageUrl="@{viewModel.podiumManager.thumbnail}"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintBottom_toBottomOf="parent"
                                android:layout_marginTop="@dimen/element_spacing"
                                app:layout_constraintBottom_toTopOf="@+id/expanding_line"
                                app:layout_goneMarginBottom="@dimen/activity_margin"
                                app:placeholder="@{@drawable/im_user_placeholder_opaque}"
                                app:riv_corner_radius="@dimen/superstar_list_dp_size"
                                tools:src="@drawable/im_user_placeholder_opaque" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/superstar_premium_badge"
                                android:layout_width="@dimen/premium_badge_size"
                                android:layout_height="@dimen/premium_badge_size"
                                android:elevation="4dp"
                                app:layout_constraintStart_toStartOf="@id/superstar_dp"
                                app:layout_constraintTop_toTopOf="@id/superstar_dp"
                                app:userBadge="@{viewModel.podiumManager.userBadge}"
                                tools:src="@drawable/ic_user_badge_premium" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/manager_name"
                                style="@style/TextAppearance.Flashat.Subtitle2"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="@dimen/activity_margin"
                                android:text="@{viewModel.userManager?@string/common_you:viewModel.podiumManager.name}"
                                app:layout_constraintBottom_toTopOf="@+id/manager_label"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@id/superstar_dp"
                                app:layout_constraintTop_toTopOf="@+id/superstar_dp"
                                app:layout_constraintVertical_chainStyle="packed"
                                tools:text="Mathew Varghese" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/manager_label"
                                style="@style/TextAppearance.Flashat.Label.Small"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:text="@string/common_manager"
                                android:textColor="@color/textColorSecondary"
                                android:visibility="visible"
                                app:layout_constraintBottom_toBottomOf="@+id/superstar_dp"
                                app:layout_constraintEnd_toEndOf="@+id/manager_name"
                                app:layout_constraintHorizontal_bias="0.5"
                                app:layout_constraintStart_toStartOf="@+id/manager_name"
                                app:layout_constraintTop_toBottomOf="@+id/manager_name" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/expanding_line"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/line_spacing"
                                android:adjustViewBounds="true"
                                app:goneIfNot="@{adminListExpanded}"
                                tools:visibility="visible"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="@id/superstar_dp"
                                app:layout_constraintStart_toStartOf="@id/superstar_dp"
                                app:layout_constraintTop_toBottomOf="@id/superstar_dp"
                                app:srcCompat="@drawable/ic_podium_admins_expanding_line"
                                app:tint="@color/colorDivider" />

                        </androidx.constraintlayout.widget.ConstraintLayout>


                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/expandable_admins_view"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            app:goneIfNot="@{adminListExpanded}"
                            tools:visibility="visible"
                            android:layout_marginHorizontal="@dimen/activity_margin"
                            android:layout_marginBottom="@dimen/activity_margin"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/manager_info_fixed">

                            <com.kennyc.view.MultiStateView
                                android:id="@+id/multiStateView"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:msv_emptyView="@layout/layout_list_state_empty"
                                app:msv_errorView="@layout/layout_list_state_error"
                                app:msv_loadingView="@layout/layout_eds_state_loading"
                                app:msv_viewState="content">

                                <androidx.recyclerview.widget.RecyclerView
                                    android:id="@+id/admins_list"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:clipToPadding="false"
                                    android:nestedScrollingEnabled="true"
                                    tools:itemCount="4"
                                    tools:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                                    tools:listitem="@layout/item_podium_about_admin_list"
                                    tools:spanCount="2" />

                            </com.kennyc.view.MultiStateView>

                        </androidx.constraintlayout.widget.ConstraintLayout>


                    </androidx.constraintlayout.widget.ConstraintLayout>


                </com.google.android.material.card.MaterialCardView>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/counter_holder"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/activity_margin"
                    android:layout_marginTop="@dimen/activity_margin"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    app:layout_constraintWidth_max="450dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/admins_and_manager_layout">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/total_joined"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        app:cardBackgroundColor="@color/colorSurfaceSecondary"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="1dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/total_speakers"
                        app:layout_constraintHorizontal_chainStyle="spread"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/element_spacing">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/total_joined_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/podium_about_total_joined"
                                android:textAlignment="center"
                                android:textAppearance="@style/TextAppearance.Flashat.Caption"
                                android:textColor="@color/colorPrimary"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/total_joined_img"
                                android:layout_width="10dp"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/element_spacing"
                                android:adjustViewBounds="true"
                                app:layout_constraintBottom_toBottomOf="@id/total_joined_count"
                                app:layout_constraintEnd_toStartOf="@id/total_joined_count"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="@id/total_joined_count"
                                app:srcCompat="@drawable/bg_generic_circle"
                                app:tint="@color/textColorSecondary" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/total_joined_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/line_spacing"
                                android:layout_marginEnd="@dimen/line_spacing"
                                android:text="@{viewModel.podium.totalUsersFormatted}"
                                android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                                android:textColor="@color/textColorSecondary"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@+id/total_joined_img"
                                app:layout_constraintTop_toBottomOf="@id/total_joined_text"
                                tools:text="500" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/total_speakers"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/activity_margin"
                        app:cardBackgroundColor="@color/colorSurfaceSecondary"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="1dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/like_counter"
                        app:layout_constraintStart_toEndOf="@+id/total_joined"
                        app:layout_constraintTop_toTopOf="parent">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/element_spacing">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/total_speakers_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/podium_about_total_speakers"
                                android:textAlignment="center"
                                android:textAppearance="@style/TextAppearance.Flashat.Caption"
                                android:textColor="@color/colorPrimary"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/total_speakers_img"
                                android:layout_width="18dp"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/line_spacing"
                                android:adjustViewBounds="true"
                                app:layout_constraintBottom_toBottomOf="@id/total_speakers_count"
                                app:layout_constraintEnd_toStartOf="@id/total_speakers_count"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="@id/total_speakers_count"
                                app:srcCompat="@drawable/ic_podium_about_speakers"
                                app:tint="@color/textColorSecondary" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/total_speakers_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/line_spacing"
                                android:layout_marginEnd="@dimen/line_spacing"
                                android:text="@{viewModel.podium.speakersCountFormatted}"
                                android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                                android:textColor="@color/textColorSecondary"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@+id/total_speakers_img"
                                app:layout_constraintTop_toBottomOf="@id/total_speakers_text"
                                tools:text="500" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/like_counter"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        app:cardBackgroundColor="@color/colorSurfaceSecondary"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="1dp"
                        goneIf="@{viewModel.podium.podiumKind.birthDayPodium}"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/coins_counter"
                        app:layout_constraintStart_toEndOf="@+id/total_speakers"
                        app:layout_constraintTop_toTopOf="parent">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/element_spacing">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/total_likes_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/podium_about_total_likes"
                                android:textAlignment="center"
                                android:textAppearance="@style/TextAppearance.Flashat.Caption"
                                android:textColor="@color/colorPrimary"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/total_likes_img"
                                android:layout_width="18dp"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/line_spacing"
                                android:adjustViewBounds="true"
                                app:layout_constraintBottom_toBottomOf="@id/total_likes_count"
                                app:layout_constraintEnd_toStartOf="@id/total_likes_count"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="@id/total_likes_count"
                                app:srcCompat="@drawable/ic_podium_liked"
                                app:tint="@color/colorPrimary" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/total_likes_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/line_spacing"
                                android:layout_marginEnd="@dimen/line_spacing"
                                android:text="@{viewModel.podium.likesFormatted}"
                                android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                                android:textColor="@color/textColorSecondary"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@+id/total_likes_img"
                                app:layout_constraintTop_toBottomOf="@id/total_likes_text"
                                tools:text="500" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/coins_counter"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        app:cardBackgroundColor="@color/colorSurfaceSecondary"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="1dp"
                        goneIfNot="@{viewModel.podium.podiumKind.birthDayPodium}"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/like_counter"
                        app:layout_constraintTop_toTopOf="parent">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/element_spacing">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/total_coins_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text ="@string/podium_about_total_gifts"
                                android:textAlignment="center"
                                android:textAppearance="@style/TextAppearance.Flashat.Caption"
                                android:textColor="@color/colorPrimary"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/total_coins_img"
                                android:layout_width="18dp"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/line_spacing"
                                android:adjustViewBounds="true"
                                app:layout_constraintBottom_toBottomOf="@id/total_coins_count"
                                app:layout_constraintEnd_toStartOf="@id/total_coins_count"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="@id/total_coins_count"
                                app:srcCompat="@drawable/yellow_gift" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/total_coins_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/line_spacing"
                                android:layout_marginEnd="@dimen/line_spacing"
                                android:text="@{viewModel.podium.totalCoinsFormatted}"
                                android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                                android:textColor="@color/textColorSecondary"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@+id/total_coins_img"
                                app:layout_constraintTop_toBottomOf="@id/total_coins_text"
                                tools:text="500" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.google.android.material.card.MaterialCardView>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/duration_holder"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/activity_margin"
                    android:layout_marginTop="@dimen/activity_margin"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    app:layout_constraintWidth_max="450dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/counter_holder">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/total_live_times"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        app:cardBackgroundColor="@color/colorSurfaceSecondary"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="1dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/total_live_duration"
                        app:layout_constraintHorizontal_chainStyle="spread"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/element_spacing">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/total_live_times_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/podium_about_live_count"
                                android:textAppearance="@style/TextAppearance.Flashat.Caption"
                                android:textColor="@color/colorPrimary"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/total_live_times_img"
                                android:layout_width="10dp"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/element_spacing"
                                android:adjustViewBounds="true"
                                app:srcCompat="@drawable/ic_no_lives"
                                app:layout_constraintBottom_toBottomOf="@id/total_live_times_count"
                                app:layout_constraintEnd_toStartOf="@id/total_live_times_count"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="@id/total_live_times_count"
                                />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/total_live_times_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/line_spacing"
                                android:layout_marginEnd="@dimen/line_spacing"
                                android:text="@{viewModel.podium.totalSessions}"
                                android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                                android:textColor="@color/textColorSecondary"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@+id/total_live_times_img"
                                app:layout_constraintTop_toBottomOf="@id/total_live_times_text"
                                tools:text="500" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/total_live_duration"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/activity_margin"
                        app:cardBackgroundColor="@color/colorSurfaceSecondary"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="1dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/total_live_times"
                        app:layout_constraintTop_toTopOf="parent">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/element_spacing">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/total_live_duration_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/podium_about_live_duration"
                                android:textAppearance="@style/TextAppearance.Flashat.Caption"
                                android:textColor="@color/colorPrimary"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/total_live_duration_img"
                                android:layout_width="18dp"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/line_spacing"
                                android:adjustViewBounds="true"
                                app:layout_constraintBottom_toBottomOf="@id/total_live_duration_count"
                                app:layout_constraintEnd_toStartOf="@id/total_live_duration_count"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="@id/total_live_duration_count"
                                app:srcCompat="@drawable/ic_access_time"
                                app:tint="@color/textColorSecondary" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/total_live_duration_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/line_spacing"
                                android:layout_marginEnd="@dimen/line_spacing"
                                android:text="@{viewModel.podium.totalDuration}"
                                android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                                android:textColor="@color/textColorSecondary"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@+id/total_live_duration_img"
                                app:layout_constraintTop_toBottomOf="@id/total_live_duration_text"
                                tools:text="500" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.google.android.material.card.MaterialCardView>

                </androidx.constraintlayout.widget.ConstraintLayout>

<!--                <androidx.constraintlayout.widget.ConstraintLayout-->
<!--                    android:id="@+id/card_created"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    app:layout_constraintTop_toBottomOf="@id/duration_holder"-->
<!--                    app:layout_constraintStart_toStartOf="parent"-->
<!--                    app:layout_constraintEnd_toEndOf="parent"-->
<!--                    android:layout_marginTop="@dimen/activity_margin"-->
<!--                    android:layout_marginHorizontal="@dimen/activity_margin"-->
<!--                    goneIfNot="@{CompactedUser}">-->

<!--                    <com.google.android.material.card.MaterialCardView-->
<!--                        android:layout_width="0dp"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:elevation="@dimen/cardview_default_elevation"-->
<!--                        app:cardCornerRadius="@dimen/element_spacing"-->
<!--                        app:cardBackgroundColor="@color/colorPodiumSpeakerResident"-->
<!--                        app:layout_constraintStart_toStartOf="parent"-->
<!--                        app:layout_constraintEnd_toEndOf="parent"-->
<!--                        app:layout_constraintTop_toTopOf="parent">-->

<!--                        <androidx.appcompat.widget.AppCompatTextView-->
<!--                            style="@style/TextAppearance.Flashat.Body1"-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            tools:text="Created Since:19/11/2023"-->
<!--                            android:gravity="center"-->
<!--                            android:text="@{@string/podium_created_since(viewModel.podium.timeCreatedParsed)}"-->
<!--                            android:paddingVertical="@dimen/activity_margin"-->
<!--                            android:textColor="@color/textColorSecondary" />-->
<!--                    </com.google.android.material.card.MaterialCardView>-->
<!--                </androidx.constraintlayout.widget.ConstraintLayout>-->

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toBottomOf="@id/duration_holder"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:layout_marginHorizontal="@dimen/activity_margin"
                    goneIf="@{CompactedUser}">

                    <View
                        android:id="@+id/history_divider"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginHorizontal="@dimen/activity_margin"
                        android:layout_marginTop="@dimen/activity_margin"
                        android:background="@color/colorDivider"
                        app:goneIfNot="@{viewModel.iAmElevated}"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />


                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/history_layout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/activity_margin"
                        android:layout_marginTop="@dimen/activity_margin"
                        android:layout_marginEnd="@dimen/activity_margin"
                        app:goneIfNot="@{viewModel.iAmElevated}"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/history_divider">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/history_img"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:adjustViewBounds="true"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_podium_history" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/history_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/element_spacing"
                            android:text="@string/podium_about_history"
                            android:textAppearance="@style/TextAppearance.Flashat.Headline6"
                            android:textColor="@color/colorPrimary"
                            app:layout_constraintBottom_toBottomOf="@+id/history_img"
                            app:layout_constraintStart_toEndOf="@id/history_img"
                            app:layout_constraintTop_toTopOf="@id/history_img" />

                        <com.kennyc.view.MultiStateView
                            android:id="@+id/multiStateViewHistory"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginTop="@dimen/activity_margin"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/history_title"
                            app:msv_emptyView="@layout/layout_list_state_empty"
                            app:msv_errorView="@layout/layout_list_state_error"
                            app:msv_loadingView="@layout/layout_eds_state_loading"
                            app:msv_viewState="content">

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/record_list"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:clipToPadding="false"
                                android:nestedScrollingEnabled="true"
                                tools:itemCount="0"
                                tools:listitem="@layout/item_podium_records_expandable_item" />

                        </com.kennyc.view.MultiStateView>


                    </androidx.constraintlayout.widget.ConstraintLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>

    </com.kennyc.view.MultiStateView>
</layout>

