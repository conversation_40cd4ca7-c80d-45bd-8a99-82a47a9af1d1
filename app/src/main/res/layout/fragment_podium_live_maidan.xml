<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable name="viewModel" type="com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel" />
        <import type="com.app.messej.data.model.entity.Podium.MaidanStatus"/>
        <import type="com.app.messej.ui.utils.DataFormatHelper"/>
        <import type="com.app.messej.data.model.enums.StarType"/>
        <import type="com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.MaidanCompetitorStatus"/>

        <variable name="presenter" type="com.app.messej.ui.home.publictab.podiums.live.PodiumLiveMaidanFragment.DataPresenter" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/main_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorAlwaysDarkSurface"
        android:fitsSystemWindows="true">

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/colorSurface">

                <com.kennyc.view.MultiStateView
                    android:id="@+id/podium_header_multiStateView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/colorAlwaysDarkSurface"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:msv_loadingView="@layout/layout_eds_state_loading_podium_header"
                    app:msv_viewState="content">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/podium_header"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/podium_header_views"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent">

                            <com.makeramen.roundedimageview.RoundedImageView
                                android:id="@+id/podium_dp"
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:layout_margin="@dimen/activity_margin"
                                android:clickable="true"
                                android:foreground="?attr/selectableItemBackground"
                                android:scaleType="centerCrop"
                                app:imageUrl="@{viewModel.maidanProfilePic}"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintVertical_bias="0.0"
                                app:placeholder="@{@drawable/im_podium_placeholder}"
                                app:riv_oval="true"
                                tools:src="@drawable/im_podium_placeholder" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/premium_badge"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:adjustViewBounds="true"
                                android:elevation="4dp"
                                visibleIf="@{viewModel.isMaidanMainUserPremium}"
                                android:translationY="@dimen/line_spacing_negative"
                                app:layout_constraintStart_toStartOf="@id/podium_dp"
                                app:layout_constraintTop_toTopOf="@id/podium_dp"
                                app:localeAwareTranslationX="@{@dimen/line_spacing_negative}"
                                android:src="@drawable/ic_user_badge_premium" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/podium_name"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/activity_margin"
                                android:layout_marginEnd="@dimen/element_spacing"
                                android:ellipsize="end"
                                android:maxLines="1"
                                android:text="@{viewModel.podium.name}"
                                android:textAppearance="@style/TextAppearance.Flashat.Headline6"
                                android:textColor="@color/textColorOnPrimary"
                                app:layout_constrainedWidth="true"
                                app:layout_constraintBottom_toTopOf="@+id/counter_holder"
                                app:layout_constraintEnd_toStartOf="@+id/follow_holder"
                                app:layout_constraintHorizontal_bias="0.0"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintStart_toEndOf="@+id/podium_dp"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintVertical_chainStyle="packed"
                                tools:text="Tony With a long ass name" />

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:id="@+id/follow_holder"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                app:goneIf="@{viewModel.iAmManager}"
                                app:layout_constraintBottom_toBottomOf="@+id/podium_name"
                                app:layout_constraintEnd_toStartOf="@+id/relationship_note"
                                app:layout_constraintHorizontal_bias="0.0"
                                app:layout_constraintStart_toEndOf="@+id/podium_name"
                                app:layout_constraintTop_toTopOf="@+id/podium_name">

                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/follow_manager_button"
                                    style="@style/Widget.Flashat.MaidanFollowButton"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/user_action_follow"
                                    android:visibility="gone"
                                    app:goneIfNot="@{viewModel.starType==StarType.NONE}"
                                    app:orGoneIf="@{viewModel.followManagerLoading}"
                                    app:icon="@drawable/ic_plus"
                                    app:layout_constraintTop_toTopOf="parent"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    tools:enabled="false"
                                    tools:visibility="visible" />

                                <com.google.android.material.progressindicator.CircularProgressIndicator
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:indeterminate="true"
                                    tools:indeterminate="false"
                                    app:goneIfNot="@{viewModel.followManagerLoading}"
                                    android:visibility="gone"
                                    tools:visibility="visible"
                                    tools:progress="100"
                                    app:indicatorSize="20dp"
                                    app:trackThickness="3dp"
                                    android:layout_marginStart="6dp"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toTopOf="parent" />

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/star_note"
                                    style="@style/TextAppearance.Flashat.Caption.Bold"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:background="@drawable/bg_round_chip"
                                    android:backgroundTint="@color/textColorAlwaysDarkSecondary"
                                    android:paddingHorizontal="10dp"
                                    android:paddingVertical="2dp"
                                    android:text="@{presenter.getStarType(viewModel.starType)}"
                                    android:textColor="@color/textColorAlwaysLightSecondary"
                                    android:textSize="11sp"
                                    app:goneIf="@{viewModel.starType==null}"
                                    app:orGoneIf="@{viewModel.starType==StarType.NONE}"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toTopOf="parent"
                                    tools:text="@string/profile_star"
                                    tools:visibility="visible" />

                            </androidx.constraintlayout.widget.ConstraintLayout>

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/relationship_note"
                                style="@style/TextAppearance.Flashat.Caption.Bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/bg_round_chip"
                                android:backgroundTint="@color/textColorAlwaysDarkSecondary"
                                android:paddingHorizontal="10dp"
                                android:paddingVertical="2dp"
                                android:layout_marginStart="@dimen/line_spacing"
                                android:text="@{presenter.getRelation(viewModel.relation)}"
                                android:textColor="@color/textColorAlwaysLightSecondary"
                                android:textSize="11sp"
                                app:goneIfNull="@{viewModel.relation}"

                                app:layout_constraintBottom_toBottomOf="@+id/podium_name"
                                app:layout_constraintEnd_toStartOf="@+id/share_button"
                                app:layout_constraintHorizontal_bias="0.0"
                                app:layout_constraintStart_toEndOf="@+id/follow_holder"
                                app:layout_constraintTop_toTopOf="@+id/podium_name"
                                tools:text="@string/profile_star"
                                tools:visibility="visible" />

                            <LinearLayout
                                android:id="@+id/counter_holder"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:layout_marginTop="@dimen/line_spacing"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toStartOf="@+id/podium_name"
                                app:layout_constraintTop_toBottomOf="@id/podium_name">

                                <androidx.constraintlayout.widget.ConstraintLayout
                                    android:id="@+id/like_counter"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="@dimen/element_spacing">

                                    <androidx.appcompat.widget.AppCompatImageView
                                        android:id="@+id/like_img"
                                        android:layout_width="14dp"
                                        android:layout_height="wrap_content"
                                        android:adjustViewBounds="true"
                                        app:layout_constraintBottom_toBottomOf="parent"
                                        app:layout_constraintStart_toStartOf="parent"
                                        app:layout_constraintTop_toTopOf="parent"
                                        app:srcCompat="@drawable/ic_podium_liked"
                                        app:tint="@color/textColorOnPrimary" />

                                    <androidx.appcompat.widget.AppCompatTextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="@dimen/line_spacing"
                                        android:text="@{viewModel.podium.likesFormatted}"
                                        android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                                        android:textColor="@color/textColorOnPrimary"
                                        app:layout_constraintBottom_toBottomOf="parent"
                                        app:layout_constraintEnd_toEndOf="parent"
                                        app:layout_constraintStart_toEndOf="@+id/like_img"
                                        app:layout_constraintTop_toTopOf="parent"
                                        tools:text="12K" />

                                </androidx.constraintlayout.widget.ConstraintLayout>

                                <androidx.constraintlayout.widget.ConstraintLayout
                                    android:id="@+id/live_counter"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content">

                                    <androidx.appcompat.widget.AppCompatImageView
                                        android:id="@+id/live_img"
                                        android:layout_width="10dp"
                                        android:layout_height="wrap_content"
                                        android:adjustViewBounds="true"
                                        app:layout_constraintBottom_toBottomOf="parent"
                                        app:layout_constraintStart_toStartOf="parent"
                                        app:layout_constraintTop_toTopOf="parent"
                                        app:srcCompat="@drawable/bg_generic_circle"
                                        app:tint="@color/colorPass" />

                                    <androidx.appcompat.widget.AppCompatTextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="@dimen/line_spacing"
                                        android:text="@{viewModel.podium.liveUsersFormatted}"
                                        android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                                        android:textColor="@color/textColorOnPrimary"
                                        app:layout_constraintBottom_toBottomOf="parent"
                                        app:layout_constraintEnd_toEndOf="parent"
                                        app:layout_constraintStart_toEndOf="@+id/live_img"
                                        app:layout_constraintTop_toTopOf="parent"
                                        tools:text="123" />

                                </androidx.constraintlayout.widget.ConstraintLayout>

                            </LinearLayout>

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/share_button"
                                style="@style/Widget.Flashat.Button.TextButton.IconOnly.Secondary"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/element_spacing"
                                app:icon="@drawable/ic_share_filled"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                        </androidx.constraintlayout.widget.ConstraintLayout>
                    </androidx.constraintlayout.widget.ConstraintLayout>

                </com.kennyc.view.MultiStateView>

                <androidx.compose.ui.platform.ComposeView
                    android:id="@+id/ticker"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:composableName="com.app.messej.ui.home.promobar.PromoBarKt.PromoBarPreviewSingle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/podium_header_multiStateView"/>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/progress_holder"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ticker">

                    <View
                        android:id="@+id/start_spacer"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:background="@color/colorPrimary"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintDimensionRatio="1:1"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:id="@+id/end_spacer"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:background="@color/colorSecondary"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintDimensionRatio="1:1"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.google.android.material.progressindicator.LinearProgressIndicator
                        android:id="@+id/maidan_progress"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:progress="50"
                        tools:progress="50"
                        app:trackColor="@color/colorSecondary"
                        app:trackThickness="24dp"
                        app:indicatorColor="@color/colorPrimary"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/start_spacer"
                        app:layout_constraintEnd_toStartOf="@id/end_spacer"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="0"
                        android:text="@{DataFormatHelper.numberToK(viewModel.maidanProgress.firstPoints)}"
                        android:layout_marginStart="@dimen/line_spacing"
                        android:textColor="@color/colorAlwaysLightSecondaryLightest"
                        android:textAppearance="@style/TextAppearance.Flashat.Label.Small"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/opponent_score"
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        android:gravity="center_vertical"
                        tools:text="100"
                        android:text="@{DataFormatHelper.numberToK(viewModel.maidanProgress.secondPoints)}"
                        android:paddingEnd="@dimen/line_spacing"
                        android:paddingStart="@dimen/element_spacing"
                        android:clickable="@{viewModel.meAsSpeaker==null}"
                        android:foreground="?attr/selectableItemBackgroundBorderless"
                        android:textColor="@color/colorAlwaysLightPrimaryDarker"
                        android:textAppearance="@style/TextAppearance.Flashat.Label.Small"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.Group
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    tools:visibility="visible"
                    app:goneIfNot="@{viewModel.maidanStatusData.isLive}"
                    app:constraint_referenced_ids="progress_holder,progress_thumb_holder"/>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/speaker_section"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/progress_holder">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/speakers_group"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        app:layout_constraintDimensionRatio="h,6:5"
                        tools:translationX="0dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent">

                        <androidx.constraintlayout.widget.Guideline
                            android:id="@+id/speaker_split_line"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            app:layout_constraintGuide_percent="0.5" />

                        <include
                            android:id="@+id/main_screen"
                            layout="@layout/item_podium_speaker_main"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            tools:visibility="visible"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@+id/speaker_split_line" />

                        <include
                            android:id="@+id/main_screen_two"
                            layout="@layout/item_podium_speaker_main"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            tools:visibility="visible"
                            app:iAmManager="@{viewModel.iAmManager}"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/speaker_split_line" />

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/won_left_layout"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/element_spacing"
                            android:paddingHorizontal="@dimen/element_spacing"
                            app:goneIfNot="@{viewModel.maidanProgress.showWonCount}"
                            app:orGoneIf="@{viewModel.mainScreenSpeakerId==null}"
                            android:paddingVertical="2dp"
                            android:background="@drawable/bg_podium_maidan_need_chip"
                            app:layout_constraintTop_toTopOf="@id/main_screen"
                            app:layout_constraintStart_toStartOf="@id/main_screen">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/won_left_icon"
                                android:layout_width="wrap_content"
                                android:layout_height="16dp"
                                android:adjustViewBounds="true"
                                app:srcCompat="@drawable/ic_podium_maidan_trophy"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"/>

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/won_left_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{@string/podium_maidan_won_count(viewModel.maidanProgress.firstWonString)}"
                                tools:text="Won 1"
                                android:layout_marginStart="@dimen/line_spacing"
                                android:textAppearance="@style/TextAppearance.Flashat.Label.Small"
                                android:textColor="@color/textColorOnPrimary"
                                android:textStyle="bold"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toEndOf="@id/won_left_icon"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"/>

                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/needs_layout"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/element_spacing"
                            app:goneIfNull="@{viewModel.likesNeeded}"
                            android:paddingHorizontal="@dimen/element_spacing"
                            android:paddingVertical="2dp"
                            android:background="@drawable/bg_podium_maidan_need_chip"
                            app:layout_constraintTop_toBottomOf="@id/won_left_layout"
                            app:layout_constraintStart_toStartOf="@id/main_screen">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/need_tv"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/podium_maidan_likes_need"
                                android:textAppearance="@style/TextAppearance.Flashat.Label.Small"
                                android:textColor="@color/textColorOnPrimary"
                                android:textStyle="bold"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/need_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                tools:text="100"
                                android:text="@{viewModel.likesNeeded.toString()}"
                                android:layout_marginStart="@dimen/line_spacing"
                                android:textAppearance="@style/TextAppearance.Flashat.Label.Small"
                                android:textColor="@color/colorSecondary"
                                android:textStyle="bold"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toEndOf="@id/need_tv"
                                app:layout_constraintTop_toTopOf="parent" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="wrap_content"
                                android:layout_height="12dp"
                                android:adjustViewBounds="true"
                                android:layout_marginStart="@dimen/line_spacing"
                                app:srcCompat="@drawable/ic_coin"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toEndOf="@id/need_count"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"/>

                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/won_right_layout"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/element_spacing"
                            android:paddingHorizontal="@dimen/element_spacing"
                            app:goneIfNot="@{viewModel.maidanProgress.showWonCount}"
                            app:orGoneIf="@{viewModel.secondMainScreenSpeakerId==null}"
                            android:paddingVertical="2dp"
                            android:background="@drawable/bg_podium_maidan_need_chip"
                            app:layout_constraintTop_toTopOf="@id/main_screen_two"
                            app:layout_constraintEnd_toEndOf="@id/main_screen_two">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/won_right_icon"
                                android:layout_width="wrap_content"
                                android:layout_height="16dp"
                                android:adjustViewBounds="true"
                                app:srcCompat="@drawable/ic_podium_maidan_trophy"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"/>

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/won_right_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{@string/podium_maidan_won_count(viewModel.maidanProgress.secondWonString)}"
                                tools:text="Won 2"
                                android:layout_marginStart="@dimen/line_spacing"
                                android:textAppearance="@style/TextAppearance.Flashat.Label.Small"
                                android:textColor="@color/textColorOnPrimary"
                                android:textStyle="bold"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toEndOf="@id/won_right_icon"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"/>

                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/second_eds"
                            android:layout_width="0dp"
                            android:layout_height="0dp"
                            android:background="@color/colorAlwaysDarkSurfaceSecondary"
                            android:visibility="gone"
                            android:clickable="true"
                            android:foreground="?attr/selectableItemBackgroundBorderless"
                            app:goneIfNot="@{viewModel.maidanCompetitorStatus==MaidanCompetitorStatus.WAITING}"
                            app:layout_constraintBottom_toBottomOf="@id/main_screen_two"
                            app:layout_constraintEnd_toEndOf="@id/main_screen_two"
                            app:layout_constraintStart_toStartOf="@id/main_screen_two"
                            app:layout_constraintTop_toTopOf="@id/main_screen_two"
                            tools:visibility="gone">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/eds_icon"
                                android:layout_width="80dp"
                                android:layout_height="wrap_content"
                                android:layout_margin="@dimen/activity_margin"
                                android:adjustViewBounds="true"
                                app:layout_constrainedWidth="true"
                                app:layout_constraintBottom_toTopOf="@+id/eds_text"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintHorizontal_bias="0.5"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintVertical_chainStyle="packed"
                                app:srcCompat="@drawable/ic_podium_maidan_fist" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/eds_text"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="@dimen/activity_margin"
                                android:text="@string/podium_maidan_eds_message"
                                android:textAlignment="center"
                                android:textColor="@color/textColorAlwaysDarkSecondaryLight"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintHorizontal_bias="0.5"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/eds_icon" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/second_eds_new_challenge"
                            android:layout_width="0dp"
                            android:layout_height="0dp"
                            android:background="@color/colorAlwaysDarkSurfaceSecondary"
                            android:visibility="gone"
                            app:goneIfNot="@{viewModel.maidanCompetitorStatus==MaidanCompetitorStatus.CHALLENGE_NEW}"
                            app:layout_constraintBottom_toBottomOf="@id/main_screen_two"
                            app:layout_constraintEnd_toEndOf="@id/main_screen_two"
                            app:layout_constraintStart_toStartOf="@id/main_screen_two"
                            app:layout_constraintTop_toTopOf="@id/main_screen_two"
                            tools:visibility="gone">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/eds_new_icon"
                                android:layout_width="80dp"
                                android:layout_height="wrap_content"
                                android:layout_margin="@dimen/activity_margin"
                                android:adjustViewBounds="true"
                                app:layout_constrainedWidth="true"
                                app:layout_constraintBottom_toTopOf="@+id/challenge_new_button"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintHorizontal_bias="0.5"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintVertical_chainStyle="packed"
                                app:srcCompat="@drawable/ic_podium_maidan_fist" />

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/challenge_new_button"
                                style="@style/Widget.Flashat.SmallRoundButton.Secondary"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                app:layout_constrainedWidth="true"
                                android:layout_marginTop="@dimen/activity_margin"
                                android:text="@string/podium_maidan_challenge_new"
                                android:visibility="visible"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintHorizontal_bias="0.5"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/eds_new_icon" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/second_eds_invite"
                            android:layout_width="0dp"
                            android:layout_height="0dp"
                            android:background="@color/colorAlwaysDarkSurfaceSecondary"
                            android:visibility="gone"
                            android:clickable="true"
                            android:foreground="?attr/selectableItemBackgroundBorderless"
                            app:goneIfNot="@{viewModel.maidanCompetitorStatus==MaidanCompetitorStatus.INVITE}"
                            app:layout_constraintBottom_toBottomOf="@id/main_screen_two"
                            app:layout_constraintEnd_toEndOf="@id/main_screen_two"
                            app:layout_constraintStart_toStartOf="@id/main_screen_two"
                            app:layout_constraintTop_toTopOf="@id/main_screen_two"
                            tools:visibility="gone">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/eds_invite_icon"
                                android:layout_width="80dp"
                                android:layout_height="wrap_content"
                                android:layout_margin="@dimen/activity_margin"
                                android:adjustViewBounds="true"
                                app:layout_constrainedWidth="true"
                                app:layout_constraintBottom_toTopOf="@+id/eds_invite_text"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintHorizontal_bias="0.5"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintVertical_chainStyle="packed"
                                app:srcCompat="@drawable/ic_maidan_main_invite" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/eds_invite_text"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="@dimen/activity_margin"
                                android:text="@string/podium_maidan_invite"
                                android:textAlignment="center"
                                android:textColor="@color/textColorAlwaysDarkSecondaryLight"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintHorizontal_bias="0.5"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/eds_invite_icon" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/main_screen_one_result"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:adjustViewBounds="true"
                            android:visibility="gone"
                            tools:srcCompat="@drawable/im_maidan_winner_gif"
                            android:layout_margin="@dimen/double_margin"
                            app:layout_constraintBottom_toBottomOf="@id/main_screen"
                            app:layout_constraintEnd_toEndOf="@id/main_screen"
                            app:layout_constraintStart_toStartOf="@id/main_screen"
                            app:layout_constraintTop_toTopOf="@id/main_screen" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/main_screen_two_result"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:adjustViewBounds="true"
                            android:visibility="gone"
                            tools:srcCompat="@drawable/im_maidan_loser_gif"
                            android:layout_margin="@dimen/double_margin"
                            app:layout_constraintBottom_toBottomOf="@id/main_screen_two"
                            app:layout_constraintEnd_toEndOf="@id/main_screen_two"
                            app:layout_constraintStart_toStartOf="@id/main_screen_two"
                            app:layout_constraintTop_toTopOf="@id/main_screen_two" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/timer_holder"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:goneIf="@{viewModel.maidanStatusData.statusEnum == MaidanStatus.WAITING}"
                    android:animateLayoutChanges="true"
                    app:layout_constraintTop_toBottomOf="@+id/progress_holder"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent">

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:background="@color/colorMaidanTimerBackground"
                        app:layout_constraintStart_toEndOf="@id/timer_decor_left"
                        app:layout_constraintEnd_toStartOf="@id/timer_decor_right"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"/>

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/timer_decor_right"
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        android:adjustViewBounds="true"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:srcCompat="@drawable/bg_podium_maidan_timer_right"
                        app:layout_constraintEnd_toEndOf="parent"
                        />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/timer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/TextAppearance.Flashat.Headline5"
                        android:textColor="@color/textColorOnPrimary"
                        android:layout_marginEnd="@dimen/element_spacing"
                        android:paddingVertical="2dp"
                        android:textStyle="bold"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/timer_decor_right"
                        tools:text="3:22" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/timer_state"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/TextAppearance.Flashat.Body2"
                        android:textAllCaps="true"
                        android:textColor="@color/colorSecondary"
                        app:goneIfNullOrBlank="@{presenter.getTimerText(viewModel.maidanStatusData.statusEnum)}"
                        android:text="@{presenter.getTimerText(viewModel.maidanStatusData.statusEnum)}"
                        tools:text="@string/podium_maidan_talk_time"
                        android:visibility="gone"
                        tools:visibility="visible"
                        android:paddingVertical="2dp"
                        android:textStyle="bold"
                        android:layout_marginEnd="@dimen/element_spacing"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/timer" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/timer_decor_left"
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        android:adjustViewBounds="true"
                        android:layout_marginEnd="@dimen/element_spacing"
                        app:srcCompat="@drawable/bg_podium_maidan_timer_left"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/timer_state"
                        />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/element_spacing"
                    android:paddingHorizontal="@dimen/extra_margin"
                    android:clipToPadding="false"
                    app:goneIfNot="@{viewModel.canChallengeAgain}"
                    app:layout_constraintTop_toBottomOf="@id/timer_holder"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/challenge_again_button"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        style="@style/Widget.Flashat.SmallRoundButton.Secondary"
                        android:text="@string/podium_maidan_challenge_again"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"/>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/challenge_end_button"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        style="@style/Widget.Flashat.SmallRoundButton.Negative"
                        android:text="@string/podium_maidan_exit_prompt_ok"
                        app:layout_constraintTop_toBottomOf="@id/challenge_again_button"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"/>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/progress_thumb_holder"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="24dp"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    android:animateLayoutChanges="true"
                    app:layout_constraintBottom_toBottomOf="@id/progress_holder"
                    app:layout_constraintStart_toStartOf="@id/progress_holder"
                    app:layout_constraintTop_toTopOf="@id/progress_holder"
                    app:layout_constraintEnd_toStartOf="@id/progress_holder">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/progress_thumb"
                        android:layout_width="40dp"
                        android:layout_height="wrap_content"
                        android:adjustViewBounds="true"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/ic_podium_maidan_fist_filled"
                        tools:translationX="70dp" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/supporters_holder"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_margin="@dimen/element_spacing"
                    android:clickable="true"
                    android:elevation="1dp"
                    app:goneIfNot="@{viewModel.maidanStatusData.isLive}"
                    app:cardBackgroundColor="@color/colorSurfaceSecondaryDark"
                    app:cardCornerRadius="4dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/speaker_section">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/top_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/element_spacing"
                            android:text="@string/podium_maidan_top"
                            android:textAlignment="center"
                            android:textAllCaps="true"
                            android:textAppearance="@style/TextAppearance.Flashat.Label.Bold"
                            android:textColor="@color/colorPrimaryLightSecondaryDark"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/supporters_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/element_spacing"
                            android:text="@string/challenge_supporters_title"
                            android:textAlignment="center"
                            android:textAllCaps="true"
                            android:textAppearance="@style/TextAppearance.Flashat.Label.Smaller.Bold"
                            android:textColor="@color/colorPrimaryLightSecondaryDark"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/top_text" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/top_supporters"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:minWidth="64dp"
                            android:orientation="vertical"
                            android:paddingVertical="@dimen/element_spacing"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/supporters_text"
                            tools:itemCount="4"
                            tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                            tools:listitem="@layout/item_podium_maidan_supporters" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </com.google.android.material.card.MaterialCardView>

                <include
                    android:id="@+id/likes_container"
                    layout="@layout/item_podium_likes_container" />

                <com.kennyc.view.MultiStateView
                    android:id="@+id/live_chat_multiStateView"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toTopOf="@id/challenge_prompt"
                    app:layout_constraintEnd_toStartOf="@+id/supporters_holder"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/speaker_section"
                    app:msv_emptyView="@layout/layout_list_state_empty"
                    app:msv_errorView="@layout/layout_list_state_error"
                    app:msv_viewState="content">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/live_chat"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="@dimen/line_spacing"
                        tools:itemCount="6"
                        tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:listitem="@layout/item_podium_live_chat" />

                </com.kennyc.view.MultiStateView>

                <com.kennyc.view.MultiStateView
                    android:id="@+id/chat_textBox_multiStateView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:background="@color/colorSurface"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/supporters_holder"
                    app:layout_constraintStart_toStartOf="parent"
                    app:msv_emptyView="@layout/layout_podium_chat_paused_empty"
                    app:msv_errorView="@layout/layout_list_state_error"
                    app:msv_viewState="content">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/chat_input_holder"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/colorSurface"
                        android:elevation="6dp"
                        app:goneIfNot="@{viewModel.canSendChats}">

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/chat_input"
                            style="@style/Widget.Flashat.GreyTextInput.Small"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/element_spacing"
                            android:layout_marginStart="@dimen/element_spacing"
                            android:layout_marginEnd="@dimen/element_spacing"
                            app:hintAnimationEnabled="false"
                            app:hintEnabled="false"
                            app:counterEnabled="true"
                            app:counterMaxLength="150"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@+id/chat_send_button"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/input_comment"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:hint="@string/chat_input_hint"
                                android:inputType="textShortMessage|textMultiLine"
                                android:maxLines="1"
                                android:maxLength="150"
                                android:text="@={viewModel.chatText}" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/chat_send_button"
                            style="@style/Widget.Flashat.Button.TextButton.IconOnly.Small"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:enabled="@{viewModel.chatText.trim().length>0}"
                            app:icon="@drawable/ic_chat_send"
                            app:iconTint="@color/colorPrimary"
                            android:layout_marginVertical="@dimen/element_spacing"
                            app:layout_constraintVertical_bias="0.0"
                            android:layout_marginEnd="@dimen/element_spacing"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </com.kennyc.view.MultiStateView>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/challenge_prompt"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:goneIfNot="@{viewModel.maidanStatusData.isLive}"
                    app:orGoneIf="@{viewModel.iAmMaidanCompetitor}"
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:layout_marginEnd="@dimen/activity_margin"
                    app:layout_constraintBottom_toTopOf="@id/chat_textBox_multiStateView"
                    app:layout_constraintEnd_toStartOf="@+id/supporters_holder"
                    app:layout_constraintStart_toStartOf="parent">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/like_button"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        style="@style/Widget.Flashat.MaidanLikeButton"
                        app:icon="@drawable/ic_podium_like"
                        android:enabled="@{!viewModel.maidanLikeSending}"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/gift_button"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        style="@style/Widget.Flashat.MaidanLikeButton"
                        app:icon="@drawable/ic_gift_square"
                        android:enabled="@{!viewModel.maidanLikeSending}"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/challenge_prompt_card"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        app:cardBackgroundColor="@color/colorSurfaceSecondaryDark"
                        app:cardCornerRadius="6dp"
                        app:cardElevation="1dp"
                        android:layout_marginHorizontal="@dimen/activity_margin"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintStart_toEndOf="@id/like_button"
                        app:layout_constraintEnd_toStartOf="@id/gift_button"
                        >

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/coin_given_title"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/line_spacing"
                                android:text="@string/podium_maidan_given"
                                android:textAppearance="@style/TextAppearance.Flashat.Body2"
                                android:textColor="@color/textColorSecondary"
                                app:layout_constraintBottom_toTopOf="@+id/coin_balance_title"
                                app:layout_constraintEnd_toStartOf="@+id/coin_given"
                                app:layout_constraintHorizontal_bias="0.5"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintVertical_chainStyle="packed" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/coin_given"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{DataFormatHelper.numberToK(viewModel.maidanCoinBalance.first)}"
                                android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                                android:textColor="@color/textColorPrimary"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintHorizontal_bias="0.5"
                                app:layout_constraintStart_toEndOf="@+id/coin_given_title"
                                app:layout_constraintTop_toTopOf="@id/coin_given_title"
                                tools:text="254" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/coin_balance_title"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/line_spacing"
                                android:text="@string/podium_maidan_balance"
                                android:textAppearance="@style/TextAppearance.Flashat.Body2"
                                android:textColor="@color/textColorSecondary"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toStartOf="@+id/coin_balance"
                                app:layout_constraintHorizontal_bias="0.5"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/coin_given_title" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/coin_balance"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{DataFormatHelper.numberToK(viewModel.maidanCoinBalance.second)}"
                                android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                                android:textColor="@color/textColorPrimary"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintHorizontal_bias="0.5"
                                app:layout_constraintStart_toEndOf="@+id/coin_balance_title"
                                app:layout_constraintTop_toTopOf="@id/coin_balance_title"
                                tools:text="254" />

                            <com.google.android.material.progressindicator.LinearProgressIndicator
                                android:id="@+id/linearProgressIndicator"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:indeterminate="true"
                                app:goneIfNot="@{viewModel.maidanLikeSending}"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toStartOf="parent" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.google.android.material.card.MaterialCardView>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <!--                <androidx.recyclerview.widget.RecyclerView-->
                <!--                    android:id="@+id/live_likes"-->
                <!--                    android:layout_width="0dp"-->
                <!--                    android:layout_height="0dp"-->
                <!--                    android:padding="@dimen/line_spacing"-->
                <!--                    tools:itemCount="6"-->
                <!--                    tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"-->
                <!--                    tools:listitem="@layout/item_podium_live_chat_maidan"-->
                <!--                    app:layout_constraintBottom_toTopOf="@id/challenge_prompt"-->
                <!--                    app:layout_constraintEnd_toStartOf="@+id/supporters_holder"-->
                <!--                    app:layout_constraintStart_toStartOf="parent"-->
                <!--                    app:layout_constraintTop_toBottomOf="@+id/speaker_section"/>-->

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>
