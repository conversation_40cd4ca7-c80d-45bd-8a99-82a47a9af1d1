<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="Widget.Flashat.Podium.ActionFAB" parent="Widget.Flashat.Button.IconOnly">
        <item name="android:textAppearance">@style/TextAppearance.Flashat.Subtitle2</item>
        <item name="android:padding">8dp</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:minWidth">40dp</item>
        <item name="android:minHeight">40dp</item>
        <item name="iconSize">24dp</item>
        <item name="shapeAppearance">@style/ShapeAppearance.Flashat.Podium.ActionFAB</item>
    </style>

    <style name="Widget.Flashat.Podium.ActionFAB.Secondary">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Secondary</item>
    </style>

    <style name="Widget.Flashat.Podium.ActionFAB.FullIcon">
        <item name="android:padding">0dp</item>
        <item name="iconSize">40dp</item>
        <item name="iconTint">@null</item>
    </style>

    <style name="Widget.Flashat.Podium.ActionFAB.Secondary.PrimaryIcon">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Podium.ActionFAB.Secondary.PrimaryIcon</item>
    </style>

    <style name="ThemeOverlay.Flashat.Podium.ActionFAB.Secondary.PrimaryIcon" parent="">
        <item name="colorPrimary">@color/colorSecondary</item>
        <item name="colorOnPrimary">@color/colorPrimary</item>
        <item name="colorOnSurface">@color/colorSecondary</item>
    </style>

    <style name="ShapeAppearance.Flashat.Podium.ActionFAB" parent="ShapeAppearance.MaterialComponents.SmallComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">24dp</item>
    </style>

    <style name="Widget.Flashat.PodiumUpgradeDialog" parent="MaterialAlertDialog.MaterialComponents">
        <item name="buttonBarNeutralButtonStyle">@style/Widget.Flashat.PodiumUpgradeDialogNeutralButton</item>
    </style>

    <style name="Widget.Flashat.PodiumUpgradeDialogNeutralButton" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/colorSecondary</item>
    </style>

    <style name="Widget.Flashat.PodiumBottomSheet" parent="ThemeOverlay.MaterialComponents.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/Widget.Flashat.PodiumBottomSheet.Modal</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style>

    <style name="Widget.Flashat.PodiumBottomSheet.Modal" parent="Widget.MaterialComponents.BottomSheet.Modal">
        <!--        <item name="android:background">@color/transparent</item>-->
        <item name="backgroundTint">@color/colorSurface</item>
        <item name="shapeAppearance">@style/ShapeAppearance.Flashat.PodiumBottomSheet</item>
        <item name="android:clipToOutline" tools:targetApi="s">true</item>
        <item name="behavior_skipCollapsed">true</item>
        <item name="behavior_fitToContents">false</item>
        <item name="behavior_expandedOffset">@dimen/flash_comment_sheet_offset</item>
        <item name="behavior_halfExpandedRatio">0.55</item>
        <item name="shouldRemoveExpandedCorners">false</item>
    </style>

    <style name="ShapeAppearance.Flashat.PodiumBottomSheet" parent="ShapeAppearance.MaterialComponents.LargeComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">@dimen/settingsBottomSheetRadius</item>
    </style>

    <style name="Widget.Flashat.QuestionChallengeButton" parent="Widget.Flashat.SmallRoundedButton.SmallText">
        <item name="backgroundTint">@drawable/sel_podium_challenge_background</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/textColorOnPrimary</item>
        <item name="android:paddingStart">@dimen/element_spacing</item>
        <item name="android:paddingEnd">@dimen/element_spacing</item>
        <item name="android:minWidth">70dp</item>
    </style>

    <style name="Widget.Flashat.ConfourSpeakerTileChip" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">30dp</item>
        <item name="cardElevation">0dp</item>
        <item name="cardBackgroundColor">#88FFFFFF</item>
        <item name="strokeColor">#33FFFFFF</item>
        <item name="strokeWidth">3dp</item>
    </style>

    <style name="Widget.Flashat.ConfourSpeakerTileChip.Secondary">
        <item name="cardElevation">1dp</item>
        <item name="cardBackgroundColor">@color/colorAlwaysLightSecondary</item>
        <item name="strokeColor">@color/colorAlwaysLightSecondaryLightest</item>
        <item name="strokeWidth">3dp</item>
    </style>

    <style name="Widget.Flashat.ConfourParticipantChip" parent="Widget.MaterialComponents.CardView">
        <item name="cardElevation">1dp</item>
        <item name="cardBackgroundColor">@color/colorAlwaysLightPrimary</item>
        <item name="strokeColor">@color/colorAlwaysLightPrimaryLightest</item>
        <item name="cardCornerRadius">8dp</item>
    </style>

    <style name="Widget.Flashat.ConfourSpeakerTileChip.Level" parent="Widget.Flashat.ConfourSpeakerTileChip.Secondary">
        <item name="cardCornerRadius">6dp</item>
    </style>

    <style name="Widget.Flashat.ConFour.PlayerTimer" parent="Widget.MaterialComponents.CircularProgressIndicator">'
        <item name="android:elevation">6dp</item>
        <item name="indicatorColor">@color/colorSecondary</item>
        <item name="indicatorSize">50dp</item>
        <item name="trackThickness">6dp</item>
        <item name="trackCornerRadius">3dp</item>
    </style>

    <style name="Widget.Flashat.ShareCopyButton" parent="Widget.Flashat.MiniRoundedButton.Outline.Primary">'
        <item name="shapeAppearance">@style/ShapeAppearance.Flashat.ShareCopyButton</item>
    </style>

    <style name="ShapeAppearance.Flashat.ShareCopyButton" parent="ShapeAppearance.MaterialComponents.MediumComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">20dp</item>
    </style>

    <style name="Widget.Flashat.Button.AloneSendButton" parent="Widget.Flashat.Button.IconOnly">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Inverse.OnPrimary</item>
        <item name="shapeAppearance">@style/ShapeAppearance.Flashat.AloneSendButton</item>
    </style>

    <style name="ShapeAppearance.Flashat.AloneSendButton" parent="ShapeAppearance.MaterialComponents.MediumComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">32dp</item>
    </style>

    <style name="Widget.Flashat.Button.AloneGiftButton" parent="Widget.Flashat.Button.TextButton.IconOnly">
        <item name="android:minWidth">48dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:padding">2dp</item>
        <item name="iconSize">24dp</item>
        <item name="iconGravity">top</item>
        <item name="iconTint">@null</item>
    </style>

    <style name="TextAppearance.Flashat.Podium.SpeakerTile" parent="TextAppearance.Flashat.Label.Tiny">
    </style>

    <style name="Widget.Flashat.MainScreenButton" parent="Widget.Flashat.Button.IconOnly.Small">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.MainScreenButton</item>
        <item name="shapeAppearance">@style/ShapeAppearance.Flashat.MainScreenButton</item>
    </style>

    <style name="ThemeOverlay.Flashat.MainScreenButton" parent="">
        <item name="colorPrimary">#55000000</item>
        <item name="colorOnPrimary">@color/textColorOnPrimary</item>
        <item name="colorOnSurface">@color/textColorOnPrimary</item>
    </style>

    <style name="ShapeAppearance.Flashat.MainScreenButton" parent="ShapeAppearance.MaterialComponents.MediumComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">32dp</item>
    </style>

    <style name="Widget.Flashat.MaidanFollowButton" parent="Widget.Flashat.TinyRoundedButton.Primary">
        <item name="shapeAppearance">@style/ShapeAppearance.Flashat.MaidanFollowButton</item>
        <item name="android:paddingHorizontal">12dp</item>
    </style>

    <style name="ShapeAppearance.Flashat.MaidanFollowButton" parent="ShapeAppearance.MaterialComponents.MediumComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">25dp</item>
    </style>

    <style name="Widget.Flashat.MaidanShareButton" parent="Widget.Flashat.Button.TextButton.IconOnly">
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:padding">12dp</item>
        <item name="android:minWidth">60dp</item>
        <item name="android:minHeight">60dp</item>
        <item name="iconSize">36dp</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Primary</item>
    </style>

    <style name="Widget.Flashat.MaidanListButton" parent="Widget.Flashat.Button.TextButton.IconOnly">
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:padding">12dp</item>
        <item name="android:minWidth">60dp</item>
        <item name="android:minHeight">60dp</item>
        <item name="iconSize">36dp</item>
        <item name="iconTint">@null</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Primary</item>
    </style>

    <style name="Widget.Flashat.MaidanPromptButton" parent="Widget.Flashat.LargeRoundedButton">
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:padding">4dp</item>
        <item name="android:minWidth">60dp</item>
        <item name="android:minHeight">60dp</item>
        <item name="iconSize">48dp</item>
        <item name="iconTint">@null</item>
        <item name="iconGravity">textStart</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Primary</item>
    </style>

    <style name="Widget.Flashat.MaidanPromptButton.Outline" parent="Widget.Flashat.LargeRoundedButton.Outline.Primary">
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:padding">12dp</item>
        <item name="android:minWidth">60dp</item>
        <item name="android:minHeight">60dp</item>
        <item name="iconSize">36dp</item>
        <item name="iconTint">@null</item>
        <item name="iconGravity">textStart</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Primary</item>
    </style>

    <style name="Widget.Flashat.MaidanPromptButton.Outline.Negative">
        <item name="iconTint">@color/colorError</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Negative</item>
    </style>

    <style name="Widget.Flashat.MaidanListButton.Small">
        <item name="android:padding">6dp</item>
        <item name="android:minWidth">40dp</item>
        <item name="android:minHeight">40dp</item>
        <item name="iconSize">28dp</item>
    </style>

    <style name="Widget.Flashat.CreateMaidanButton" parent="Widget.Flashat.Button.IconOnly">
        <item name="iconSize">36dp</item>
        <item name="iconGravity">textStart</item>

        <item name="shapeAppearance">@style/ShapeAppearance.Flashat.CreateMaidanButton</item>
    </style>

    <style name="Widget.Flashat.CreateMaidanButton.Negative" parent="Widget.Flashat.Button.TextButton.IconOnly">
        <item name="iconSize">36dp</item>
        <item name="iconGravity">textStart</item>
        <item name="shapeAppearance">@style/ShapeAppearance.Flashat.CreateMaidanButton</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Negative</item>
    </style>

    <style name="ShapeAppearance.Flashat.CreateMaidanButton" parent="ShapeAppearance.MaterialComponents.MediumComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">8dp</item>
    </style>

    <style name="Widget.Flashat.MaidanLikeButton" parent="Widget.Flashat.Button.IconOnly">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.MaidanLikeButton</item>
        <item name="iconSize">36dp</item>
        <item name="iconTint">@null</item>
        <item name="android:padding">8dp</item>
        <item name="android:minWidth">52dp</item>
        <item name="android:minHeight">52dp</item>
        <item name="iconGravity">textStart</item>
    </style>

    <style name="ThemeOverlay.Flashat.MaidanLikeButton" parent="">
        <item name="colorPrimary">@color/colorSurfaceSecondaryDark</item>
        <item name="colorOnPrimary">@color/textColorSecondary</item>
        <item name="colorOnSurface">@color/colorSurfaceSecondaryDark</item>
    </style>

    <style name="Widget.Flashat.PaidLikeButton" parent="Widget.Flashat.Button.TextButton.IconOnly">
        <item name="iconSize">36dp</item>
        <item name="iconTint">@null</item>
        <item name="android:padding">10dp</item>
        <item name="android:minWidth">56dp</item>
        <item name="android:minHeight">56dp</item>
        <item name="iconGravity">textStart</item>
    </style>

</resources>