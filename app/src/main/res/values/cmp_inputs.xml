<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- Text Inputs -->

    <style name="Widget.Flashat.LocaleTextInput" parent="">
        <item name="android:textDirection">firstStrong</item>
        <item name="android:textAlignment">textStart</item>
    </style>

    <style name="Widget.Flashat.LocaleTextInput.OutlinedBox.Dense" parent="Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense">
        <item name="android:textDirection">firstStrong</item>
        <item name="android:textAlignment">textStart</item>
    </style>

    <style name="Widget.Flashat.GreyTextInput" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense">
        <item name="android:textColor">@color/textColorSecondary</item>
        <item name="android:textColorHint">@color/textColorHint</item>
        <item name="hintTextColor">@color/textColorHint</item>
        <item name="boxBackgroundColor">@color/textInputBackground</item>
        <item name="boxStrokeWidth">0dp</item>
        <item name="boxStrokeWidthFocused">0dp</item>
        <item name="hintAnimationEnabled">false</item>
        <item name="hintEnabled">false</item>
        <item name="android:textDirection">firstStrong</item>
        <item name="errorIconDrawable">@null</item>
        <item name="android:textAlignment">textStart</item>
        <item name="counterTextColor">@color/textColorSecondaryLight</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.GreyTextInput</item>
    </style>

    <style name="ThemeOverlay.Flashat.GreyTextInput" parent="">
        <item name="colorPrimary">@color/textColorSecondary</item>
        <item name="colorOnSurface">@color/textColorSecondary</item>
        <item name="colorError">@color/colorError</item>
        <item name="editTextStyle">@style/Widget.Flashat.LocaleTextInput.OutlinedBox.Dense</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.Flashat.Body2</item>
        <item name="textAppearanceCaption">@style/TextAppearance.Flashat.Label.Small</item>
    </style>

    <style name="Widget.Flashat.GreyTextInput.NoBG">
        <item name="boxBackgroundColor">@color/transparent</item>
    </style>

    <style name="Widget.Flashat.GreyTextInput.AlwaysDark">
        <item name="android:textColor">@color/textColorAlwaysDarkSecondary</item>
        <item name="android:textColorHint">@color/textColorAlwaysDarkHint</item>
        <item name="hintTextColor">@color/textColorAlwaysDarkHint</item>
        <item name="boxBackgroundColor">@color/textInputAlwaysDarkBackground</item>
    </style>

    <style name="Widget.Flashat.GreyTextInput.AutoComplete" parent="">
        <item name="android:background">@color/transparent</item>
        <item name="android:backgroundTint">@color/transparent</item>
        <item name="android:textDirection">firstStrong</item>
        <item name="android:textAlignment">textStart</item>
    </style>

    <style name="Widget.Flashat.GreyTextInput.Password" parent="Widget.Flashat.GreyTextInput">
        <item name="endIconDrawable">@drawable/ic_showhide_password</item>
        <item name="endIconMode">password_toggle</item>
    </style>

    <style name="Widget.Flashat.GreyTextInput.Small" parent="Widget.Flashat.GreyTextInput">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.GreyTextInput.Small</item>
    </style>

    <style name="ThemeOverlay.Flashat.GreyTextInput.Small">
        <item name="editTextStyle">@style/TextStyle.Flashat.OutlinedBox.Dense.Small</item>
    </style>

    <style name="TextStyle.Flashat.OutlinedBox.Dense.Small" parent="Widget.Flashat.LocaleTextInput.OutlinedBox.Dense">
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:paddingStart">10dp</item>
        <item name="android:paddingEnd">10dp</item>
    </style>

    <!-- Location Autocomplete Input -->

    <style name="Widget.Flashat.WhiteTextInput" parent="Widget.Flashat.GreyTextInput">
        <item name="boxBackgroundColor">@color/colorSurface</item>
    </style>

    <style name="Widget.Flashat.GreyTextInput.ExposedDropdownMenu" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu">
        <item name="hintTextColor">@color/textColorHint</item>
        <item name="boxBackgroundColor">@color/textInputBackground</item>
        <item name="boxStrokeWidth">0dp</item>
        <item name="boxStrokeWidthFocused">0dp</item>
        <item name="errorTextAppearance">@style/TextAppearance.Flashat.Label.Small</item>
        <item name="errorTextColor">@color/colorError</item>
    </style>

    <style name="Widget.Flashat.GreyTextInput.ExposedDropdownMenu.NoBG">
        <item name="boxBackgroundColor">@color/transparent</item>
    </style>

    <style name="Widget.Flashat.WhiteTextInput.LocationSearch" parent="Widget.Flashat.GreyTextInput">
        <item name="endIconDrawable">@drawable/ic_close_circle_filled</item>
        <item name="android:textSize">18sp</item>
        <item name="endIconMode">clear_text</item>
        <item name="endIconTint">@null</item>
    </style>

    <style name="Widget.Flashat.OutlinedTextInput" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.OutlinedTextInput</item>
    </style>

    <style name="ThemeOverlay.Flashat.OutlinedTextInput" parent="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense">
        <item name="colorPrimary">@color/textColorSecondary</item>
        <item name="colorOnSurface">@color/textColorSecondary</item>
        <item name="colorError">@color/colorError</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.Flashat.Body2</item>
        <item name="textAppearanceCaption">@style/TextAppearance.Flashat.Label.Small</item>
        <item name="editTextStyle">@style/Widget.Flashat.LocaleTextInput.OutlinedBox.Dense</item>
    </style>

    <style name="Widget.Flashat.OutlinedTextInput.ExposedDropdownMenu" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.OutlinedTextInput.ExposedDropdownMenu</item>
    </style>

    <style name="ThemeOverlay.Flashat.OutlinedTextInput.ExposedDropdownMenu" parent="ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense">
        <item name="colorPrimary">@color/textColorSecondary</item>
        <item name="colorOnSurface">@color/textColorSecondary</item>
        <item name="colorError">@color/colorError</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.Flashat.Body2</item>
        <item name="textAppearanceCaption">@style/TextAppearance.Flashat.Label.Small</item>
    </style>

</resources>