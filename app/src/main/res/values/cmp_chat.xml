<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- Chat Listing -->
    <style name="Widget.Flashat.ChatList.JoinStatus" parent="">
        <item name="android:elevation">1dp</item>
        <item name="android:textAppearance">@style/TextAppearance.Flashat.MessageInvited</item>
        <item name="android:background">@drawable/bg_chat_join_status_chip</item>
        <item name="android:paddingHorizontal">@dimen/element_spacing</item>
        <item name="android:paddingVertical">2dp</item>
    </style>

    <style name="Widget.Flashat.ChatList.UnreadCounter" parent="">
        <item name="android:elevation">1dp</item>
        <item name="android:textAppearance">@style/TextAppearance.Flashat.Caption</item>
        <item name="android:textColor">@color/textColorOnPrimary</item>
        <item name="android:background">@drawable/bg_chat_unread_counter</item>
        <item name="android:paddingHorizontal">2dp</item>
        <item name="android:paddingVertical">1dp</item>
        <item name="android:minWidth">20dp</item>
        <item name="android:textAlignment">center</item>
    </style>

    <style name="Widget.Flashat.ChatEntryButton" parent="Widget.Flashat.Button.TextButton.IconOnly">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.ChatEntryButton</item>
    </style>

    <style name="ThemeOverlay.Flashat.ChatEntryButton" parent="">
        <item name="colorPrimary">@color/colorChatEntryButtons</item>
        <item name="colorOnPrimary">@color/textColorOnPrimary</item>
        <item name="colorOnSurface">@color/colorChatEntryButtons</item>
    </style>

    <!-- Chat Screen -->
    <style name="Widget.Flashat.ChatBubble" parent="">
        <item name="android:elevation">1dp</item>
        <item name="android:minWidth">150dp</item>
        <item name="android:padding">@dimen/element_spacing</item>
    </style>

    <style name="Widget.Flashat.ChatBubble.Incoming">
        <item name="android:background">@drawable/bg_chat_bubble_incoming</item>
        <item name="android:backgroundTint">@color/colorChatDefault</item>
        <item name="android:layout_marginEnd">80dp</item>
        <item name="android:layout_width">0dp</item>
    </style>

    <style name="Widget.Flashat.ChatBubble.Outgoing">
        <item name="android:background">@drawable/bg_chat_bubble_outgoing</item>
        <item name="android:backgroundTint">@color/colorChatOutgoing</item>
        <item name="android:layout_marginStart">80dp</item>
        <item name="android:layout_width">0dp</item>
    </style>

    <style name="Widget.Flashat.ChatBubble.Media" parent="">
        <item name="android:layout_width">match_parent</item>
    </style>

    <style name="Widget.Flashat.ChatBubble.Huddle">
        <item name="android:elevation">0dp</item>
        <item name="android:padding">0dp</item>
        <item name="android:background">@drawable/bg_chat_bubble_huddle_default</item>
        <item name="android:backgroundTint">@null</item>
    </style>

    <style name="Widget.Flashat.ChatBubble.Broadcast" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">8dp</item>
    </style>


    <style name="Widget.Flashat.ChatBubble.Message" parent="Widget.AppCompat.TextView">
        <item name="android:textAppearance">@style/TextAppearance.Flashat.Body2</item>
        <item name="android:textColor">@color/colorChatTextSecondary</item>
        <item name="android:autoLink">web</item>
        <item name="android:textColorLink">@color/colorPrimaryDarker</item>
        <item name="readMoreMaxLines">12</item>
        <item name="readMoreText">@string/chat_read_more</item>
        <item name="readMoreTextAppearance">@style/TextAppearance.Flashat.Body2</item>
        <item name="readMoreTextColor">@color/colorChatReadMoreLink</item>
<!--        <item name="readLessText">@string/chat_read_less</item>-->
<!--        <item name="readLessTextAppearance">@style/TextAppearance.Flashat.Body2</item>-->
<!--        <item name="readLessTextColor">@color/colorPrimary</item>-->
        <item name="readMoreToggleArea">none</item>
    </style>

    <style name="Widget.Flashat.ChatDecor.Date" parent="">
        <item name="android:textAppearance">@style/TextAppearance.Flashat.Subtitle2</item>
        <item name="android:textSize">12sp</item>
        <item name="android:paddingHorizontal">@dimen/activity_margin</item>
        <item name="android:paddingVertical">@dimen/line_spacing</item>
        <item name="android:background">@drawable/bg_chat_date_chip</item>
    </style>

    <style name="Widget.Flashat.ChatDecor.Activity" parent="">
        <item name="android:textAppearance">@style/TextAppearance.Flashat.Label</item>
        <item name="android:textSize">11sp</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">middle</item>
        <item name="android:textColor">@color/textColorSecondary</item>
        <item name="android:paddingHorizontal">@dimen/extra_margin</item>
        <item name="android:paddingVertical">6dp</item>
        <item name="android:background">@drawable/bg_chat_activity_chip</item>
    </style>

    <style name="Widget.Flashat.ChatDecor.DownloadButton" parent="Widget.Flashat.FAB">'
        <item name="fabCustomSize">50dp</item>
        <item name="elevation">0dp</item>
        <item name="backgroundTint">#66000000</item>
        <item name="tint">@color/textColorOnPrimary</item>
        <item name="maxImageSize">33dp</item>
    </style>

    <style name="Widget.Flashat.ChatDecor.DownloadProgress" parent="Widget.MaterialComponents.CircularProgressIndicator">'
        <item name="android:elevation">6dp</item>
        <item name="indicatorColor">@color/textColorOnPrimary</item>
        <item name="indicatorSize">50dp</item>
        <item name="trackThickness">3dp</item>
    </style>

    <style name="Widget.Flashat.ChatDecor.HuddlePostMediaActionFAB" parent="Widget.Flashat.FAB.Inverse">'
        <item name="fabCustomSize">40dp</item>
        <item name="maxImageSize">24dp</item>
    </style>

    <style name="Widget.Flashat.ChatDecor.HuddlePostAttachFAB" parent="Widget.Flashat.FAB">'
        <item name="fabCustomSize">40dp</item>
        <item name="maxImageSize">20dp</item>
    </style>

    <style name="Widget.Flashat.ChatDecor.ForwardFab" parent="Widget.Flashat.FAB">'
        <item name="fabCustomSize">32dp</item>
        <item name="maxImageSize">16dp</item>
    </style>

    <style name="Widget.Flashat.ChatDecor.HuddlePostRecordFAB" parent="Widget.Flashat.FAB">'
    </style>

    <style name="Widget.Flashat.ChatDecor.MediaChooseButton" parent="Widget.Flashat.FAB">'
        <item name="fabCustomSize">54dp</item>
        <item name="maxImageSize">24dp</item>
    </style>


    <style name="Widget.Flashat.ChatDecor.MediaControlButton" parent="Widget.Flashat.Button.TextButton.IconOnly">
        <item name="android:paddingHorizontal">4dp</item>
        <item name="android:paddingVertical">4dp</item>
        <item name="iconSize">24dp</item>

        <item name="android:minWidth">32dp</item>
        <item name="android:minHeight">32dp</item>
    </style>

    <style name="Widget.Flashat.ChatDecor.MediaSlider" parent="Widget.MaterialComponents.Slider">
        <item name="android:paddingVertical">4dp</item>
        <item name="android:height">30dp</item>
        <item name="trackHeight">3dp</item>
        <item name="trackColorInactive">@color/colorChatMediaTrack</item>
        <item name="trackColorActive">@color/colorChatMediaActiveTrack</item>
        <item name="thumbColor">@color/colorChatMediaThumb</item>
        <item name="thumbRadius">6dp</item>
    </style>

    <style name="Widget.Flashat.ChatSearch.Button" parent="Widget.Flashat.Button.TextButton.IconOnly">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.ChatSearch.Button</item>
    </style>

    <style name="ThemeOverlay.Flashat.ChatSearch.Button" parent="">
        <item name="colorPrimary">@color/textColorSecondary</item>
        <item name="colorOnSurface">@color/textColorSecondary</item>
    </style>

    <style name="Widget.Flashat.ChatImageCaption" parent="Widget.MaterialComponents.TextView">
        <item name="android:textAppearance">@style/TextAppearance.Flashat.Label</item>
        <item name="android:paddingVertical">@dimen/line_spacing</item>
        <item name="android:paddingHorizontal">@dimen/element_spacing</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/bg_chat_image_caption</item>
        <item name="android:maxLines">10</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="Widget.Flashat.HuddlePostBottomSheet" parent="ThemeOverlay.MaterialComponents.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/Widget.Flashat.HuddlePostBottomSheet.Modal</item>
    </style>

    <style name="Widget.Flashat.HuddlePostBottomSheet.Modal" parent="Widget.MaterialComponents.BottomSheet.Modal">
        <item name="backgroundTint">@color/colorSurface</item>
        <item name="shapeAppearance">@style/ShapeAppearance.Flashat.HuddlePostBottomSheet</item>
<!--        <item name="behavior_peekHeight">0dp</item>-->
        <item name="behavior_fitToContents">false</item>
        <item name="behavior_skipCollapsed">false</item>
        <item name="behavior_draggable">true</item>
        <item name="behavior_expandedOffset">@dimen/chat_huddle_post_sheet_offset</item>
<!--        <item name="maxHeight">600dp</item>-->
        <item name="shouldRemoveExpandedCorners">false</item>
    </style>

    <style name="ShapeAppearance.Flashat.HuddlePostBottomSheet" parent="ShapeAppearance.MaterialComponents.LargeComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">16dp</item>
    </style>

    <style name="Widget.Flashat.HuddleNewPostFAB" parent="Widget.MaterialComponents.ExtendedFloatingActionButton.Icon">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.HuddleNewPostFAB</item>
        <item name="icon">@drawable/ic_arrow_up</item>
        <item name="iconSize">20dp</item>
        <item name="collapsedSize">40dp</item>
        <item name="iconPadding">@dimen/element_spacing</item>
        <item name="android:paddingVertical">0dp</item>
        <item name="android:paddingHorizontal">0dp</item>
        <item name="android:minWidth">40dp</item>
        <item name="android:minHeight">40dp</item>
    </style>

    <style name="ThemeOverlay.Flashat.HuddleNewPostFAB" parent="">
        <item name="colorSecondary">@color/colorSecondary</item>
        <item name="colorOnSecondary">@color/textColorOnSecondary</item>
        <item name="colorOnSurface">@color/textColorOnPrimary</item>
    </style>

    <!--  Video Player  -->
    <style name="Widget.Flashat.VideoPlayer" parent="">
        <item name="bar_height">6dp</item>
        <item name="scrubber_enabled_size">16dp</item>
        <item name="scrubber_dragged_size">20dp</item>
        <item name="scrubber_color">@color/colorPrimary</item>
        <item name="played_color">@color/colorPrimaryLight</item>
        <item name="buffered_color">@color/colorPrimaryLighter</item>
        <item name="unplayed_color">@color/colorSurfaceSecondary</item>
        <item name="show_buffering">when_playing</item>
        <item name="show_previous_button">false</item>
        <item name="show_next_button">false</item>
        <item name="show_rewind_button">false</item>
        <item name="show_fastforward_button">false</item>
        <item name="auto_show">false</item>
    </style>

    <style name="Widget.Flashat.VideoPlayer.Attach">
        <item name="auto_show">true</item>
    </style>

    <style name="Widget.Flashat.Chat.AudioSpeedButton" parent="">
        <item name="android:textAllCaps">false</item>
        <item name="android:textAlignment">center</item>
        <item name="android:clickable">true</item>
        <item name="android:foreground">?attr/selectableItemBackgroundBorderless</item>
        <item name="android:background">@drawable/bg_audio_speed_button</item>
        <item name="android:paddingBottom">4dp</item>
        <item name="android:paddingTop">2dp</item>
        <item name="android:paddingHorizontal">8dp</item>
        <item name="android:minWidth">38dp</item>
    </style>

    <style name="Widget.Flashat.StickerBottomSheet" parent="ThemeOverlay.MaterialComponents.BottomSheetDialog">
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style>

    <style name="Widget.Flashat.PollEditButton" parent="Widget.Flashat.Button.IconOnly.Small">
        <item name="android:minWidth">32dp</item>
        <item name="android:minHeight">32dp</item>
        <item name="android:padding">6dp</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.PollEditButton</item>
    </style>

    <style name="ThemeOverlay.Flashat.PollEditButton" parent="">
        <item name="colorPrimary">@color/colorSurfaceSecondaryDark</item>
        <item name="colorOnPrimary">@color/textColorSecondaryLight</item>
        <item name="colorOnSurface">@color/colorSurfaceSecondaryDark</item>
    </style>

    <style name="ThemeOverlay.Flashat.HuddlePostInput" parent="">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox</item>
    </style>

    <style name="ShapeAppearance.Flashat.HuddlePostInput" parent="ShapeAppearance.MaterialComponents.LargeComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">16dp</item>
    </style>
</resources>