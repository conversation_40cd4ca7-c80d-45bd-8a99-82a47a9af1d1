package com.app.messej.ui.home.publictab.maidan

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.enums.GiftConversion
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.databinding.FragmentPodiumMaidanChallengeBottomSheetBinding
import com.app.messej.ui.home.publictab.maidan.PodiumMaidanChallengeViewModel.ChallengeEligibilityStatus
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.checkIfJoinHidden
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import kotlin.math.roundToInt

class PodiumMaidanChallengeBottomSheetFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentPodiumMaidanChallengeBottomSheetBinding
    private val viewModel: PodiumMaidanChallengeViewModel by viewModels()

    val args: PodiumMaidanChallengeBottomSheetFragmentArgs by navArgs()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_PodiumBottomSheet)

    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_maidan_challenge_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = true
        viewModel.setParams(args.podiumId)
        setup()
        observe()
    }

    fun setup() {
        (dialog as? BottomSheetDialog)?.behavior?.apply {
            state = BottomSheetBehavior.STATE_COLLAPSED
        }
        binding.actionChallenge.setOnClickListener {
            when(viewModel.challengeEligibilityStatus.value) {
                is ChallengeEligibilityStatus.Eligible -> viewModel.joinMaidanChallenge()
                is ChallengeEligibilityStatus.NotEnoughCoins -> {
                    val action = NavGraphHomeDirections.actionGlobalConvertCoinToFlaxFragment(GiftConversion.FLAX_TO_COIN)
                    findNavController().navigateSafe(action)
                }
                is ChallengeEligibilityStatus.NotEnoughCoinsNorFlix -> {
                    val action = NavGraphHomeDirections.actionGlobalBuyflaxFragment(isBuyCoin = false, hideActionBar = true)
                    findNavController().navigateSafe(action)
                }
                else -> {}
            }
        }
        binding.actionWatch.setOnClickListener {
            if (args.fromPodium) findNavController().navigateUp()
            else {
                checkIfJoinHidden(viewModel.user,args.hiddenSetting) { hidden ->
                    navigateToPodium(args.podiumId,hidden)
                }
            }
        }
    }

    fun observe() {
        viewModel.onMaidanJoined.observe(viewLifecycleOwner){
            viewModel.podium.value?.let { pod ->
                if ((pod.challenge?.competitorFee ?: 0.0) != 0.0) {
                    showToast(getString(R.string.podium_maidan_fee_debit_toast, pod.challenge?.competitorFee?.roundToInt().toString().orEmpty()))
                }
            }
            navigateToPodium(it.id)
        }
        viewModel.onMaidanJoinError.observe(viewLifecycleOwner) {
            showToast(it)
            findNavController().popBackStack()
        }
        viewModel.onMaidanAnotherUserJoinedError.observe(viewLifecycleOwner) {
            showMaidanAnotherUserJoinAlert(it)
        }
        viewModel.onPodiumLoadError.observe(viewLifecycleOwner) {
            showToast(it)
            findNavController().popBackStack()
        }
        viewModel.onLiveInAnotherPodium.observe(viewLifecycleOwner) {
            val builder = MaterialAlertDialogBuilder(requireContext()).apply {
                setCancelable(false)
                setMessage(it.message)
                if (it.canLeave) {
                    setPositiveButton(resources.getString(R.string.common_proceed)) { dialog, which ->
                        dialog.dismiss()
                        viewModel.leaveOtherPodium(it.otherPodiumId)
                    }
                    setNegativeButton(resources.getString(R.string.common_cancel)) { dialog, which ->
                        dialog.dismiss()
                        val action = NavGraphHomeDirections.actionGlobalNavLivePodium(it.otherPodiumId)
                        val options = NavOptions.Builder().setPopUpTo(R.id.nav_live_podium, inclusive = true).build()
                        findNavController().navigateSafe(action, options)
                    }
                } else {
                    setPositiveButton(resources.getString(R.string.podium_join_back)) { dialog, which ->
                        dialog.dismiss()
                        val action = NavGraphHomeDirections.actionGlobalNavLivePodium(it.otherPodiumId)
                        val options = NavOptions.Builder().setPopUpTo(R.id.nav_live_podium, inclusive = true).build()
                        findNavController().navigateSafe(action, options)
                    }
                }
            }
            builder.show()
        }

//        @string/(viewModel.userNameOrNickName,viewModel.podium.challenge.competitorFee,viewModel.podium.challenge.prize)

        viewModel.onLeftOtherPodium.observe(viewLifecycleOwner) {
            viewModel.joinMaidanChallenge()
        }

        viewModel.challengeEligibilityStatus.observe(viewLifecycleOwner) { status ->
            binding.actionChallenge.isEnabled = true
            when(status) {
                is ChallengeEligibilityStatus.Eligible -> {
                    if (args.fromPodium) {
                        binding.sheetTitle.text = if (status.challengeFee == 0.0) getString(R.string.podium_challenge_free_maidan_challenge_podium, status.name)
                        else getString(R.string.podium_challenge_participation_text, status.challengeFee.roundToInt().toString())
                        binding.actionWatch.apply {
                            setIconResource(R.drawable.ic_close)
                            setIconTintResource(R.color.colorError)
                        }
                    } else {
                        binding.sheetTitle.text = if (status.challengeFee == 0.0) getString(R.string.podium_challenge_free_maidan_compete_prompt_text, status.name)
                        else getString(R.string.podium_challenge_maidan_prompt_message,status.name,status.challengeFee.roundToInt().toString(),status.prize.toString())
                        binding.actionChallenge.setIconResource(R.drawable.ic_podium_maidan_fist_filled)
                    }
                }
                is ChallengeEligibilityStatus.NotEnoughCoins -> {
                    binding.sheetTitle.text = getString(R.string.podium_challenge_maidan_prompt_message_no_coins,status.name,status.challengeFee.roundToInt().toString(),status.coins.toString(),status.flix.toString())
                    binding.actionChallenge.setIconResource(R.drawable.ic_flax_to_coin)
                }
                is ChallengeEligibilityStatus.NotEnoughCoinsNorFlix -> {
                    binding.sheetTitle.text = getString(R.string.podium_challenge_maidan_prompt_message_no_coins,status.name,status.challengeFee.roundToInt().toString(),status.coins.toString(),status.flix.toString())
                    binding.actionChallenge.setIconResource(R.drawable.ic_coin)
                }
                ChallengeEligibilityStatus.Error -> {
                    binding.sheetTitle.text = getString(R.string.default_eds_error_message)
                    binding.actionChallenge.isEnabled = false
                }
                null -> {
                    binding.actionChallenge.isEnabled = false
                }
            }
        }
    }

    private fun showMaidanAnotherUserJoinAlert(message: String?) {
        showFlashatDialog {
            setTitle(getString(R.string.podium_maidan_bad_luck_title))
            setMessage(message ?: "")
            setIcon(R.drawable.ic_sad)
            setConfirmButtonVisible(true)
            setCloseButton(title = R.string.common_close, icon = R.drawable.ic_close) { findNavController().popBackStack() }
            setConfirmButton(R.string.podium_maidan_bad_luck_find_another_maidan, R.drawable.ic_podium_maidan_fist_filled, tint = false, iconPadding = false) {
                findNavController().popBackStack()
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicMaidanFragment())
                true
            }
        }
    }

    private fun navigateToPodium(id: String, hidden: Boolean = false) {
        val action = NavGraphHomeDirections.actionGlobalNavLivePodium(id, kind = PodiumKind.MAIDAN.ordinal, joinHidden = hidden)
        val options = NavOptions.Builder().apply {
            if (args.fromPodium) {
                setPopUpTo(R.id.nav_live_podium, inclusive = true)
            } else {
                setPopUpTo(R.id.podiumMaidanChallengeBottomSheetFragment, inclusive = true)
            }
        }.build()
        findNavController().navigateSafe(action, options)
    }
}