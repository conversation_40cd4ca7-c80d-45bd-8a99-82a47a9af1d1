package com.app.messej.ui.home.publictab.podiums.live

import android.content.res.Configuration
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.CutCornerShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.data.model.api.podium.challenges.MaidanPlayerSummary
import com.app.messej.data.model.api.podium.challenges.PlayerStats
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.app.messej.ui.composeComponents.FlashatComposeTypography
import com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel

@Composable
fun MaidanPlayerSummaryComposable(
    speakers: List<PodiumSpeakerUIModel.ActiveSpeakerUIModel>?,
    maidanSummary: MaidanPlayerSummary?
) {
    val user =
        speakers?.firstOrNull {
            it.speaker.id ==
                    maidanSummary?.playerStats?.userId
        }?.speaker
    val competitor = maidanSummary?.competitorStats?.userId?.let { cid ->
        speakers?.firstOrNull { it.speaker.id == cid }?.speaker
    }
    if (user == null) return
    if (maidanSummary != null) {

        Box(
            modifier = Modifier
                .fillMaxSize()
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = 16.dp)
            ) {
                Text(
                    text = stringResource(id = R.string.podium_maidan_player_summary),
                    style = FlashatComposeTypography.defaultType.body1,
                    modifier = Modifier
                        .align(alignment = Alignment.CenterHorizontally)
                        .padding(16.dp),
                    color = colorResource(R.color.textColorSecondary)
                )
                ProfileCard(
                    profilePicUrl = user.thumbnail.orEmpty(),
                    rating = user.userRatingPercent,
                    userName = user.name
                )
                ProfileDivider()
                MaidanSubHeading(
                    subHeading = stringResource(R.string.podium_maidan_performance_stats),
                    modifier = Modifier.padding(horizontal = 16.dp)
                )
                PerformanceStats(
                    playerStats = maidanSummary.playerStats,
                    modifier = Modifier
                        .align(alignment = Alignment.Start)
                        .padding(16.dp)
                )
                competitor?.let {
                    maidanSummary.competitorStats?.let { stats ->
                        PlayerVsCompetitorStats(
                            user = user,
                            competitor = it,
                            stats = stats,
                        )
                    }
                }

            }
        }
    }
}

@Composable
fun Loader() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        LinearProgressIndicator()
    }
}

//@Preview(uiMode = Configuration.UI_MODE_NIGHT_NO, name = "Light Mode")
@Composable
fun ProfileCardPreviewLight() {
    ProfileCardPreview()
}

//@Preview(uiMode = Configuration.UI_MODE_NIGHT_YES, name = "Dark Mode")
@Composable
fun ProfileCardPreviewDark() {
    ProfileCardPreview()
}

@Composable
fun ProfileCardPreview() {
    ProfileCard(
        profilePicUrl = "",
        rating = "",
        userName = "Shahim"
    )
}

@Composable
fun ProfileCard(profilePicUrl: String, rating: String, userName: String) {
    Card(
        shape = RoundedCornerShape(16.dp),
        modifier = Modifier
            .padding(16.dp)
            .fillMaxWidth()
    ) {

        Box {
            AsyncImage(
                model = profilePicUrl,
                placeholder = painterResource(R.drawable.im_user_placeholder),
                error = painterResource(R.drawable.im_user_placeholder),
                contentDescription = null,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(400.dp)
                    .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)),
                contentScale = ContentScale.Fit
            )

            ProfilePictureDetails(
                modifier = Modifier
                    .wrapContentWidth()
                    .background(
                        color = Color(0xFFF5F5F5),
                        shape = CutCornerShape(bottomStart = 20.dp)
                    )
                    .padding(12.dp)
                    .align(Alignment.TopEnd),
                bodyItalicsText = stringResource(R.string.restore_rating_your_rating_title),
                headingItalicsText =
                    stringResource(
                        R.string.podium_maidan_rating_percentage,
                        rating
                    ),
                bodyItalicsBoldText = ""
            )

            ProfilePictureDetails(
                modifier = Modifier
                    .wrapContentWidth()
                    .background(
                        color = Color(0xFFF5F5F5),
                        shape = CutCornerShape(topEnd = 20.dp) // gives diagonal look
                    )
                    .padding(12.dp)
                    .align(Alignment.BottomStart),
                bodyItalicsText = "",
                headingItalicsText = "",
                bodyItalicsBoldText = userName
            )
        }
    }
}

@Composable
fun ProfilePictureDetails(
    modifier: Modifier = Modifier,
    bodyItalicsText: String,
    headingItalicsText: String,
    bodyItalicsBoldText: String,
) {
    Box(
        modifier = modifier
    ) {
        Row(
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = bodyItalicsText,
                style = FlashatComposeTypography.bodyItalics,
            )
            Text(
                text = headingItalicsText,
                style = FlashatComposeTypography.h4Italics,
            )
        }
        Text(
            text = bodyItalicsBoldText,
            style = FlashatComposeTypography.h5Italics,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun ProfileDivider() {
    HorizontalDivider(
        color = colorResource(id = R.color.colorDivider),
        thickness = 1.dp,
        modifier = Modifier
            .padding(
                horizontal = dimensionResource(id = R.dimen.activity_margin),
                vertical = dimensionResource(id = R.dimen.activity_margin)
            )
    )
}

@Composable
fun MaidanSubHeading(subHeading: String, modifier: Modifier) {
    Text(
        text = subHeading,
        style = FlashatComposeTypography.defaultType.h6,
        modifier = modifier,
        color = colorResource(R.color.textColorSecondary)
    )
}

@Preview(uiMode = Configuration.UI_MODE_NIGHT_NO, name = "Light Mode")
@Composable
fun PerformanceStatsPreviewLight() {
    PerformanceStatsPreview()
}

@Preview(uiMode = Configuration.UI_MODE_NIGHT_YES, name = "Dark Mode")
@Composable
fun PerformanceStatsPreviewDark() {
    PerformanceStatsPreview()
}

@Composable
fun PerformanceStatsPreview() {
    Column(
        modifier = Modifier
            .padding(16.dp)
            .background(color = colorResource(R.color.colorSurface))
    ) {
        PerformanceStats(
            PlayerStats(
                gained = 1.0,
                lost = 2,
                played = 3,
                winRate = 4.0,
                won = 5
            ),
            modifier = Modifier
                .align(alignment = Alignment.Start)
                .padding(16.dp)
        )
    }
}

@Composable
fun PerformanceStats(playerStats: PlayerStats, modifier: Modifier) {
    Column {
        Row(
            modifier = modifier,
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            PerformanceStatsTile(
                iconPainter = painterResource(R.drawable.ic_maidan_played),
                count = playerStats.played.toString(),
                statType = stringResource(R.string.podium_maidan_played),
                modifier = Modifier.weight(1f)
            )
            PerformanceStatsTile(
                iconPainter = painterResource(R.drawable.ic_podium_maidan_trophy),
                count = playerStats.won.toString(),
                statType = stringResource(R.string.podium_maidan_won),
                modifier = Modifier.weight(1f)
            )
            PerformanceStatsTile(
                iconPainter = painterResource(R.drawable.ic_maidan_lost),
                count = playerStats.lost.toString(),
                statType = stringResource(R.string.podium_maidan_lost),
                modifier = Modifier.weight(1f)
            )
        }
        Row(
            modifier = modifier,
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            GainWinRateTile(
                playerStats = playerStats,
                modifier = Modifier.weight(1f),
                gained = true
            )
            GainWinRateTile(
                playerStats = playerStats,
                modifier = Modifier.weight(1f),
                gained = false
            )
        }
    }
}

@Preview(uiMode = Configuration.UI_MODE_NIGHT_NO, name = "Light Mode")
@Composable
fun PlayerVsCompetitorStatsPreviewLight() {
    PlayerVsCompetitorStatsPreview()
}

@Preview(uiMode = Configuration.UI_MODE_NIGHT_YES, name = "Dark Mode")
@Composable
fun PlayerVsCompetitorStatsPreviewDark() {
    PlayerVsCompetitorStatsPreview()
}

@Composable
fun PlayerVsCompetitorStatsPreview() {
    Column(
        modifier = Modifier
            .padding(16.dp)
            .background(color = colorResource(R.color.colorSurface))
    ) {
        PlayerVsCompetitorStats(
            user = PodiumSpeaker(
                id = 1,
                name = "Shahim",
                username = "shahim",
                thumbnail = "",
                membership = UserType.PREMIUM,
                citizenship = UserCitizenship.CITIZEN,
                verified = false
            ),
            competitor = PodiumSpeaker(
                id = 1,
                name = "Ajay",
                username = "ajay93",
                thumbnail = "",
                membership = UserType.PREMIUM,
                citizenship = UserCitizenship.CITIZEN,
                verified = false
            ),
            stats = PlayerStats(
                gained = 1.0,
                lost = 2,
                played = 3,
                winRate = 4.0,
                won = 5
            ),
            modifier = Modifier
                .padding(16.dp),
        )
    }
}

@Composable
fun PlayerVsCompetitorStats(
    user: PodiumSpeaker,
    competitor: PodiumSpeaker,
    stats: PlayerStats,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = Modifier
    ) {
        MaidanSubHeading(
            subHeading = stringResource(R.string.podium_maidan_player_competitor_stats),
            modifier = modifier.padding(horizontal = 16.dp)
        )
        Card(
            shape = RoundedCornerShape(8.dp),
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .fillMaxWidth()
                .wrapContentHeight()
        ) {
            var upward by remember { mutableStateOf(true) }
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .background(color = colorResource(R.color.colorSurfaceSecondary))
            ) {
                Text(
                    text =
                        stringResource(
                            R.string.podium_challenge_winner_ticker_coins,
                            stats.gained.toString()
                        ),
                    style = FlashatComposeTypography.defaultType.body2,
                    color = colorResource(R.color.colorSocialGreen),
                    modifier = modifier
                        .align(Alignment.CenterHorizontally)
                        .padding(16.dp)
                )
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                        .padding(horizontal = 16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                ) {
                    PlayerDetails(user, modifier = Modifier.weight(1f))
                    Column(
                        modifier = Modifier
                            .wrapContentWidth()
                            .align(Alignment.CenterVertically),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Text(
                            text = stringResource(R.string.podium_maidan_summary_vs),
                            style = FlashatComposeTypography.h3Italics,
                            color = colorResource(R.color.textColorSecondary)
                        )
                        IconOnSemicircle(upward = upward, onClick = {
                            upward = !upward
                        })
                    }
                    PlayerDetails(competitor, modifier = Modifier.weight(1f))
                }
                if (upward) {
                    PerformanceStats(
                        playerStats = stats,
                        modifier = Modifier
                            .align(alignment = Alignment.Start)
                            .padding(16.dp)
                    )
                }
            }
        }
    }
}

@Composable
fun PlayerDetails(user: PodiumSpeaker, modifier: Modifier) {
    Column(modifier = modifier) {
        AsyncImage(
            model = user.thumbnail,
            placeholder = painterResource(R.drawable.im_user_placeholder),
            error = painterResource(R.drawable.im_user_placeholder),
            contentDescription = null,
            modifier = Modifier
                .size(65.dp)
                .clip(CircleShape)
                .align(Alignment.CenterHorizontally),
            contentScale = ContentScale.Fit
        )
        Text(
            text = user.name,
            style = FlashatComposeTypography.defaultType.body1,
            color = colorResource(R.color.textColorSecondary),
            modifier = Modifier
                .padding(top = 8.dp, start = 8.dp, end = 8.dp)
                .align(Alignment.CenterHorizontally)
        )
    }
}

@Composable
fun PerformanceStatsTile(
    iconPainter: Painter,
    count: String,
    statType: String,
    modifier: Modifier = Modifier
) {
    val linear =
        Brush.verticalGradient(
            listOf(
                colorResource(R.color.colorSurfaceSecondaryDark),
                colorResource(R.color.colorSurfaceSecondaryDarker)
            )
        )
    Card(
        shape = RoundedCornerShape(8.dp),
        modifier = modifier
    ) {
        Column(
            modifier = Modifier
                .background(linear)
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(horizontal = 16.dp, vertical = 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                painter = iconPainter,
                contentDescription = "",
                tint = Color.Unspecified,
                modifier = Modifier
                    .size(50.dp)
            )
            Column(
                modifier = Modifier
                    .align(Alignment.CenterHorizontally)
                    .padding(top = 16.dp)
            ) {
                Text(
                    text = count,
                    style = FlashatComposeTypography.h4Italics,
                    color = colorResource(R.color.textColorSecondary),
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                )
                Text(
                    text = statType,
                    style = FlashatComposeTypography.bodyItalics,
                    color = colorResource(R.color.textColorSecondary),
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                )
            }
        }
    }
}

fun Color.darken(factor: Float = 0.8f): Color {
    // Ensure the factor is between 0 and 1
    val clampedFactor = factor.coerceIn(0f, 1f)
    return this.copy(
        red = this.red * clampedFactor,
        green = this.green * clampedFactor,
        blue = this.blue * clampedFactor
    )
}


@Composable
fun GainWinRateTile(playerStats: PlayerStats, modifier: Modifier, gained: Boolean) {
    val linear =
        if (gained) {
            Brush.verticalGradient(
                listOf(
                    colorResource(R.color.colorMaidanGainChip),
                    colorResource(R.color.colorMaidanGainChip).darken(0.4f)
                )
            )
        } else {
            Brush.verticalGradient(
                listOf(
                    colorResource(R.color.colorPrimary),
                    colorResource(R.color.colorPrimary).darken(0.4f)
                )
            )
        }
    Card(
        shape = RoundedCornerShape(8.dp),
        modifier = modifier
    ) {
        Row(
            modifier = Modifier
                .background(linear)
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Column {
                Text(
                    text = if (gained)
                        playerStats.gained.toString()
                    else
                        stringResource(
                            R.string.podium_maidan_rating_percentage,
                            playerStats.winRate.toString()
                        ),
                    style = FlashatComposeTypography.h5Italics, color = Color.White
                )
                Text(
                    text = if (gained)
                        stringResource(R.string.podium_maidan_gained)
                    else
                        stringResource(R.string.podium_maidan_win_rate),
                    style = FlashatComposeTypography.body2Italics,
                    color = Color.White
                )
            }
            Icon(
                painter = if (gained)
                    painterResource(R.drawable.ic_coin)
                else
                    painterResource(R.drawable.ic_maidan_win_rate),
                contentDescription = "",
                tint = Color.Unspecified,
                modifier = Modifier
                    .size(50.dp)
            )
        }
    }
}

enum class SemicircleSide { Top, Bottom, Left, Right }

@Composable
fun FilledSemicircle(
    modifier: Modifier = Modifier,
    color: Color,
    side: SemicircleSide = SemicircleSide.Top
) {
    Canvas(modifier = modifier) {
        val (start, sweep) = when (side) {
            SemicircleSide.Top -> 180f to 180f
            SemicircleSide.Bottom -> 0f to 180f
            SemicircleSide.Left -> 270f to 180f
            SemicircleSide.Right -> 90f to 180f
        }
        drawArc(
            color = color,
            startAngle = start,
            sweepAngle = sweep,
            useCenter = true,
            topLeft = Offset.Zero,
            size = size
        )
    }
}

@Composable
fun IconOnSemicircle(upward: Boolean, onClick: () -> Unit = {}) {
    Box(
        modifier = Modifier.size(30.dp),
        contentAlignment = Alignment.TopCenter
    ) {
        FilledSemicircle(
            modifier = Modifier
                .fillMaxSize()
                .clickable { onClick() },
            color = colorResource(R.color.colorPrimary),
            side = SemicircleSide.Top,
        )
        Icon(
            painter = if (upward)
                painterResource(R.drawable.ic_caret_up_rounded)
            else
                painterResource(R.drawable.ic_caret_down_rounded),
            contentDescription = null,
            tint = Color.White,
            modifier = Modifier
                .matchParentSize()
                .padding(bottom = 10.dp)
        )
    }
}
