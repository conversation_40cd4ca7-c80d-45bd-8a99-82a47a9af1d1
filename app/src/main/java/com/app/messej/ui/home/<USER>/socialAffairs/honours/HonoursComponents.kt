package com.app.messej.ui.home.publictab.socialAffairs.honours

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.app.messej.R
import com.app.messej.data.model.api.socialAffairs.HonoursResponse
import com.app.messej.data.model.api.socialAffairs.HonoursResponse.Companion.testSingleHonourDetail
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.ui.composeComponents.FlashatComposeTypography
import com.app.messej.ui.home.publictab.myETribe.ETribeUtil.setupETribeCitizenshipTextColor
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomVerticalSpacer
import com.app.messej.ui.home.publictab.socialAffairs.components.UserImageBadgeAndCountryCodeView
import com.app.messej.ui.utils.DataFormatHelper.numberToK
import com.app.messej.ui.utils.EnumUtils.displayText
import kotlin.math.roundToInt

@Composable
fun HonourSingleItem(
    item : HonoursResponse.Honour,
    onUserDPClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .clip(shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing)))
            .background(color = colorResource(id = R.color.colorSocialSurfaceSecondary))
            .fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.fillMaxWidth()
        ) {
            //Case Supported View
            HonourItemTitleView(
                modifier = Modifier.weight(weight = 1F),
                title = R.string.social_case_supported,
                colors = listOf(colorResource(id = R.color.colorHonoursGradient), colorResource(id = R.color.colorSocialSurfaceSecondary)),
                count = "${item.voteCount ?: 0}"
            )
            //Total Donated View
            HonourItemTitleView(
                modifier = Modifier.weight(weight = 1F),
                title = R.string.social_total_donated,
                colors = listOf(colorResource(id = R.color.colorDonatedGradient), colorResource(id = R.color.colorSocialSurfaceSecondary)),
                count = numberToK(number = item.totalDonated ?: 0.0)
            )
        }
        Row(
            modifier = Modifier
                .padding(all = 12.dp)
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            //User View
            UserImageBadgeAndCountryCodeView(
                userImage = item.userDetail?.thumbnail,
                userImageSize = 48.dp,
                isBorderVisible = false,
                userType = item.userDetail?.userBadge,
                onUserDpClick = onUserDPClick
            )
            Column(
                modifier = Modifier
                    .padding(start = dimensionResource(id = R.dimen.element_spacing))
                    .fillMaxWidth()
                    .weight(weight = 1F)
            ) {
                Row(
                    modifier = Modifier
                        .padding(end = dimensionResource(id = R.dimen.element_spacing))
                        .fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    //User Name
                    Text(
                        text = item.userDetail?.name ?: "",
                        modifier = Modifier.weight(weight = 1F, fill = false),
                        style = FlashatComposeTypography.defaultType.overline,
                        color = colorResource(id = R.color.textColorPrimary),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                    //Country Flag
                    item.userDetail?.countryFlag?.let {
                        AsyncImage(
                            model = ImageRequest.Builder(LocalContext.current)
                                .crossfade(enable = true)
                                .data(data = it)
                                .build(),
                            contentDescription = null,
                            contentScale = ContentScale.Crop,
                            modifier = Modifier
                                .padding(start = dimensionResource(id = R.dimen.element_spacing))
                                .size(width = 20.dp, height = 13.dp)
                        )
                    }
                }
                CustomVerticalSpacer(
                    space = dimensionResource(id = R.dimen.element_spacing)
                )
                //Citizenship
                item.userDetail?.citizenship?.let { citizenship ->
                    Text(
                        text = stringResource(id = citizenship.displayText()),
                        color = colorResource(id = citizenship.setupETribeCitizenshipTextColor()),
                        style = FlashatComposeTypography.overLineSmallerBold,
                        modifier = Modifier
                            .padding(top = dimensionResource(id = R.dimen.line_spacing))
                            .setupCitizenshipLevelBackground(citizenShip = citizenship)
                            .padding(
                                vertical = dimensionResource(id = R.dimen.line_spacing),
                                horizontal = 12.dp
                            )
                    )
                }
            }
            Column {
                //Rating View
                HonoursGNRRatingView(
                    isGnr = false,
                    value = stringResource(id = R.string.common_percentage_value, "${(item.userDetail?.userRating ?: 0.0).roundToInt()}")
                )
                CustomVerticalSpacer(
                    space = dimensionResource(id = R.dimen.element_spacing)
                )
                //GNR View
                HonoursGNRRatingView(
                    isGnr = true,
                    value = item.userDetail?.generosity
                )
            }
        }
    }
}

@Preview
@Composable
private fun HonourSingleItemPreview() {
    HonourSingleItem(
        item = testSingleHonourDetail,
        onUserDPClick = {}
    )
}

@Composable
private fun HonourItemTitleView(
    modifier: Modifier = Modifier,
    @StringRes title: Int,
    count : String?,
    colors: List<Color>
) {
    Box(
        modifier = modifier
            .background(brush = Brush.verticalGradient(colors = colors))
            .padding(vertical = dimensionResource(id = R.dimen.element_spacing))
            .fillMaxWidth(),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = buildAnnotatedString {
                append(text = stringResource(id = title))
                withStyle(style = SpanStyle(fontSize = 12.sp, fontWeight = FontWeight.Bold)) { append(" ${count ?: ""}") }
            },
            style = FlashatComposeTypography.overLineSmaller.copy(fontSize = 11.sp),
            color = colorResource(id = R.color.textColorPrimary)
        )
    }
}

@Preview
@Composable
private fun HonourItemTitlePreview() {
    HonourItemTitleView(
        title = R.string.social_total_donated,
        colors = listOf(colorResource(id = R.color.colorDonatedGradient), colorResource(id = R.color.colorSurfaceSecondary)),
        count = numberToK(number = 100.0)
    )
}

@Composable
private fun HonoursGNRRatingView(
    isGnr:Boolean,
    value: String?
) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        Icon(
            painter = painterResource(id = if (isGnr) R.drawable.ic_idcard_lv else R.drawable.ic_idcard_rating),
            tint = colorResource(id = R.color.colorSocialVoteUpExpired),
            modifier = Modifier.size(size = 20.dp),
            contentDescription = null
        )
        androidx.compose.material.Text(
            text = buildAnnotatedString {
                withStyle(style = SpanStyle(fontWeight = FontWeight.Bold)) { append("${value ?: "0"}\n") }
                withStyle(style = SpanStyle(fontSize = 6.sp)) { append(stringResource(id = if (isGnr) R.string.id_card_lv else R.string.id_card_rating)) }
            },
            modifier = Modifier.padding(start = dimensionResource(id = R.dimen.line_spacing)),
            style = FlashatComposeTypography.overLineSmaller.copy(lineHeight = 8.sp),
            color = colorResource(id = R.color.colorSocialVoteUpExpired)
        )
    }
}

@Preview
@Composable
private fun HonoursGNRRatingPreview() {
    HonoursGNRRatingView(isGnr = false, value = "41")
}

@Composable
private fun Modifier.setupCitizenshipLevelBackground(citizenShip: UserCitizenship): Modifier {
    val backgroundColor = when (citizenShip) {
        UserCitizenship.VISITOR, UserCitizenship.RESIDENT -> List(size = 2) { (colorResource(R.color.textColorAlwaysDarkSecondaryLight)) }
        UserCitizenship.CITIZEN -> List(size = 2) { colorResource(R.color.colorPodiumSpeakerCitizen) }
        UserCitizenship.OFFICER -> List(size = 2) { colorResource(R.color.colorPodiumSpeakerOfficer) }
        UserCitizenship.AMBASSADOR -> List(size = 2) { colorResource(R.color.colorPodiumSpeakerAmbassador) }
        UserCitizenship.MINISTER -> List(size = 2) { colorResource(R.color.colorPrimary) }
        UserCitizenship.PRESIDENT -> listOf(Color(color = 0xFFA37A1E), Color(color = 0xFFE6BE69), Color(color = 0xFF956E13))
        UserCitizenship.GOLDEN -> listOf(Color(0xFFFFD473),colorResource(R.color.colorFlashatGolden))
    }
    return background(
        brush = Brush.verticalGradient(colors = backgroundColor),
        shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing))
    ).border(
        width = 0.35.dp,
        shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing)),
        color = colorResource(id = R.color.white)
    )
}