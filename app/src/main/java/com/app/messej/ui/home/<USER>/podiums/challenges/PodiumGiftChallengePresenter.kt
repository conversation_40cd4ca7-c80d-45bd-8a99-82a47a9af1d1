package com.app.messej.ui.home.publictab.podiums.challenges

import android.content.res.ColorStateList
import android.util.Log
import android.view.View
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.entity.NickName.Companion.nickNameOrName
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.databinding.ItemPodiumSpeakerChallengeDecorGiftBinding
import com.app.messej.databinding.LayoutPodiumChallengeBoardGiftsBinding
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumLiveChallengeExtensions.transitionToText
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel
import com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel.ActiveSpeakerUIModel
import com.app.messej.ui.utils.DataFormatHelper.numberToKWithFractions
import com.app.messej.ui.utils.FragmentExtensions.showToast
import kotlinx.coroutines.launch

class PodiumGiftChallengePresenter(holder: ViewGroup, challenge: PodiumChallenge, challengeListener: ChallengeEventListener, protected val giftChallengeListener: GiftChallengeEventListener): PodiumChallengePresenter(holder, challenge, challengeListener) {

    private lateinit var mGiftChallengeSupporterListAdapter : PodiumGiftChallengeTopSupporterQuickAdapter
    override lateinit var liveBinding: LayoutPodiumChallengeBoardGiftsBinding

    interface GiftChallengeEventListener {
        fun onSendGift(speaker: PodiumSpeaker)
        fun onRequireUpgrade()
        fun onShowSupporters()
    }

    override val challengeTitle: Int
        get() = R.string.podium_challenge_gifts


    override fun setupView() {
        liveBinding = DataBindingUtil.inflate(layoutInflater, R.layout.layout_podium_challenge_board_gifts, holder, false)

        mGiftChallengeSupporterListAdapter =
            PodiumGiftChallengeTopSupporterQuickAdapter(mutableListOf())
                .apply {
                    animationEnable = false
                    isAnimationFirstOnly = false
                    setDiffCallback(PodiumGiftChallengeTopSupporterQuickAdapter.DiffCallback())
                }
    }

    override fun onNewScoresAvailable(forceRefresh: Boolean) {
        if (forceRefresh) refreshLiveUI()
        else refreshScores()
    }

    override fun decorateSpeakerTile(item: ActiveSpeakerUIModel, holder: ViewGroup, mainScreen: Boolean): Boolean {
        Log.w(
            "PCP",
            "decorateSpeakerTile: status: ${challenge.status}, started: ${challenge.hasStarted}, ended: ${challenge.gameOver}, running: ${challenge.running}"
        )
        if (!challenge.running) return false
        val sc = scores.find { it.id == item.speaker.id } ?: return false
        val overlay = DataBindingUtil.inflate<ItemPodiumSpeakerChallengeDecorGiftBinding>(layoutInflater, R.layout.item_podium_speaker_challenge_decor_gift, holder, false)
        overlay.giftChallengeCoins.text = sc.score.numberToKWithFractions()
        val totalScore = scores.sumOf { it.score }
        overlay.horizontalProgressbar.progress = (sc.score.toFloat() / totalScore * 100).toInt()
        try {
            val color = sc.color.toColorInt()
            overlay.challengeIcon.imageTintList = ColorStateList.valueOf(color)
            overlay.horizontalProgressbar.setIndicatorColor(color)
        } catch (_: Exception) {}
        holder.removeAllViews()
        holder.addView(overlay.root)
        return true
    }

    override fun onSpeakerClick(item: ActiveSpeakerUIModel, view: View, mainScreen: Boolean): Boolean {
        val currentUser = challengeListener.getLiveViewModel().user
        if (!challenge.running || item.speaker.id == currentUser.id) return false
        when(challengeListener.getLiveViewModel().canSendGifts()) {
            PodiumLiveViewModel.PodiumGiftSend.ELIGIBLE -> {
                when(challengeListener.getLiveViewModel().canReceiveGifts(item.speaker)) {
                    PodiumLiveViewModel.PodiumGiftSend.ELIGIBLE -> giftChallengeListener.onSendGift(item.speaker)
                    else -> {
                        challengeListener.getContext().showToast(R.string.podium_challenge_send_gift_inactive)
                    }
                }
            }
            PodiumLiveViewModel.PodiumGiftSend.UPGRADE -> giftChallengeListener.onRequireUpgrade()
            else -> {
                //TODO show some toast
            }
        }

        return true
    }

    override fun refreshLiveUI() {
        Log.w("PCP", "refreshLiveUI: $challenge")
        tickerJob?.cancel()
        Log.d("PCP", "tickerJob canceled in: refreshLiveUI")
        scope.launch {
            liveBinding.apply {
                timer.text = DateTimeUtils.formatSeconds(challenge.duration ?: 0)
                title.text = resources.getString(R.string.podium_challenge_gifts)

                content.isVisible = false
                supportersHolder.isVisible = false
            }

            Log.w(
                "PCP",
                "refreshLiveUI: status: ${challenge.status}, started: ${challenge.hasStarted}, ended: ${challenge.gameOver}, running: ${challenge.running}"
            )

            if (challenge.hasStarted) {
                suspend fun finalize() {
                    liveBinding.supportersHolder.isVisible = false
                    liveBinding.content.transitionToText(resources.getString(R.string.podium_challenge_finalizing))
                }
                if (!challenge.gameOver) {
                    liveBinding.supportersHolder.apply {
                        liveBinding.supportersListLayout.giftChallengeSupporters.apply {
                            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
                            setHasFixedSize(true)
                            adapter = mGiftChallengeSupporterListAdapter
                        }
                        liveBinding.supportersListLayout.rightArrowSupporters.setOnClickListener {
                            giftChallengeListener.onShowSupporters()
                        }
                        refreshScores()
                        challengeListener.onRequireSpeakerTileDecor()
                        isVisible = true
                    }

                    liveBinding.timer.showChallengeTimer {
                        finalize()
                        challengeListener.onRequireSpeakerTileDecor()
                    }
                } else {
                    finalize()
                }
            } else {
                liveBinding.content.isVisible = true
                tickerJob = liveBinding.content.showStartCountdown {
                    refreshLiveUI()
                }
                Log.d("PCP", "tickerJob set in RefreshLiveUI: PGCP")
            }
        }
    }

    private fun refreshScores() {
        Log.w("PCPG", "refreshScores" )
        challengeListener.onRequireSpeakerTileDecor()
        val topSupporters: List<PodiumChallenge.ChallengeUser>? =  challenge.topSupporters?.map {
                ps ->
            ps.copy(
                name = challengeListener.getLiveViewModel().nickNames.nickNameOrName(ps)
            )
        }?.toList()
        Log.w("PCPG", "top supporters: $topSupporters" )
        mGiftChallengeSupporterListAdapter.apply {
            topSupporters?.let { sl ->
                setNewInstance(sl.toMutableList())
            }
        }
    }

}