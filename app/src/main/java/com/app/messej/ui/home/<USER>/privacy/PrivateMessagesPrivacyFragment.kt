package com.app.messej.ui.home.settings.privacy

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.api.settings.StartPrivateChat
import com.app.messej.databinding.FragmentPrivateMessagesPrivacyBinding
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost

class PrivateMessagesPrivacyFragment : Fragment(), MenuProvider {

  private val viewModel: PrivateMessagePrivacyViewModel by viewModels()
    lateinit var binding: FragmentPrivateMessagesPrivacyBinding

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        (activity as AppCompatActivity).onBackPressedDispatcher.addCallback(viewLifecycleOwner, object: OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                Log.d("NAVC", "handleOnBackPressed:")
                viewModel.exitPrivacyStage()
            }
        })
        binding.customActionBar.toolbar.apply {
            setNavigationOnClickListener {
                viewModel.exitPrivacyStage()
            }
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_private_messages_privacy, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }
    private fun observe() {
        viewModel.isMessagePrivacyUpdateApiSuccess.observe(viewLifecycleOwner){
            if(it){
                Toast.makeText(requireContext(), getString(R.string.privacy_settings_selection_updated_title), Toast.LENGTH_SHORT).show()
                findNavController().popBackStack()
            }
        }
        
        viewModel.isPrivacyEnabled.observe(viewLifecycleOwner){
            requireActivity().invalidateOptionsMenu()
          managePrivacyToast(it)
        }


        viewModel.changeDetectionLiveData.observe(viewLifecycleOwner){
        }

        viewModel.onExitPrivacyStage.observe(viewLifecycleOwner){
            if (viewModel.changeDetectionLiveData.value == true){
                Toast.makeText(requireActivity(), getString(R.string.title_privacy_settings_changes_not_saved), Toast.LENGTH_SHORT).show()
            }
            findNavController().popBackStack()
        }
    }

    private fun managePrivacyToast(toggleButtonState: Boolean?) {
        if (viewModel.isMessagePrivacyApiSuccess.value == true && viewModel.isPrivacyMessage.value?.disablePrivateChat == false && toggleButtonState == false) {
            Toast.makeText(requireContext(), getString(R.string.private_messages_disabled), Toast.LENGTH_SHORT).show()
            viewModel.updateMessagePrivacy()

        }else if (viewModel.isMessagePrivacyApiSuccess.value == true && viewModel.isPrivacyMessage.value?.disablePrivateChat == true && toggleButtonState == true){
            Toast.makeText(requireContext(), getString(R.string.private_messages_enabled), Toast.LENGTH_SHORT).show()
        }

    }

    private fun setup() {
/* old privacy*/
        binding.itemContinueNone.root.setOnClickListener {
            viewModel.setOldPrivacyMessage(StartPrivateChat(noOne = true))
        }
        binding.itemAllContinueUsers.root.setOnClickListener {
            viewModel.setOldPrivacyMessage(StartPrivateChat(anyOne = true))
        }
        binding.itemOnlyContinueDears.root.setOnClickListener {
            viewModel.setOldPrivacyMessage(StartPrivateChat(onlyDears = true))
        }
        binding.itemOnlyContinueDearsAndFans.root.setOnClickListener {
            viewModel.setOldPrivacyMessage(StartPrivateChat(onlyDearsFans = true))
        }
        binding.itemContinuePremiumUsers.root.setOnClickListener {
            viewModel.setOldPrivacyMessage(StartPrivateChat(onlyPremium = true))
        }
        binding.itemContinueAllUsersWithHundred.root.setOnClickListener {
            viewModel.setOldPrivacyMessage(StartPrivateChat(onlyUsersWithHundred = true))
        }
        binding.itemContinuePremiumUsersWithHundred.root.setOnClickListener {
            viewModel.setOldPrivacyMessage(StartPrivateChat(onlyPremiumWithHundred = true))
        }


        /* New privacy*/
        binding.itemStartNone.root.setOnClickListener {
            viewModel.setNewPrivacyMessage(StartPrivateChat(noOne = true))
        }
        binding.itemStartAllUsers.root.setOnClickListener {
            viewModel.setNewPrivacyMessage(StartPrivateChat(anyOne = true))
        }
        binding.itemStartOnlyDears.root.setOnClickListener {
            viewModel.setNewPrivacyMessage(StartPrivateChat(onlyDears = true))
        }
        binding.itemStartOnlyDearsAndFans.root.setOnClickListener {
            viewModel.setNewPrivacyMessage(StartPrivateChat(onlyDearsFans = true))
        }
        binding.itemStartPremiumUsers.root.setOnClickListener {
            viewModel.setNewPrivacyMessage(StartPrivateChat(onlyPremium = true))
        }
        binding.itemStartAllUsersWithHundred.root.setOnClickListener {
            viewModel.setNewPrivacyMessage(StartPrivateChat(onlyUsersWithHundred = true))
        }
        binding.itemStartPremiumUsersWithHundred.root.setOnClickListener {
            viewModel.setNewPrivacyMessage(StartPrivateChat(onlyPremiumWithHundred = true))
        }

        binding.switchPrivateMessagePrivacy.setOnCheckedChangeListener { _, isChecked ->
            viewModel.handlePrivacySwitch(isChecked)
        }
    }
    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        menuInflater.inflate(R.menu.menu_privacy_private_message, menu)
        menu.findItem(R.id.item_private_message_update).isVisible = viewModel.isPrivacyEnabled.value==true

    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {

        when (menuItem.itemId) {
            android.R.id.home -> {
                findNavController().popBackStack()
            }

            R.id.item_private_message_update->{
                viewModel.updateMessagePrivacy()
            }
        }
        return true
    }


}
