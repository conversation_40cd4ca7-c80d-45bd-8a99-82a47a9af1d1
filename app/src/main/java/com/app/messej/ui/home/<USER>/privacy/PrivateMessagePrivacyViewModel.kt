package com.app.messej.ui.home.settings.privacy

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.settings.PrivacyMessage
import com.app.messej.data.model.api.settings.StartPrivateChat
import com.app.messej.data.repository.SettingsRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class PrivateMessagePrivacyViewModel (application: Application) : AndroidViewModel(application) {


    private var initialData: PrivacyMessage? = null

    private val _selectedOldPrivateMessage = MutableLiveData<StartPrivateChat?>(null)
    val selectedOldPrivateMessage: LiveData<StartPrivateChat?> = _selectedOldPrivateMessage

    fun setOldPrivacyMessage(item: StartPrivateChat?) {
        _selectedOldPrivateMessage.postValue(item)

    }

    private val _selectedNewPrivateMessage = MutableLiveData<StartPrivateChat?>(null)
    val selectedNewPrivateMessage: LiveData<StartPrivateChat?> = _selectedNewPrivateMessage

    fun setNewPrivacyMessage(item: StartPrivateChat?) {
        _selectedNewPrivateMessage.postValue(item)
    }


    private val _isMessagePrivacyApiLoading = MutableLiveData(true)
    val isMessagePrivacyApiLoading: LiveData<Boolean> = _isMessagePrivacyApiLoading
    private val settingsRepository = SettingsRepository(application)
    private val _isMessagePrivacyApiSuccess = MutableLiveData(false)

    val isMessagePrivacyApiSuccess: LiveData<Boolean> = _isMessagePrivacyApiSuccess

    private val _isMessagePrivacyApiFailed = MutableLiveData(false)
    val isMessagePrivacyApiFailed: LiveData<Boolean> = _isMessagePrivacyApiFailed

    private val _privacyApiFailedMessage = MutableLiveData("")
    val privacyApiFailedMessage: LiveData<String> = _privacyApiFailedMessage

    private val _isPrivacyEnabled = MutableLiveData(false)
    val isPrivacyEnabled: LiveData<Boolean> = _isPrivacyEnabled


    private val _isPrivacyMessage = MutableLiveData<PrivacyMessage>(null)
    val isPrivacyMessage: LiveData<PrivacyMessage> = _isPrivacyMessage

    private val _changeDetectionLiveData: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun check() {
            val isWhoCanStartChanged = initialData?.whoCanStartPrivateChat != _selectedNewPrivateMessage.value
            val isWhoCanContinueChanged = initialData?.whoCanContinuePrivateChat == _selectedOldPrivateMessage.value
            med.postValue(  isWhoCanStartChanged || isWhoCanContinueChanged)
        }
        med.addSource(_selectedOldPrivateMessage) {
        check()
        }
        med.addSource(_selectedNewPrivateMessage) {
            check()
        }

        med
    }


    val changeDetectionLiveData : LiveData<Boolean> = _changeDetectionLiveData



    private val _isMessagePrivacyUpdateApiSuccess = MutableLiveData(false)
    val isMessagePrivacyUpdateApiSuccess: LiveData<Boolean> = _isMessagePrivacyUpdateApiSuccess

    private val _isMessagePrivacyUpdateApiLoading = MutableLiveData(false)
    val isMessagePrivacyUpdateApiLoading: LiveData<Boolean> = _isMessagePrivacyUpdateApiLoading

    private val _isMessagePrivacyUpdateApiFailed = MutableLiveData(false)
    val isMessagePrivacyUpdateApiFailed: LiveData<Boolean> = _isMessagePrivacyUpdateApiFailed

    private val _isLoadingPrivacyComplete = MutableLiveData(false)
    val isLoadingPrivacyComplete: LiveData<Boolean> = _isLoadingPrivacyComplete


    init {
        getMessagePrivacy()
    }
    private fun getMessagePrivacy() {
        viewModelScope.launch(Dispatchers.IO) {
            _isMessagePrivacyApiLoading.postValue(true)

            when (val result: ResultOf<PrivacyMessage> = settingsRepository.getMessagePrivacyStatus()) {
                is ResultOf.Success -> {
                    _isMessagePrivacyApiSuccess.postValue(true)
                    _isPrivacyEnabled.postValue(!result.value.disablePrivateChat)
                    _isPrivacyMessage.postValue(result.value)
                    _isMessagePrivacyApiLoading.postValue(false)
                    Log.d("privatemessageprivacy", "getMessagePrivacy:$result.value ")
                    initialData = result.value
                    if (!result.value.disablePrivateChat) {
                        _selectedOldPrivateMessage.postValue(result.value.whoCanContinuePrivateChat)
                        _selectedNewPrivateMessage.postValue(result.value.whoCanStartPrivateChat)
                    }
                }
                is ResultOf.APIError -> {
                    _isMessagePrivacyApiFailed.postValue(true)
                    _isMessagePrivacyApiLoading.postValue(false)
                    _privacyApiFailedMessage.postValue(result.error.message)
                }
                is ResultOf.Error ->  {
                    _isMessagePrivacyApiFailed.postValue(true)
                    _isMessagePrivacyApiLoading.postValue(false)
                    _privacyApiFailedMessage.postValue(result.exception.message)
                }
            }
        }
    }


    fun updateMessagePrivacy() {
        viewModelScope.launch(Dispatchers.IO) {
            _isMessagePrivacyUpdateApiLoading.postValue(true)

            when (val result = settingsRepository.setMessagePrivacy(
                PrivacyMessage(!_isPrivacyEnabled.value!!, whoCanStartPrivateChat = _selectedNewPrivateMessage.value, whoCanContinuePrivateChat = _selectedOldPrivateMessage.value)

            )){
                is ResultOf.APIError -> {
                    // TODO show some feedback
                    _isMessagePrivacyUpdateApiLoading.postValue(false)
                    _isMessagePrivacyApiFailed.postValue(true)
                    _privacyApiFailedMessage.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    _isMessagePrivacyUpdateApiLoading.postValue(false)
                    _isMessagePrivacyApiFailed.postValue(true)
                    _privacyApiFailedMessage.postValue(result.exception.message)
                }
                is ResultOf.Success -> {
                    _isMessagePrivacyUpdateApiLoading.postValue(false)
                    _isMessagePrivacyUpdateApiSuccess.postValue(true)
                }
            }
        }
            }
    fun handlePrivacySwitch(checked: Boolean) {
        _isPrivacyEnabled.value =checked
        if(_isMessagePrivacyApiLoading.value == true){
            _isLoadingPrivacyComplete.postValue(true)
        }
    }

    fun exitPrivacyStage(){
        onExitPrivacyStage.postValue(true)
    }
    val onExitPrivacyStage = LiveEvent<Boolean>()
    }


