package com.app.messej.ui.home.publictab.postat

import android.graphics.Rect
import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.annotation.OptIn
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.appcompat.widget.PopupMenu
import androidx.core.view.MenuProvider
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.MediaSource
import androidx.navigation.fragment.findNavController
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.afollestad.materialdialogs.MaterialDialog
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.ReportPackage
import com.app.messej.data.model.entity.Postat
import com.app.messej.data.model.enums.ForwardSource
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.data.model.enums.ReportContentType
import com.app.messej.data.model.enums.ReportType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.databinding.FragmentPostatInnerListBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureInteractionAllowed
import com.app.messej.ui.enforcements.EnforcementExtensions.ensurePostatPostingAllowed
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureReportBanAllowed
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureReportContentAllowed
import com.app.messej.ui.home.publictab.HomePublicFragmentDirections
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.validateAndConfirmJoinFromGreenDot
import com.app.messej.ui.home.publictab.postat.create.CreatePostatFragmentDirections
import com.app.messej.ui.legal.report.ReportFragment.Companion.setReportListener
import com.app.messej.ui.legal.report.ReportUtils.canBan
import com.app.messej.ui.legal.report.ReportUtils.canReport
import com.app.messej.ui.legal.report.ReportUtils.canReportAndHide
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.setFragmentResultListenerOnActivity
import com.app.messej.ui.utils.FragmentExtensions.setupMenuItemTextColor
import com.app.messej.ui.utils.FragmentExtensions.showLoader
import com.app.messej.ui.utils.FragmentExtensions.showSnackbar
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.FragmentPlayerExtensions.getCookieMediaSource
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.kennyc.view.MultiStateView
import kotlin.math.roundToInt

abstract class PostatFeedBaseFragment : Fragment(), MenuProvider {

    protected lateinit var innerBinding: FragmentPostatInnerListBinding

    protected open val showIgnoreUserOption: Boolean = true

    protected abstract val viewModel: PostatFeedBaseViewModel

    protected var mAdapter: FeedPostatAdapter? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observer()
    }

    private var apiLoader : MaterialDialog? = null

    private fun showAPILoader() {
        apiLoader = showLoader()
    }

    private fun hideAPILoader() {
        apiLoader?.dismiss()
        apiLoader = null
    }

    open fun setup(){
        initAdapter()
        innerBinding.swipeRefresh.apply {
            setOnRefreshListener {
                mAdapter?.refresh()
            }
        }
        setupPlayer()

        setFragmentResultListenerOnActivity(PostatGiftSendFragment.GIFT_REQUEST_KEY){ _, _ ->
                Toast.makeText(requireContext(), "Gift sent successfully!", Toast.LENGTH_SHORT).show()
                mAdapter?.refresh()
        }

        setReportListener { type, success ->
            if (type!= ReportContentType.POSTAT || !success) return@setReportListener
            mAdapter?.apply {
                refresh()
                notifyDataSetChanged()
            }
            Toast.makeText(requireContext(), getString(R.string.postat_report_successfull), Toast.LENGTH_SHORT).show()
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        if (viewModel.user.profile?.premium == true) {
            return menuInflater.inflate(R.menu.menu_home_postat, menu)
        }
        else{
            return
        }
    }
    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_more -> showMoreMenu(menuItem)
            else -> return false
        }
        return true
    }

    private fun showMoreMenu(v: MenuItem) {
        val view = activity?.findViewById<View>(v.itemId)?: return
        val popup = PopupMenu(requireContext(),view)
        popup.menuInflater.inflate(R.menu.menu_home_postat_more, popup.menu)
        popup.setForceShowIcon(true)
        popup.menu.apply {

        }

        popup.setOnMenuItemClickListener { menuItem ->
            when(menuItem.itemId) {
                R.id.action_ignored_users -> {
                    navigateIgnoredPostatUsers()
                }
                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
    }

    open fun observer() {

        viewModel.onPostatUserIgnored.observe(viewLifecycleOwner){
            Toast.makeText(requireContext(), getString(R.string.postat_user_ignored_text, it), Toast.LENGTH_SHORT).show()
            mAdapter?.refresh()
        }

        viewModel.postatList.observe(viewLifecycleOwner) {
            mAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }

        viewModel.onUserUnFollow.observe(viewLifecycleOwner) {
            mAdapter?.refresh()
            Toast.makeText(requireContext(), getString(R.string.public_star_unfollowing_text, it), Toast.LENGTH_SHORT).show()
        }

        viewModel.onUserFollow.observe(viewLifecycleOwner) {
            mAdapter?.refresh()
            Toast.makeText(requireContext(), getString(R.string.public_star_following_text, it), Toast.LENGTH_SHORT).show()
        }

        viewModel.onPostatDeleted.observe(viewLifecycleOwner) {
            if (it) {
                Toast.makeText(requireContext(), getString(R.string.postat_deleted), Toast.LENGTH_SHORT).show()
                mAdapter?.refresh()
            }
        }

        viewModel.onShareCodeGenerated.observe(viewLifecycleOwner) { shareCode ->
            sharePostAt(shareCode)
        }

        viewModel.onSuccessfullyBlockedUserFromPosting.observe(viewLifecycleOwner) { isBlocked ->
            if (isBlocked) {
                showSnackbar(R.string.postat_block_success_message)
            }
        }

        viewModel.errorMessage.observe(viewLifecycleOwner) {
            showToast(message = it)
        }

        viewModel.actionLoading.observe(viewLifecycleOwner) {
            if (it) showAPILoader() else hideAPILoader()
        }

        viewModel.isNavigateToUpdatePost.observe(viewLifecycleOwner) {
            if (it.first) findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalCreatePostat(it.second))
        }
    }

    private fun sharePostAt(link: String) {
        if (link.isNotEmpty()) {
            val message=getString(R.string.postat_share_text, link)
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalInternalShareFragment(message, ForwardSource.POSTAT))
        }
    }

    private var currentlyFocusedPosition: Int? = null

    open fun onUserClick(item: Postat) {
        findNavController().navigateSafe(HomePublicFragmentDirections.actionHomePublicFragmentToUserPostatFeedFragment(item.userId,item.senderDetails.userLivePodiumId))
    }

    private fun playAtPosition(pos: Int) {
        val vh = innerBinding.feedPostatList.findViewHolderForAdapterPosition(pos)?: return
        Log.w("POSTATF", "playAtPosition: found vh for position $pos | $vh")
        (vh as? FeedPostatAdapter.FeedPostatViewHolder)?.playMediaIfReady(player?: return, audioPlayer?: return, currentlyFocusedPosition!=pos)
    }

    private fun updatePlaybackFocus() {
        Log.w("POSTATF", "updatePlaybackFocus: $currentlyFocusedPosition")
        fun getVisibleHeightPercentage(view: View): Int {

            val contentView = view.findViewById<View>(R.id.media_holder)

            val itemRect = Rect()
            val isParentViewEmpty = contentView.getLocalVisibleRect(itemRect)

            // Find the height of the item.
            val visibleHeight = itemRect.height().toDouble()
            val height = contentView.measuredHeight

            val viewVisibleHeightPercentage = visibleHeight / height * 100

            return if(isParentViewEmpty) viewVisibleHeightPercentage.roundToInt() else 0
        }

        val layoutMan = innerBinding.feedPostatList.layoutManager as? LinearLayoutManager?: return
        Log.w("POSTATF", "updatePlaybackFocus: got layoutMan")
        val firstVP = layoutMan.findFirstVisibleItemPosition()
        val lastVP = layoutMan.findLastVisibleItemPosition()
        Log.w("POSTATF", "updatePlaybackFocus: got VPs: $firstVP $lastVP")

        fun stopPlaying(pos: Int) {
            val vh = innerBinding.feedPostatList.findViewHolderForAdapterPosition(pos)?: return
            Log.w("POSTATF", "stopPlaying: found vh for position $pos | $vh")
            player?.stop()
            audioPlayer?.stop()
            (vh as? FeedPostatAdapter.FeedPostatViewHolder)?.stopMediaPlayback()
        }

        fun findFirstItemToPlay() {
            for (i in firstVP .. lastVP) {
                val vp = getVisibleHeightPercentage(layoutMan.findViewByPosition(i)?:return)
                if (vp>90 || firstVP==lastVP) {
                    Log.w("POSTATF", "findFirstItemToPlay: playing $i")
                    playAtPosition(i)
                    currentlyFocusedPosition = i
                    break
                }
            }
        }

        val percentage = getVisibleHeightPercentage(layoutMan.findViewByPosition(firstVP)?:return)
//            Log.d("POSTATF", "first item V%: $percentage")

        Log.w("POSTATF", "updatePlaybackFocus: got percentage $percentage")

        if ((percentage>90 || firstVP==lastVP) && currentlyFocusedPosition!=firstVP) {
            currentlyFocusedPosition?.let { cfp ->
                stopPlaying(cfp)
                Log.w("POSTATF", "stop currentlyPlayingItem $currentlyFocusedPosition")
                currentlyFocusedPosition = null
            }

            playAtPosition(firstVP)
            Log.w("POSTATF", "onScrolled: playing $firstVP")
            currentlyFocusedPosition = firstVP
        } else if (currentlyFocusedPosition!=null){
            val cpi = currentlyFocusedPosition?: return
            val cv = layoutMan.findViewByPosition(cpi)?: return
            val currentP = getVisibleHeightPercentage(cv)
            if (currentP<50) {
                stopPlaying(cpi)
                Log.w("POSTATF", "onScrolled: stop playing $currentlyFocusedPosition")
                currentlyFocusedPosition = null
                findFirstItemToPlay()
            }
        } else {
            findFirstItemToPlay()
        }
    }

    override fun onResume() {
        super.onResume()
        Log.w("POSTATF", "onResume")
        updatePlaybackFocus()
    }

    private fun initAdapter() {

        val layoutMan = LinearLayoutManager(context)

        mAdapter = FeedPostatAdapter(object : FeedPostatAdapter.FeedPostatActionListener {
            override fun onCommentsClicked(postatId: String, userId: Int) {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPostatCommentBottomSheetFragment(postatId,userId))
            }
            override fun onPostClicked(postat: Postat, view: View,position: Int) {
                showPopUpMenu(postat,view,position)
            }
            override fun onClickUser(pos: Int, item: Postat) {
                onUserClick(item)
            }

            override fun onGiftClicked(postat: Postat) {
                ensureInteractionAllowed {
                    //                findNavController().navigateSafe(CreatePostatFragmentDirections.actionGlobalGiftFragment(postat.userId, giftContext = GiftContext.POSTAT))
                    val isOwnUser = postat.userId == viewModel.user.id
                    val isVisitor = viewModel.user.citizenship == UserCitizenship.VISITOR
                    val isGolden = postat.senderDetails.citizenship?.isGolden?:false
                    /*if (isVisitor){
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalUpgradePremiumFragment())
                    }
                    else*/
                    if (isOwnUser) {
                        findNavController().navigateSafe(CreatePostatFragmentDirections.actionGlobalGiftFileFragment())
                        return@ensureInteractionAllowed
                    }
                    if (viewModel.isVisitor||isGolden) return@ensureInteractionAllowed
//                    else
//                    {
                        findNavController().navigateSafe(CreatePostatFragmentDirections.actionGlobalPostatGiftSendFragment(receiverId = postat.userId, singleTabItem = GiftContext.GIFT_POSTAT, messageId = postat.messageId))
//                    }
                }
            }

            @OptIn(UnstableApi::class)
            override fun getMediaSource(media: MediaItem): MediaSource? {
                val cookie = viewModel.cookies.value?:return null
                return getCookieMediaSource(media, cookie)
            }

            override fun onItemPageChanged(pos: Int) {
                if (currentlyFocusedPosition==pos) {
                    playAtPosition(pos)
                }
            }

            override fun onLivePodiumIndicatorClicked(postat: Postat) {
                val podiumId = postat.senderDetails.userLivePodiumId ?: return
                validateAndConfirmJoinFromGreenDot(podiumId, postat.senderDetails.name, viewModel.user)
            }

            override fun isSelf(item: Postat): Boolean {
                return item.userId == viewModel.user.id
            }
        })

        val scrollListener = object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                Log.w("POSTATF", "onScrolled: ${lifecycle.currentState}")
                if (!lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
                    return
                }
                updatePlaybackFocus()
            }
        }

        innerBinding.feedPostatList.apply {
            layoutManager = layoutMan
            addOnScrollListener(scrollListener)
            setHasFixedSize(false)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                innerBinding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)
                if (loadState.refresh !is LoadState.Loading) {
                    innerBinding.swipeRefresh.isRefreshing = false
                }
                viewModel.dataLoadingMore.postValue(loadState.append is LoadState.Loading)
            }
            registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
                override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
                    super.onItemRangeMoved(fromPosition, toPosition, itemCount)
                    if ((fromPosition == 0 || toPosition == 0) && layoutMan.findFirstCompletelyVisibleItemPosition() == 0) {
                        layoutMan.scrollToPosition(0)
                    }
                }
            })
            innerBinding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
                findViewById<AppCompatImageView>(R.id.eds_empty_image).setImageResource(R.drawable.bg_empty_postat)
                findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.not_posted_anything_yet)
            }
        }

    }

    private var player: ExoPlayer? = null
    private var audioPlayer: ExoPlayer? = null

    private fun releasePlayer() {
        Log.w("POSTATF", "releasePlayer")
        player?.apply {
            stop()
            release()
            player = null
        }

        audioPlayer?.apply {
            stop()
            release()
            audioPlayer = null
        }
    }

    @OptIn(UnstableApi::class)
    private fun setupPlayer() {
        if (player == null) {
            player = ExoPlayer.Builder(requireContext()).build()
        }
        player?.apply {
            repeatMode = Player.REPEAT_MODE_ONE
            playWhenReady = false
            prepare()
        }

        if (audioPlayer == null) {
            audioPlayer = ExoPlayer.Builder(requireContext()).build()
        }
        audioPlayer?.apply {
            repeatMode = Player.REPEAT_MODE_ONE
            playWhenReady = false
            prepare()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        releasePlayer()
    }

    override fun onPause() {
        super.onPause()
        player?.stop()
        audioPlayer?.stop()
        currentlyFocusedPosition = null
    }

    private fun showPopUpMenu(post: Postat, v: View, position: Int) {
        val popup = PopupMenu(requireContext(), v)

        popup.menuInflater.inflate(R.menu.menu_postat_post_actions, popup.menu)
        popup.setForceShowIcon(true)
        val isMine = viewModel.user.id == post.userId
        val iAmVisitor = viewModel.user.citizenship == UserCitizenship.VISITOR
        val iAmPremium = viewModel.user.profile?.premium == true
        val haveEmpowermentToBlockUser = viewModel.user.userEmpowerment?.allowBlockUserPostingPostat == true
        val haveEmpowermentToDelete = viewModel.user.userEmpowerment?.allowDeletePostatPost == true
        val isPresidentPostat =  post.senderDetails.citizenship == UserCitizenship.PRESIDENT
        val postatIsBlocked = post.senderDetails.blockedByEmpoweredUser
        val isMySuperstarsPost = viewModel.user.superStarId == post.userId
        val isGolden = post.senderDetails.citizenship?.isGolden?:false
        Log.d("POSTID","post Details: $post")

        popup.menu.apply {
            findItem(R.id.postat_action_edit).isVisible = isMine
            findItem(R.id.postat_action_hide).isVisible = isMine
            findItem(R.id.postat_action_delete).isVisible = isMine || haveEmpowermentToDelete

            findItem(R.id.postat_action_user_id).isVisible = !isMine
            findItem(R.id.postat_action_follow).isVisible = (!isMine && post.isFollowed!=true && !isMySuperstarsPost)
            findItem(R.id.postat_action_unfollow).isVisible = (!isMine && post.isFollowed==true && !isMySuperstarsPost)
            findItem(R.id.postat_action_send_gift).isVisible =/* !isMine && !iAmVisitor*/!isMine && post.senderDetails.citizenship!=UserCitizenship.VISITOR && !iAmVisitor && !isGolden
            findItem(R.id.postat_action_send_flax).isVisible = /*!isMine && !iAmVisitor*/!isMine && (post.senderDetails.citizenship!=UserCitizenship.VISITOR && post.senderDetails.citizenship!= UserCitizenship.GOLDEN) && !iAmVisitor && !isGolden
            findItem(R.id.postat_action_share_postat).isVisible = true
            findItem(R.id.postat_action_ignore_user).isVisible =!isMine && showIgnoreUserOption && iAmPremium
            findItem(R.id.postat_action_block_user).isVisible = !isMine && (!isPresidentPostat && haveEmpowermentToBlockUser) && !postatIsBlocked && !isGolden

            findItem(R.id.action_report).apply {
                isVisible = !isMine && viewModel.user.canReport(post.senderDetails)
                setupMenuItemTextColor(color = R.color.colorError, context = requireContext(), isTextBold = true)
            }
            findItem(R.id.action_report_and_hide).apply {
                isVisible = !isMine && viewModel.user.canReportAndHide(post.senderDetails)
                setupMenuItemTextColor(color = R.color.colorError, context = requireContext(), isTextBold = true)
            }
            findItem(R.id.action_ban).apply {
                isVisible = !isMine && viewModel.user.canBan(post.senderDetails)
                setupMenuItemTextColor(color = R.color.colorError, context = requireContext(), isTextBold = true)
            }
        }

        popup.setOnMenuItemClickListener { menuItem ->
            when (menuItem.itemId) {
                R.id.postat_action_user_id -> {
                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(post.userId))
                }
                R.id.postat_action_follow -> viewModel.followUser(post.userId)
                R.id.postat_action_unfollow -> viewModel.unFollowUser(userId = post.userId, userName = post.senderDetails.name)
                R.id.postat_action_send_gift -> {
                    findNavController().navigateSafe(CreatePostatFragmentDirections.actionGlobalGiftFragment(post.userId, giftContext = GiftContext.GIFT_POSTAT))
                }
                R.id.postat_action_send_flax -> {
                    findNavController().navigateSafe(CreatePostatFragmentDirections.actionGlobalFlaxSendFlax(post.userId))
                }
                R.id.postat_action_delete -> ensurePostatPostingAllowed { confirmPostatDelete(post.messageId,position) }
                R.id.postat_action_edit -> {
                    ensurePostatPostingAllowed {
                        viewModel.checkPermissionToUpdatePostat(id = post.messageId)
                    }
                }
                R.id.postat_action_hide -> confirmHidePostat(post.messageId)
                R.id.postat_action_share_postat -> {
                    viewModel.getShareCode(post.messageId)
                }
                R.id.postat_action_ignore_user -> {
                    IgnorePostatUser(post,true)
                }
                R.id.postat_action_block_user -> {
                    confirmBlockUserFromPostat(userId = post.userId)
                }

                R.id.action_report-> {
                    ensureReportContentAllowed {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.Postat(post, reportType = ReportType.REPORT).serialize()))
                    }
                }
                R.id.action_report_and_hide-> {
                    ensureReportContentAllowed {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.Postat(post, reportType = ReportType.REPORT_AND_HIDE).serialize()))
                    }
                }
                R.id.action_ban-> {
                    ensureReportBanAllowed {
                        post.senderDetails.asBasicUser().let {
                            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.User(it, reportType = ReportType.BAN).serialize()))
                        }
                    }
                }

                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
    }

    private fun confirmPostatDelete(messageId: String, position: Int) {
        confirmAction(message = getString(R.string.postat_delete_post)){
            viewModel.deletePost(messageId,position)
        }
    }

    private fun confirmHidePostat(messageId: String) {
        confirmAction(message = getString(R.string.postat_hide_post)){
            viewModel.hidePost(messageId)
        }
    }

    private fun navigateIgnoredPostatUsers() {
        findNavController().navigateSafe(HomePublicFragmentDirections.actionHomePublicFragmentToPostatIgnoredUsersFragment())
    }

    private fun IgnorePostatUser(post: Postat, isBlock: Boolean){
        confirmAction(message = getString(R.string.postat_ignore_user_dialog)){
            viewModel.ignorePostatUser(post,true)
        }
    }

    private fun confirmBlockUserFromPostat(userId: Int) {
        confirmAction(
            message = R.string.postat_block_user_confirm_message,
            positiveTitle = R.string.common_block,
            negativeTitle = R.string.common_close
        ) {
            viewModel.blockUserFromPostat(userId = userId)
        }
    }
}