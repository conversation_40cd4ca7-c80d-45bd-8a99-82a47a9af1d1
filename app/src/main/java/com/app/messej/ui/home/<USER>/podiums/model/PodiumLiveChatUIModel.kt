package com.app.messej.ui.home.publictab.podiums.model

import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.UserRatingProvider
import com.app.messej.data.model.api.podium.PodiumBirthdayTopGiftersEventResponse
import com.app.messej.data.model.api.podium.UserStats
import com.app.messej.data.model.enums.PodiumLiveChatType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.app.messej.data.model.socket.PodiumLiveChatPayload
import com.app.messej.data.model.socket.PodiumMaidanScoreUpdatePayload
import com.app.messej.ui.utils.DataFormatHelper.numberToKWithFractions
import com.github.f4b6a3.uuid.UuidCreator

sealed class PodiumLiveChatUIModel {

    abstract class UserWithRating: AbstractUser(), UserRatingProvider{
        abstract val tribeName: String?
        abstract val chatFrozenForUser: Boolean
    }

    interface ChatWithData {
        val podiumId: String
        val chatId: String
        val created: String
        val senderDetails: SenderDetails
        val userStats: UserStats?
        val userId: Int
        val countryCode: String?
        var chatFrozenForUser: Boolean
        val userTribeName   : String?
        val reportPayFine: Boolean?

        val userWithRating: UserWithRating
            get() = object : UserWithRating() {
                override val id: Int
                    get() = userId
                override val name: String
                    get() = senderDetails.name
                override val thumbnail: String?
                    get() = senderDetails.thumbnail
                override val username: String
                    get() = senderDetails.username
                override val membership: UserType
                    get() = senderDetails.membership
                override val verified: Boolean
                    get() = senderDetails.verified
                override val userRating: Double
                    get() = (userStats?.rating ?: 0).toDouble() / 100
                override val citizenship: UserCitizenship?
                    get() = senderDetails.citizenship

                override val countryCode: String?
                    get() = <EMAIL>

                override val tribeName: String?
                    get() = userTribeName

                override val chatFrozenForUser: Boolean
                    get() = <EMAIL>

            }
    }

    data class About(
        var name: String,
        var about: String
    ): PodiumLiveChatUIModel()

    data class ChatMessage(
        override val podiumId     : String,
        override val chatId       : String,
        override val created      : String,
        override val senderDetails: SenderDetails,
        override val userId       : Int,
        override val countryCode  : String?,
        override var chatFrozenForUser: Boolean = false,
        override val userTribeName   : String?,
        override val userStats: UserStats?,
        override val reportPayFine: Boolean?,

        val message      : String,
    ): PodiumLiveChatUIModel(), ChatWithData

    data class UserJoined(
        override val podiumId     : String,
        override val chatId       : String,
        override val created      : String,
        override val senderDetails: SenderDetails,
        override val userId       : Int,
        override val countryCode  : String?,
        override var chatFrozenForUser: Boolean = false,
        override val userTribeName   : String?,
        override val userStats: UserStats?,
        override val reportPayFine: Boolean?,
    ): PodiumLiveChatUIModel(), ChatWithData

    data class PaidLike(
        override val podiumId     : String,
        override val chatId       : String,
        override val created      : String,
        override val senderDetails: SenderDetails,
        override val userId       : Int,
        override val countryCode  : String?,
        override var chatFrozenForUser: Boolean = false,
        override val userTribeName   : String?,
        override val userStats: UserStats?,
        override val reportPayFine: Boolean?,
    ): PodiumLiveChatUIModel(), ChatWithData

    data class MaidanContribution(
        override val podiumId     : String,
        override val chatId       : String,
        override val created      : String,
        override val senderDetails: SenderDetails,
        override val userId       : Int,
        override val countryCode  : String?,
        override var chatFrozenForUser: Boolean = false,
        override val userTribeName   : String?,
        override val userStats: UserStats?,
        override val reportPayFine: Boolean?,

        val type: PodiumMaidanScoreUpdatePayload.MaidanContributionType,
        val coins: Int,
        val totalCoins: Int,
        val isBirthdayPodium: Boolean = false
    ): PodiumLiveChatUIModel(), ChatWithData {
        val giftType: Boolean
            get() = type == PodiumMaidanScoreUpdatePayload.MaidanContributionType.GIFT
        val specialGift: Boolean
            get() = (giftType && coins >= PodiumMaidanScoreUpdatePayload.MIN_COINS_FOR_CHAT_HIGHLIGHT) || isBirthdayPodium
    }

    companion object {

        fun from(payload: PodiumMaidanScoreUpdatePayload): PodiumLiveChatUIModel {
            return MaidanContribution(
                podiumId = payload.podiumId,
                chatId = UuidCreator.getTimeBased().toString(),
                created = payload.timeCreated,
                senderDetails = payload.sender,
                userId = payload.sender.id,
                countryCode = payload.sender.countryCode,
                userTribeName = "",
                userStats = null,

                type = payload.eventType,
                coins = payload.coins,
                totalCoins = payload.totalCoins,
                reportPayFine = false
            )
        }

        fun from(payload: PodiumBirthdayTopGiftersEventResponse.BirthdayGiftChatData): PodiumLiveChatUIModel {
            return MaidanContribution(
                podiumId = payload.podiumId,
                chatId = "",
                created = "",
                senderDetails = payload.sender,
                userId = payload.sender.id,
                type = PodiumMaidanScoreUpdatePayload.MaidanContributionType.GIFT,
                coins = payload.coins,
                totalCoins = payload.totalCoins,
                countryCode = payload.sender.countryCode,
                userTribeName = "",
                userStats = null,
                reportPayFine = false,
                isBirthdayPodium = true
            )
        }

        fun from(payload: PodiumLiveChatPayload): PodiumLiveChatUIModel {
            return when (payload.chatType) {
                PodiumLiveChatType.NORMAL -> ChatMessage(
                    podiumId = payload.podiumId,
                    chatId = payload.chatId,
                    created = payload.created,
                    senderDetails = payload.senderDetails,
                    userId = payload.userId,
                    countryCode = payload.countryCode,
                    message = payload.message,
                    userTribeName = payload.userTribeName,
                    userStats = payload.userStats,
                    reportPayFine = payload.reportPayFine
                )
                PodiumLiveChatType.USER_JOIN -> UserJoined(
                    podiumId = payload.podiumId,
                    chatId = payload.chatId,
                    userStats = payload.userStats,
                    created = payload.created,
                    senderDetails = payload.senderDetails,
                    userId = payload.userId,
                    countryCode = payload.countryCode,
                    userTribeName = payload.userTribeName,
                    reportPayFine = payload.reportPayFine
                )
                PodiumLiveChatType.PAID_LIKE -> PaidLike(
                    podiumId = payload.podiumId,
                    chatId = payload.chatId,
                    created = payload.created,
                    senderDetails = payload.senderDetails,
                    userId = payload.userId,
                    countryCode = payload.countryCode,
                    userTribeName = payload.userTribeName,
                    userStats = payload.userStats,
                    reportPayFine = payload.reportPayFine
                )
            }
        }
    }
}