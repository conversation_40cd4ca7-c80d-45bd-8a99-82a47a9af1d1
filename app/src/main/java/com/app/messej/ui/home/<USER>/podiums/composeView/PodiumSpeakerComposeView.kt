package com.app.messej.ui.home.publictab.podiums.composeView

import android.content.res.Configuration
import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.IconButton
import androidx.compose.material.ripple
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.app.messej.R
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomHorizontalSpacer
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomVerticalSpacer
import com.app.messej.ui.home.publictab.podiums.model.PodiumComposeSpeakerModel
import com.app.messej.ui.home.publictab.podiums.model.PodiumComposeSpeakerModel.Companion.testPodiumSubSpeakerModel

interface PodiumSpeakerListener {
    fun onSpeakerClick(item: PodiumComposeSpeakerModel)
    fun onEmptySpeakZoneClick()
    fun onMicToggle()
}

@Composable
fun PodiumSpeakerComposeView(
    modifier: Modifier = Modifier,
    item: PodiumComposeSpeakerModel?,
    podiumKind: PodiumKind?,
    isHostTagNeededWithSpeakerName: Boolean,
    showControls: Boolean,
    isLoading: State<Boolean>,
    listener: PodiumSpeakerListener
) {
    //Added for button press effect
    val source = remember { MutableInteractionSource() }
    val indication = ripple()

    Box(modifier = modifier
        .border(width = 1.dp, color = colorResource(id = if (item?.currentlySpeaking == true) R.color.colorSecondary  else R.color.colorSurface))
        .clickable(interactionSource = source, indication = indication, enabled = !isLoading.value) {
            if (item == null) {
                listener.onEmptySpeakZoneClick()
            } else {
                listener.onSpeakerClick(item = item)
            }
        }
    ) {
        if (isLoading.value) {
            PodiumSpeakerShimmerView(
                isMainSpeaker = false
            )
            return@Box
        }
        if (item == null) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(color = colorResource(id = R.color.colorSurfaceSecondary)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_podium_mic_speaker_on),
                    tint = colorResource(id = R.color.colorPrimary),
                    contentDescription = null
                )
            }
        } else {
            SetPodiumUserImageBackgroundView(
                imageUrl = item.speaker.thumbnail
            )

            PodiumSpeakerTitleAndCountryFlagView(
                modifier = Modifier.align(alignment = Alignment.TopCenter),
                item = item,
                showLocalVideo = null,
                isHostTagNeededWithSpeakerName = isHostTagNeededWithSpeakerName,
                podiumKind = podiumKind,
                isMainSpeakerView = false
            )

            if (showControls) {
                SubSpeakerSelfActionHolder(
                    modifier = Modifier.align(alignment = Alignment.Center),
                    item = item,
                    onToggleMic = listener::onMicToggle,
                    onViewMoreClick = { listener.onSpeakerClick(item) }
                )
            } else {
                PodiumUserImageHolder(
                    modifier = Modifier
                        .size(size = 40.dp)
                        .align(alignment = Alignment.Center),
                    item = item
                )
            }

            PodiumSpeakerFooterView(
                modifier = Modifier.align(alignment = Alignment.BottomCenter),
                item = item,
                isMainSpeakerTile = false,
                showControls = showControls
            )
        }
    }
}


@Composable
private fun PodiumSubSpeakerComposePreview() {
    Column {
        Text(
            text = "Main Speaker Own User View",
            color = colorResource(id = R.color.textColorPrimary)
        )
        PodiumSpeakerComposeView(
            modifier = Modifier.size(size = 200.dp),
            item = testPodiumSubSpeakerModel,
            podiumKind = PodiumKind.BIRTHDAY,
            isHostTagNeededWithSpeakerName = true,
            showControls = true,
            isLoading = remember { mutableStateOf(value = false) },
            listener = object :PodiumSpeakerListener {
                override fun onSpeakerClick(item: PodiumComposeSpeakerModel) {
                }
                override fun onEmptySpeakZoneClick() {
                }
                override fun onMicToggle() {
                }
            }
        )
        CustomVerticalSpacer(
            space = dimensionResource(id = R.dimen.activity_margin)
        )
        Text(
            text = "Main Speaker Other's View",
            color = colorResource(id = R.color.textColorPrimary)
        )
        PodiumSpeakerComposeView(
            modifier = Modifier.size(size = 200.dp),
            item = testPodiumSubSpeakerModel,
            podiumKind = PodiumKind.BIRTHDAY,
            isHostTagNeededWithSpeakerName = true,
            showControls = false,
            isLoading = remember { mutableStateOf(value = false) },
            listener = object :PodiumSpeakerListener {
                override fun onSpeakerClick(item: PodiumComposeSpeakerModel) {
                }
                override fun onEmptySpeakZoneClick() {
                }
                override fun onMicToggle() {
                }
            }
        )
    }
}

@Preview(showBackground = true, uiMode = Configuration.UI_MODE_NIGHT_YES)
@Composable
private fun PodiumScreenNightModePreview() {
    PodiumSubSpeakerComposePreview()
}

@Preview(showBackground = true, uiMode = Configuration.UI_MODE_NIGHT_NO)
@Composable
private fun PodiumScreenLightModePreview() {
    PodiumSubSpeakerComposePreview()
}

@Composable
private fun SubSpeakerSelfActionHolder(
    modifier : Modifier,
    item: PodiumComposeSpeakerModel?,
    onToggleMic: () -> Unit,
    onViewMoreClick: () -> Unit
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        SubSpeakerSelfActionIcon(
            icon = if (item?.muted == true) R.drawable.ic_podium_mic_speaker_off else R.drawable.ic_podium_mic_speaker_on,
            onClick = onToggleMic
        )
        CustomHorizontalSpacer(
            space = dimensionResource(id = R.dimen.line_spacing)
        )
        SubSpeakerSelfActionIcon(
            icon = R.drawable.ic_caret_down_rounded,
            onClick = onViewMoreClick
        )
    }
}

@Composable
private fun SubSpeakerSelfActionIcon(
    @DrawableRes icon: Int,
    onClick: () -> Unit
) {
    IconButton(
        onClick = onClick,
        modifier = Modifier.size(size = 30.dp),
        colors = IconButtonDefaults.iconButtonColors(
            containerColor = Color(0x55000000),
            contentColor = colorResource(id = R.color.textColorOnPrimary)
        )
    ) {
        Icon(
            painter = painterResource(id = icon),
            modifier = Modifier.padding(all = dimensionResource(id = R.dimen.line_spacing)),
            contentDescription = null
        )
    }
}