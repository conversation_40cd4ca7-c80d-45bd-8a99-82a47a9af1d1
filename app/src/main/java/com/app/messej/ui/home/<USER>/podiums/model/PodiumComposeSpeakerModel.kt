package com.app.messej.ui.home.publictab.podiums.model

import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.getValue
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType

data class PodiumComposeSpeakerModel(
    var speaker: PodiumSpeaker
) {
    companion object {
        val testPodiumSpeakerModel = PodiumComposeSpeakerModel(
            speaker= PodiumSpeaker(id=1948,
                name="<PERSON><PERSON>",
                username="abijithc", thumbnail="",
                membership= UserType.PREMIUM,
                citizenship= UserCitizenship.CITIZEN,
                role= Podium.PodiumUserRole.MANAGER,
                verified=false,
                invitedToBeAdmin=false,
                countryCode="IN",
                shownOnMainScreen=true,
                showOnStage=false,
                muted=true,
                likes=0, online=true,
                allowVideoInPodiums=true,
                cameraViolation=false,
                cameraExpiry=null,
                coinsReceived=242.14,
                coinSent=0.0,
                reportPayFine=false
            )
        )

        val testPodiumSubSpeakerModel = PodiumComposeSpeakerModel(
            speaker= PodiumSpeaker(id=1948,
                name="Abi",
                username="abijithc", thumbnail="",
                membership= UserType.PREMIUM,
                citizenship= UserCitizenship.CITIZEN,
                role= Podium.PodiumUserRole.AUDIENCE,
                verified=false,
                invitedToBeAdmin=false,
                countryCode="IN",
                shownOnMainScreen=false,
                showOnStage=false,
                muted=true,
                likes=0,
                online=true,
                allowVideoInPodiums=true,
                cameraViolation=false,
                cameraExpiry=null,
                coinsReceived=242.14,
                coinSent=0.0,
                reportPayFine=false
            )
        )
    }

    var muted by mutableStateOf(value = speaker.muted)
    var currentlySpeaking by mutableStateOf(value = false)
    var assemblySpeakingStatus by mutableStateOf(value = speaker.speakingStatus)
    var online by mutableStateOf(value = speaker.online)
    var admin by mutableStateOf(value = false)
    var isManager by mutableStateOf(value = speaker.online)
    var coinsToDisplay by mutableStateOf(value = speaker.coinsReceivedFormatted)
    var coinsSentToDisplay by mutableStateOf(value = speaker.coinsSentFormated)
    var countryFlag by mutableStateOf<Int?>(value = null)
}