package com.app.messej.ui.home.publictab.podiums.manage

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.ReportPackage
import com.app.messej.data.model.enums.BlockUnblockAction
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.data.model.enums.PodiumEnforcementDialogType
import com.app.messej.data.model.enums.ReportType
import com.app.messej.databinding.FragmentPodiumWaitlistActionsBottomSheetBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensurePodiumCreateAllowed
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureReportBanAllowed
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureReportUserAllowed
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.setCitizenshipWithFlixRate
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class PodiumWaitListActionsBottomSheetFragment : BasePodiumActionsBottomSheetFragment() {

    private lateinit var binding: FragmentPodiumWaitlistActionsBottomSheetBinding

    private val args: PodiumWaitListActionsBottomSheetFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_waitlist_actions_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observer()
    }

    private fun observer() {
        userStatsViewModel.userStats.observe(viewLifecycleOwner) {
            binding.userInfo.stats = it
            it?: return@observe
            setCitizenshipWithFlixRate(binding.userInfo.userFlixRate,it,viewModel.isManager(args.userId))
        }
        viewModel.onUserAllowedToSpeak.observe(viewLifecycleOwner) { msg: String? ->
            msg?.let {
                Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
            }
            findNavController().popBackStack()
        }
        viewModel.onRequestToSpeakCancelled.observe(viewLifecycleOwner) { msg: String? ->
            msg?.let {
                Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
            }
            findNavController().popBackStack()
        }
        viewModel.onUserBlockToggled.observe(viewLifecycleOwner) { msg: String? ->
            msg?.let {
                Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
            }
            findNavController().popBackStack()
        }
        viewModel.onAdminActionFinished.observe(viewLifecycleOwner) {
            findNavController().popBackStack()
        }
        viewModel.onRequestToSpeakDeclined.observe(viewLifecycleOwner) {
            if (it) findNavController().popBackStack()
        }
        viewModel.onAllowedUserInsufficientBalanceToSpeak.observe(viewLifecycleOwner) {
            showToast(message = getString(R.string.podium_accept_invitation_insufficient_coin_message_to_host, it))
            findNavController().popBackStack()
        }
    }

    private fun setup() {
        viewModel.podiumId.value?.let {
            userStatsViewModel.setUserAndPodium(args.userId, it)
        }
        viewModel.getSpeaker(args.userId)
        viewModel.getWaiter(args.userId)?.also { speaker ->
            binding.speaker = speaker
            binding.isSelf = speaker.id==viewModel.user.id
            binding.actionInfo.setOnClickListener {
                val action = PodiumWaitListActionsBottomSheetFragmentDirections.actionGlobalPublicUserProfileFragment(args.userId,false)
                findNavController().navigateSafe(action,navOptions = NavOptions.Builder()
                    .setPopUpTo(R.id.podiumWaitListActionsBottomSheetFragment, inclusive = true)
                    .build())
            }

            binding.actionAllowToSpeak.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    confirmAction(
                        title = R.string.podium_action_allow_to_speak_confirm_title,
                        message = R.string.podium_action_allow_to_speak_confirm_message
                    ) {
                        viewModel.allowToSpeak(speaker)
                    }
                }
            }

            binding.actionBlock.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    confirmAction(
                        title = null, message = R.string.podium_action_block_user_confirm_message
                    ) {
                        viewModel.toggleUserBlock(args.userId, action = BlockUnblockAction.BLOCK)
                    }
                }
            }

            binding.actionAppointAdmin.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    appointAdmin(speaker)
                }
            }

            binding.actionDismissAdmin.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    dismissAdmin(speaker)
                }
            }

            binding.userInfo.sendGift.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    viewModel.podium.value?.let {
                        val action = PodiumWaitListActionsBottomSheetFragmentDirections.actionGlobalGiftFragment(args.userId, giftContext =  GiftContext.GIFT_PODIUM, managerId = it.managerId, giftContextId = it.id)
                        findNavController().navigateSafe(action,navOptions = NavOptions.Builder()
                            .setPopUpTo(R.id.podiumSpeakerActionsBottomSheetFragment, inclusive = true)
                            .build())
                    }
                }
            }

//            binding.actionSendFlax.setOnClickListener {
//                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
//                    val action = PodiumWaitListActionsBottomSheetFragmentDirections.actionGlobalFlaxTransfer(args.userId)
//                    findNavController().navigateSafe(action,navOptions = NavOptions.Builder()
//                        .setPopUpTo(R.id.podiumSpeakerActionsBottomSheetFragment, inclusive = true)
//                        .build())
//                }
//            }

            binding.actionCancelAdminInvite.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    cancelAdminInvite(speaker)
                }
            }

            binding.actionCancelRequest.setOnClickListener {
                confirmAction(
                    title = null,
                    message = R.string.podium_action_cancel_speak_request_confirm_message
                ) {
                    viewModel.cancelRequestToSpeak()
                }
            }

            binding.actionDecline.setOnClickListener {
                confirmAction(
                    title = null,
                    message = R.string.podium_action_decline_speak_request_confirm_message
                ) {
                    viewModel.declineRequestToSpeak(args.userId)
                }
            }
            binding.actionPauseGift.setOnClickListener {
                pauseUserGift(speaker.id)
            }

            binding.actionReportUser.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    ensureReportUserAllowed {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.User(speaker.asBasicUser(), reportType = ReportType.REPORT).serialize()))
                    }
                }
            }

            binding.actionBanUser.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    ensureReportBanAllowed {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.User(speaker.asBasicUser(), reportType = ReportType.BAN).serialize()))
                    }
                }
            }

        }?: run {
            findNavController().popBackStack()
        }
    }

}