package com.app.messej.ui.home.publictab.podiums.live.birthday

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.compose.ui.platform.ComposeView
import androidx.databinding.DataBindingUtil
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.data.model.enums.PodiumEnforcementDialogType
import com.app.messej.data.model.enums.TheaterCharge
import com.app.messej.databinding.FragmentPodiumLiveBirthdayBinding
import com.app.messej.databinding.ItemPodiumLikesContainerBinding
import com.app.messej.databinding.ItemPodiumSpeakerMainBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensurePodiumCreateAllowed
import com.app.messej.ui.home.promobar.CommonPromoBarUtil.setupPodiumPromoBoard
import com.app.messej.ui.home.publictab.maidan.PodiumLiveBirthdayTopGiftersQuickAdapter
import com.app.messej.ui.home.publictab.podiums.composeView.PodiumSpeakerListener
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveAbstractFragment
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel
import com.app.messej.ui.home.publictab.podiums.manage.PodiumSpeakerActionsBottomSheetFragmentDirections
import com.app.messej.ui.home.publictab.podiums.model.PodiumComposeSpeakerModel
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.PermissionsHelper.checkAudioPermission
import com.google.android.material.button.MaterialButton
import com.kennyc.view.MultiStateView
import com.scwang.smart.refresh.layout.SmartRefreshLayout

class PodiumLiveBirthdayFragment() : PodiumLiveAbstractFragment() {

    companion object {
        private const val TAG = "PLBF"
    }

    private val args: PodiumLiveBirthdayFragmentArgs by navArgs()
    override val viewModel: PodiumLiveViewModel by navGraphViewModels(navGraphId = R.id.nav_live_podium)
    override lateinit var binding: FragmentPodiumLiveBirthdayBinding
    private lateinit var topGiftersAdapter : PodiumLiveBirthdayTopGiftersQuickAdapter

    override val podiumIdArg: String
        get()= args.podiumId

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_live_birthday, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initTopGifterAdapter()
        setup()
        observer()
    }

    override fun ItemPodiumSpeakerMainBinding.onMainScreenSetup() {
        showCameraToggle = true
    }

    override val liveBindingElements: LiveBindingElements = object: LiveBindingElements {
        override val liveChat: RecyclerView?
            get() = binding.liveChat

        override val likesContainer: ItemPodiumLikesContainerBinding?
            get() = null

        override fun onPodiumLoading(loading: Boolean) {
            binding.header.podiumHeaderMultiStateView.viewState = if (loading) MultiStateView.ViewState.LOADING else MultiStateView.ViewState.CONTENT
        }

        override fun showLocalVideoSurface(show: Boolean) {

        }

        override val actionDecorHolderTop: LinearLayoutCompat?
            get() = binding.actionDecorHolderTop

        override val chatSend: MaterialButton?
            get() = binding.chatSendButton

        override val actionPaidLike: MaterialButton?
            get() = null

        override val actionShare: MaterialButton?
            get() = null

        override val liveCounter: ViewGroup?
            get() = binding.header.liveCounter

        override val scrollerLayout: SmartRefreshLayout
            get() = binding.refreshLayout

        override val anthemOverlay: ComposeView?
            get() = null

    }

    override fun onSecondResume() {

    }

    override fun toLiveUsersList() {
        val action = PodiumLiveBirthdayFragmentDirections.actionPodiumLiveFragmentToPodiumLiveUsersListBottomSheetFragment()
        findNavController().navigateSafe(direction = action)
    }

    override fun toBuyCamera(buy: Boolean) {
        val action = PodiumLiveBirthdayFragmentDirections.actionPodiumLiveBirthdayFragmentToPodiumBuyCameraBottomSheetFragment(buyAction = buy)
        findNavController().navigateSafe(direction = action)
    }

    override fun toLiveChatActions(userId: Int) {
        val action = PodiumLiveBirthdayFragmentDirections.actionPodiumLiveBirthdayFragmentToPodiumLiveChatActionsBottomSheetFragment(userId)
        findNavController().navigateSafe(direction = action)
    }

    override fun toAdminList() {
        val action = PodiumLiveBirthdayFragmentDirections.actionPodiumLiveBirthdayFragmentToPodiumAdminsBottomSheetFragment()
        findNavController().navigateSafe(direction = action)
    }

    override fun toRestrictedUsers() {
        val action = PodiumLiveBirthdayFragmentDirections.actionPodiumLiveBirthdayFragmentToPodiumBlockedUsersBottomSheetFragment()
        findNavController().navigateSafe(direction = action)
    }

    override fun toInvitations() {
        val action = PodiumLiveBirthdayFragmentDirections.actionPodiumLiveBirthdayFragmentToPodiumInviteBottomSheetFragment(podiumId = podiumIdArg)
        findNavController().navigateSafe(action)
    }

    private fun setup() {
        binding.header.icShare.setOnClickListener {
            sharePodium()
        }

        setSpeakerComposeView()

        binding.giftContainer.setOnClickListener {
            sendGiftToManager()
        }

        binding.header.giftCounter.setOnClickListener {
            showChargesBottomSheet()
        }

        binding.header.podiumDp.setOnClickListener {
            showMoreMenu(it)
        }

        setupPodiumPromoBoard(binding.ticker)
    }

    private fun setSpeakerComposeView() {
        binding.spekersComposeView.setContent {
            PodiumBirthdayComposeLayout(
                viewModel = viewModel,
                subSpeakerListener = object : PodiumSpeakerListener {
                    override fun onSpeakerClick(item: PodiumComposeSpeakerModel) {
                        onActiveSpeakerClick(speakerId = item.speaker.id)
                    }

                    override fun onMicToggle() {
                        viewModel.muteToggleSelf()
                    }

                    override fun onEmptySpeakZoneClick() {
                        onEmptySpeakerZoneClick()
                    }
                },
                toggleCamera = { toggleCamera() },
                toggleCameraFacing = { toggleCameraFacing() },
                onMainSpeakerClick = {
                    onActiveSpeakerClick(speakerId = viewModel.mainScreenComposeSpeaker.value?.speaker?.id ?: return@PodiumBirthdayComposeLayout)
                }
            )
        }
    }

    private fun onEmptySpeakerZoneClick() {
        ensureAnthemNotPlaying {
            ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                if (viewModel.showRequestToSpeak.value == true) {
                    val coinBalance = viewModel.podium.value?.audienceFee ?: 0
                    val isPaidSpeakingFee =
                        viewModel.podium.value?.audienceFeePaid == true
                    val haveEmpowermentToSpeak =
                        viewModel.user.userEmpowerment?.allowJoinSpeakPodiumForFree == true
                    val message =
                        if (coinBalance == 0 || viewModel.podium.value?.isAdmin == true || isPaidSpeakingFee || haveEmpowermentToSpeak) getString(
                            R.string.podium_theater_speak_confirmation_free
                        )
                        else getString(
                            R.string.podium_speaking_paid_confirmation_free,
                            "$coinBalance"
                        )

                    showSpeakingConfirmationAlert(
                        haveEmpowermentToSpeak = haveEmpowermentToSpeak,
                        confirmMessage = message,
                        userCoins = viewModel.user.coinBalance.toInt(),
                        podiumSpeakingFee = coinBalance
                    ) {
                        checkAudioPermission(binding.root) {
                            viewModel.requestToSpeak()
                        }
                    }
                }
            }
        }
    }

    private fun observer() {

        viewModel.speakers.observe(viewLifecycleOwner) {
            Log.d(TAG, "observe: Speakers $it")
        }

        viewModel.podium.observe(viewLifecycleOwner) { pod ->
            val coins = pod?.speakers?.find { it.isManager }?.coinsReceivedFormatted
            Log.d(TAG, "observe: Coins $coins")
            Log.d(TAG, "observe: Podium Detail $pod")
        }

        viewModel.showRequestToSpeak.observe(viewLifecycleOwner) {
            Log.w(TAG, "observe: showRequestToSpeak $it")
        }

        viewModel.birthdayPodiumTopGifters.observe(viewLifecycleOwner) {
            Log.d(TAG, "observe: Top Gifters $it")
            topGiftersAdapter.setList(it)
            binding.topGiftersMultiStateView.viewState = if (it.isNullOrEmpty()) MultiStateView.ViewState.EMPTY  else MultiStateView.ViewState.CONTENT
        }
    }

    private fun onActiveSpeakerClick(speakerId: Int) {
        val action = PodiumLiveBirthdayFragmentDirections.actionPodiumLiveFragmentToPodiumSpeakerActionsBottomSheetFragment(userId = speakerId)
        findNavController().navigateSafe(direction = action)
    }

    private fun sendGiftToManager() {
        val manager = viewModel.mainScreenComposeSpeaker.value?.speaker ?: return
        if (!viewModel.isManager(manager.id)) return
        checkGiftEligibility(receiver = manager) {
            val action = PodiumSpeakerActionsBottomSheetFragmentDirections.actionGlobalGiftFragment(
                receiverId = manager.id,
                giftContext =  GiftContext.GIFT_PODIUM,
                managerId = manager.id,
                birthday = true,
                giftContextId = viewModel.podiumId.value
            )
            findNavController().navigateSafe(direction = action)
        }
    }

    private fun initTopGifterAdapter() {
        topGiftersAdapter =
            PodiumLiveBirthdayTopGiftersQuickAdapter(data = mutableListOf())
                .apply {
                    animationEnable = false
                    isAnimationFirstOnly = false
                    setDiffCallback(PodiumLiveBirthdayTopGiftersQuickAdapter.DiffCallback())
                }

        binding.topGifters.apply {
            layoutManager = LinearLayoutManager(requireContext(), RecyclerView.VERTICAL, false)
            adapter = topGiftersAdapter
        }
    }

    private fun showChargesBottomSheet() {
        if (viewModel.canShowTheaterCharges()) {
            viewModel.podium.value?.kind?.let {
                findNavController().navigateSafe(direction = PodiumLiveBirthdayFragmentDirections.actionGlobalPodiumChargesBottomSheetFragment(
                    podiumId = args.podiumId, chargeType = TheaterCharge.SPEAKER, podiumKind = it
                ))
            }
        } else {
            showGiftDetailAlertToOtherUsers()
        }
    }

    private fun showGiftDetailAlertToOtherUsers() {
        showFlashatDialog {
            setMessage(message = R.string.podium_birthday_gift_info)
        }
    }
}