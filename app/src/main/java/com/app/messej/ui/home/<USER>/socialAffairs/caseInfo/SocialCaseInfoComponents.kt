package com.app.messej.ui.home.publictab.socialAffairs.caseInfo

import android.util.Log
import androidx.annotation.StringRes
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.window.Dialog
import androidx.media3.common.MediaItem
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.app.messej.R
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.socialAffairs.CommitteeMembersResponse
import com.app.messej.data.model.api.socialAffairs.SocialCaseInfo
import com.app.messej.data.model.api.socialAffairs.SocialCaseInfo.Companion.dummySocialCaseInfo
import com.app.messej.data.model.api.socialAffairs.SocialCaseInfo.Companion.dummySocialProofFile
import com.app.messej.data.model.api.socialAffairs.SocialUserStatus
import com.app.messej.data.model.api.socialAffairs.SocialUserStatus.Companion.testSocialUserStatus
import com.app.messej.data.model.enums.MediaType
import com.app.messej.ui.composeComponents.FlashatComposeTypography
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomHorizontalSpacer
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomVerticalSpacer
import com.app.messej.ui.home.publictab.socialAffairs.activeCases.SocialCaseStatusText
import com.app.messej.ui.home.publictab.socialAffairs.committee.SocialCommitteeMemberSingleItem
import com.app.messej.ui.home.publictab.socialAffairs.components.SocialUserAndDonationView
import com.app.messej.ui.home.publictab.socialAffairs.components.SocialVoteActionStatusView
import com.app.messej.ui.utils.DataFormatHelper.formatDecimalWithRemoveTrailingZeros
import com.app.messej.ui.utils.EnumUtils.displayText

@Composable
fun SocialCaseInfoUserProofFileView(
    caseInfo: SocialCaseInfo?,
    user: CurrentUser? = null,
    onUserDpClick: (Int) -> Unit
) {
    Column(
        modifier = Modifier
            .clip(shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing)))
            .background(color = colorResource(id = R.color.colorSurfaceSecondary))
            .padding(all = 12.dp)
            .fillMaxWidth()
    ) {
        //Supported or Opposed Status Visible here
        if (caseInfo?.isVoteActionStatusVisible == true) {
            SocialVoteActionStatusView(
                modifier = Modifier.align(alignment = Alignment.End),
                voteStatus = caseInfo.voteStatus
            )
        }
        //All Case status -> visible to self case.
        if (caseInfo?.isCaseStatusVisible(currentUserId = user?.id) == true) {
            SocialCaseStatusText(
                modifier = Modifier.align(alignment = Alignment.End),
                status = caseInfo.status
            )
        }
        //Only Pending and closed status visible to president user
        if (caseInfo?.isCaseStatusVisibleToPresident(user = user) == true) {
            SocialCaseStatusText(
                modifier = Modifier.align(alignment = Alignment.End),
                status = caseInfo.status
            )
        }
        Text(
            text = caseInfo?.caseTitle ?: "",
            modifier = Modifier.fillMaxWidth(),
            color = colorResource(id = R.color.textColorPrimary),
            style = FlashatComposeTypography.defaultType.subtitle1
        )
        CustomVerticalSpacer(
            space = dimensionResource(id = R.dimen.activity_margin)
        )
        SocialUserAndDonationView(
            modifier = Modifier.fillMaxWidth(fraction = 0.7F),
            case = caseInfo,
            onUserDpClick = onUserDpClick
        )
        caseInfo?.caseDescription?.let {
            CustomVerticalSpacer(
                space = dimensionResource(id = R.dimen.activity_margin)
            )
            Text(
                text = stringResource(id = R.string.common_details),
                color = colorResource(id = R.color.textColorPrimary),
                style = FlashatComposeTypography.defaultType.subtitle2
            )
            CustomVerticalSpacer(
                space = dimensionResource(id = R.dimen.element_spacing)
            )
            Text(
                text = caseInfo.caseDescription,
                modifier = Modifier.fillMaxWidth(),
                color = colorResource(id = R.color.textColorPrimary),
                style = FlashatComposeTypography.defaultType.overline
            )
        }
        caseInfo?.socialProofFile?.let {
            CustomVerticalSpacer(
                space = dimensionResource(id = R.dimen.activity_margin)
            )
            WatchProofFilesView(
                proofFiles = it
            )
        }
    }
}

@Composable
fun WatchProofFilesView(
    proofFiles: List<SocialCaseInfo.SocialProofFile>
) {
    var isExpanded by remember { mutableStateOf(value = false) }
    val rotateDegree by animateFloatAsState(
        targetValue = if (!isExpanded) 180F else 0F
    )
    var previewItemPosition by remember { mutableStateOf<Int?>(value = null) }

    if (previewItemPosition != null) {
        WatchProofAlertDialog(
            proofs = proofFiles,
            initialPage = previewItemPosition ?: 0,
            onDismiss = {
                previewItemPosition = null
            }
        )
    }
    Column(
        modifier = Modifier
            .clip(shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing)))
            .background(color = colorResource(id = R.color.colorSurface))
            .padding(horizontal = 12.dp)
            .fillMaxWidth()
            .animateContentSize()
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = stringResource(id = R.string.social_watch_proof_files),
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(weight = 1F),
                color = colorResource(id = R.color.colorPrimary),
                style = FlashatComposeTypography.defaultType.subtitle2
            )
            IconButton(
                onClick = { isExpanded = !isExpanded }
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_business_down_arrow),
                    modifier = Modifier.rotate(degrees = rotateDegree),
                    tint = colorResource(id = R.color.colorPrimary),
                    contentDescription = null
                )
            }
        }
        if (isExpanded) {
            FlowRow(
                modifier = Modifier
                    .padding(bottom = 12.dp)
                    .fillMaxWidth(),
                maxItemsInEachRow = 2,
                horizontalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.activity_margin)),
                verticalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.activity_margin))
            ) {
                repeat(times = proofFiles.size) { position ->
                    val proof = proofFiles[position]

                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .clickable { previewItemPosition = position }
                            .fillMaxWidth()
                            .weight(weight = 1F)
                    ) {
                        if (proof.fileType == MediaType.VIDEO) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_business_video),
                                modifier = Modifier.size(size = dimensionResource(id = R.dimen.double_margin)),
                                tint = colorResource(id = R.color.colorPrimary),
                                contentDescription = null
                            )
                        } else {
                            AsyncImage(
                                model = ImageRequest
                                    .Builder(context = LocalContext.current)
                                    .crossfade(enable = true)
                                    .data(data = proof.url)
                                    .build(),
                                placeholder = painterResource(id = R.drawable.im_chat_image_placeholder),
                                error = painterResource(id = R.drawable.im_chat_image_placeholder),
                                contentDescription = null,
                                contentScale = ContentScale.Crop,
                                modifier = Modifier
                                    .size(size = dimensionResource(id = R.dimen.double_margin))
                                    .clip(shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.line_spacing)))
                                    .border(
                                        width = 1.dp, shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.line_spacing)), color = colorResource(id = R.color.colorSocialSurfaceSecondaryLight)
                                    )
                            )
                        }
                        CustomHorizontalSpacer(
                            space = dimensionResource(id = R.dimen.element_spacing)
                        )
                        Text(
                            text = stringResource(id = R.string.social_affairs_proof_file, "${position + 1}"),
                            modifier = Modifier.fillMaxWidth(),
                            color = colorResource(id = R.color.colorPrimary),
                            style = FlashatComposeTypography.overLineBold
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun WatchProofAlertDialog(
    proofs: List<SocialCaseInfo.SocialProofFile>,
    initialPage: Int = 0,
    onDismiss: () -> Unit
) {
    val state = rememberPagerState(initialPage = initialPage) { proofs.size }
    Dialog(
        onDismissRequest = onDismiss
    ) {
        Column(
            modifier = Modifier
                .padding(vertical = dimensionResource(id = R.dimen.activity_margin))
                .fillMaxWidth()
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(id = R.string.social_preview_image),
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(weight = 1F),
                    color = colorResource(id = R.color.white),
                    style = FlashatComposeTypography.defaultType.subtitle1
                )
                IconButton(
                    onClick = onDismiss
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_close_flash),
                        modifier = Modifier.size(size = dimensionResource(id = R.dimen.extra_margin)),
                        contentDescription = null,
                        tint = Color.Unspecified
                    )
                }
            }
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(color = colorResource(id = R.color.colorSocialSurfaceSecondary))
                    .padding(bottom = dimensionResource(id = R.dimen.activity_margin)),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(id = R.string.case_detail_watch_proof),
                    modifier = Modifier
                        .padding(top = dimensionResource(id = R.dimen.activity_margin))
                        .padding(horizontal = dimensionResource(id = R.dimen.activity_margin))
                        .fillMaxWidth(),
                    color = colorResource(id = R.color.textColorPrimary),
                    style = FlashatComposeTypography.overLineBold
                )
                HorizontalPager(
                    state = state,
                    pageSpacing = dimensionResource(id = R.dimen.activity_margin),
                    contentPadding = PaddingValues(all = dimensionResource(id = R.dimen.activity_margin)),
                    modifier = Modifier
                        .clip(shape = RoundedCornerShape(
                            size = dimensionResource(id = R.dimen.line_spacing))
                        )
                ) { position ->
                    val proof = proofs[position]
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Box(
                            modifier = Modifier
                                .aspectRatio(ratio = 0.9F)
                                .clip(shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.line_spacing)))
                                .border(width = 1.dp, shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.line_spacing)), color = colorResource(id = R.color.colorSocialSurfaceSecondaryLight)),
                            contentAlignment = Alignment.Center
                        ) {
                            when(proof.fileType) {
                                MediaType.IMAGE -> {
                                    AsyncImage(
                                        model = ImageRequest
                                            .Builder(context = LocalContext.current)
                                            .crossfade(enable = true)
                                            .data(data = proof.url)
                                            .build(),
                                        placeholder = painterResource(id = R.drawable.im_chat_image_placeholder),
                                        error = painterResource(id = R.drawable.im_chat_image_placeholder),
                                        contentDescription = null,
                                        contentScale = ContentScale.Inside
                                    )
                                }
                                MediaType.VIDEO -> {
                                    SocialExoPlayerView(
                                        url = proof.url
                                    )
                                }
                                else -> {}
                            }
                        }
                    }
                }
                if (proofs.size > 1){
                    Text(
                        text = "${state.currentPage + 1} / ${proofs.size}",
                        color = colorResource(id = R.color.textColorPrimary),
                        style = FlashatComposeTypography.defaultType.overline
                    )
                }
            }
        }
    }
}

@Composable
fun CaseInfoCommitteeMembersView(
    committeeMembers : List<CommitteeMembersResponse.CommitteeMemberUserDetail>,
    onUserDpClick: (userId: Int) -> Unit
) {
    Column(
        modifier = Modifier
            .clip(shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing)))
            .background(color = colorResource(id = R.color.colorSurfaceSecondary))
            .padding(all = 12.dp)
            .fillMaxWidth()
    ) {
        Text(
            text = stringResource(id = R.string.social_committee_members),
            modifier = Modifier.fillMaxWidth(),
            color = colorResource(id = R.color.textColorPrimary),
            style = FlashatComposeTypography.defaultType.subtitle1
        )
        CustomVerticalSpacer(
            space = dimensionResource(id = R.dimen.element_spacing)
        )
        Column(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.line_spacing))
        ) { 
            repeat(times = committeeMembers.take(n = 4).size) { position ->
                val item = committeeMembers[position]
                SocialCommitteeMemberSingleItem(
                    item = item,
                    backgroundColor = colorResource(id = R.color.colorSurface),
                    onUserDpClick = { item.userDetail?.id?.let { onUserDpClick(it) } }
                )
            }
        }
    }
}

@Composable
fun CaseInfoCardWithCount(
    @StringRes title: Int,
    @StringRes description: Int,
    count: Int?,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .clip(shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing)))
            .clickable { onClick() }
            .background(color = colorResource(id = R.color.colorSurfaceSecondary))
            .padding(all = 12.dp)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .weight(weight = 1F)
        ) {
            Text(
                text = stringResource(id = title),
                style = FlashatComposeTypography.defaultType.subtitle2,
                color = colorResource(id = R.color.textColorPrimary)
            )
            Text(
                text = buildAnnotatedString {
                    withStyle(style = SpanStyle(fontSize = 14.sp, color = colorResource(id = R.color.colorPrimary))) { append("${count ?: 0} ") }
                    append(stringResource(id = description))
                },
                style = FlashatComposeTypography.defaultType.overline,
                color = colorResource(id = R.color.textColorPrimary)
            )
        }
        Icon(
            painter = painterResource(id = R.drawable.ic_caret_right),
            tint = colorResource(id = R.color.textColorPrimary),
            contentDescription = null
        )
    }
}

@Composable
fun CaseInfoUpgradeSupportView(
    userStatus: SocialUserStatus?
) {
    Column(
        modifier = Modifier
            .clip(shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing)))
            .background(color = colorResource(id = R.color.colorSurfaceSecondary))
            .padding(all = 12.dp)
            .fillMaxWidth()
    ) {
        Text(
            text = stringResource(id = R.string.social_case_info_current_status),
            style = FlashatComposeTypography.defaultType.subtitle2,
            color = colorResource(id = R.color.textColorPrimary)
        )
        CustomVerticalSpacer(
            space = dimensionResource(id = R.dimen.element_spacing)
        )
        Row(
            modifier = Modifier.fillMaxWidth()
        ) {
            userStatus?.citizenship?.displayText()?.let {
                TextDescriptionView(
                    modifier = Modifier.fillMaxWidth().weight(weight = 1F),
                    title = R.string.social_case_info_user_level,
                    description = stringResource(id = it)
                )
                CustomHorizontalSpacer(
                    space = dimensionResource(id = R.dimen.element_spacing)
                )
            }
            TextDescriptionView(
                modifier = Modifier.fillMaxWidth().weight(weight = 1F),
                title = R.string.social_case_info_performance_rating,
                description = stringResource(id = R.string.common_percentage_value, userStatus?.userRating?.formatDecimalWithRemoveTrailingZeros() ?: "0")
            )
        }
        CustomVerticalSpacer(
            space = dimensionResource(id = R.dimen.element_spacing)
        )
        Row(
            modifier = Modifier.fillMaxWidth()
        ) {
            TextDescriptionView(
                modifier = Modifier.fillMaxWidth().weight(weight = 1F),
                title = R.string.social_case_info_flashat_age,
                description = "${userStatus?.flashatAge ?: 0}"
            )
            CustomHorizontalSpacer(
                space = dimensionResource(id = R.dimen.element_spacing)
            )
            TextDescriptionView(
                modifier = Modifier.fillMaxWidth().weight(weight = 1F),
                title = R.string.social_case_info_flix_balance,
                description = userStatus?.flixBalance?.formatDecimalWithRemoveTrailingZeros()
            )
            CustomHorizontalSpacer(
                space = dimensionResource(id = R.dimen.element_spacing)
            )
            TextDescriptionView(
                modifier = Modifier.fillMaxWidth().weight(weight = 1F),
                title = R.string.social_case_info_coin_balance,
                description = userStatus?.coinBalance?.formatDecimalWithRemoveTrailingZeros()
            )
        }
    }
}

@Composable
private fun SocialExoPlayerView(url: String) {
    val context = LocalContext.current

    val exoPlayer = remember {
        ExoPlayer.Builder(context).build().apply {
            setMediaItem(MediaItem.fromUri(url))
            prepare()
            playWhenReady = true
        }
    }

    DisposableEffect (key1 = Unit) {
        onDispose {
            exoPlayer.stop()
            exoPlayer.release()
            Log.d("SocialExoPlayer", "Release Player")
        }
    }

    AndroidView(
        factory = { ctx ->
            PlayerView(ctx).apply {
                player = exoPlayer
            }
        }, modifier = Modifier.fillMaxWidth()
    )
}

@Preview
@Composable
private fun CaseInfoUpgradeUserView() {
    CaseInfoUpgradeSupportView(
        userStatus = testSocialUserStatus
    )
}

@Preview
@Composable
fun CaseInfoThumbsUpDownDescription() {
    Column(
        modifier = Modifier
            .clip(shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing)))
            .background(color = colorResource(id = R.color.colorSurfaceSecondary))
            .padding(all = 12.dp)
            .fillMaxWidth()
    ) {
        Text(
            text = stringResource(id = R.string.common_note),
            style = FlashatComposeTypography.defaultType.subtitle2,
            color = colorResource(id = R.color.textColorPrimary)
        )
        CustomVerticalSpacer(
            space = dimensionResource(id = R.dimen.line_spacing)
        )
        Text(
            text = stringResource(id = R.string.social_support_confirmation_message),
            modifier = Modifier.fillMaxWidth(),
            style = FlashatComposeTypography.defaultType.overline,
            color = colorResource(id = R.color.textColorPrimary)
        )
    }
}

@Composable
private fun TextDescriptionView(
    modifier: Modifier,
    @StringRes title: Int,
    description: String?
) {
    Column(
        modifier = modifier
            .clip(shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing)))
            .background(color = colorResource(id = R.color.colorSurface))
            .padding(all = dimensionResource(id = R.dimen.element_spacing))
            .fillMaxWidth()
    ) {
        Text(
            text = stringResource(id = title),
            style = FlashatComposeTypography.defaultType.overline,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            color = colorResource(id = R.color.textColorPrimary)
        )
        Text(
            text = description ?: "",
            style = FlashatComposeTypography.defaultType.subtitle1,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            color = colorResource(id = R.color.textColorPrimary)
        )
    }
}

@Preview
@Composable
private fun CaseInfoCardWithCountPreview() {
    CaseInfoCardWithCount(
        title = R.string.social_view_voters_list,
        description = R.string.social_voters,
        count = 10,
        onClick = {}
    )
}

@Preview
@Composable
private fun WatchProofAlertDialogPreview() {
    WatchProofAlertDialog(
        proofs = dummySocialProofFile,
        onDismiss = {}
    )
}


@Preview
@Composable
private fun SocialCaseInfoUserProofFilePreview() {
    SocialCaseInfoUserProofFileView(
        caseInfo = dummySocialCaseInfo,
        onUserDpClick = {}
    )
}

@Preview
@Composable
private fun SocialCaseWatchProofPreview() {
    WatchProofFilesView(
        proofFiles = dummySocialProofFile
    )
}