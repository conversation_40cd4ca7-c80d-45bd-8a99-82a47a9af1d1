package com.app.messej.ui.premium

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.core.view.MenuHost
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.android.billingclient.api.BillingClient
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.databinding.FragmentSubscribeBinding
import com.app.messej.databinding.LayoutWelcomeSubscriptionDialogBinding
import com.app.messej.ui.utils.DateFormatHelper
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class AlreadySubscribedFragment : Fragment(), MenuProvider {

    private lateinit var binding: FragmentSubscribeBinding
    private val viewModel: AlreadySubscribedViewModel by viewModels()
    private val navArgs:AlreadySubscribedFragmentArgs by navArgs()


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_subscribe, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel=viewModel
        return binding.root
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.setShowWelcomePopUp(navArgs.isShowWelcomePopup)
        requireActivity().onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                isEnabled = false
//                requireActivity().onBackPressedDispatcher.onBackPressed()
                if (navArgs.isShowWelcomePopup) {
                    findNavController().navigateSafe(
                        NavGraphHomeDirections.actionGlobalHomeFragment()
                    )
                }
                else{
                    findNavController().popBackStack()
                }
            }
        })
    }

    override fun onStart() {
        super.onStart()
        viewModel.getSubsctriptionDetails()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolBarTitle.text = getString(R.string.settings_title_subscription)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        activity?.actionBar?.setDisplayHomeAsUpEnabled(true)
        activity?.actionBar?.setHomeButtonEnabled(true)
        binding.customActionBar.toolbar.setNavigationOnClickListener {
            if (navArgs.isShowWelcomePopup) {
                findNavController().navigateSafe(
                    NavGraphHomeDirections.actionGlobalHomeFragment()
                )
            } else {
                findNavController().popBackStack()
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val menuHost: MenuHost = requireActivity()
        menuHost.addMenuProvider(this, viewLifecycleOwner, Lifecycle.State.RESUMED)
        observe()
        setUp()
        viewModel.fetchUserLevelUpgradeDetails()
        viewModel.userLevelUpgradeDetails.observe(viewLifecycleOwner) { details ->
            binding.flixUpgradeComposeView.setContent {
                FlixUpgradeOverviewCard(details)
            }
        }
    }

    private fun setUp() {
        binding.createPasswordNextButton.setOnClickListener {
            findNavController().navigateSafe(AlreadySubscribedFragmentDirections.actionGlobalUpgradePremiumFragment())
        }
        binding.createPasswordNextButtonAutoRenewal.setOnClickListener {
            findNavController().navigateSafe(AlreadySubscribedFragmentDirections.actionGlobalUpgradePremiumFragment())
        }

        binding.actionSwitch.setOnCheckedChangeListener{_, isChecked ->
                viewModel.handleCountryFlagUpdate(isChecked)
        }
    }

    private fun observe() {
        viewModel.isActive.observe(viewLifecycleOwner){
            it?.let {
                binding.createPasswordNextButton.visibility = if (it) View.GONE else View.VISIBLE
                binding.txtPremiumSubscriptionStatus.text = if (it) getString(R.string.common_active) else getString(R.string.common_expired)
                binding.cardRenewalFlix.visibility = if (viewModel.currency.value == "FLiX") View.VISIBLE else View.GONE
            }
        }

        viewModel.expiry.observe(viewLifecycleOwner){
            it?.let {
                val period=DateFormatHelper.getPeriod(it)
                when {
                    period.isNegative ->  binding.txtExpiry.text = requireContext().getString(R.string.title_exprired)
                    period.toTotalMonths() > 1 ->
                        binding.txtExpiry.text = requireContext().getString(R.string.title_exprired_month,period.toTotalMonths(),period.days)
                    else ->   binding.txtExpiry.text =requireContext().getString(R.string.title_exprired_days,period.days)
                }

                Log.d("ExpiryPEnd","${DateFormatHelper.getRemainingLocalDate(it)}")

                val remainingDays = DateTimeUtils.durationFromNowToFuture(DateFormatHelper.getRemainingLocalDate(it))?.toDays()

                Log.d("ExpiryPEnd"," days${remainingDays}")
                if (remainingDays != null) {
                    if(remainingDays <= 30) binding.createPasswordNextButtonAutoRenewal.visibility =View.VISIBLE else binding.createPasswordNextButtonAutoRenewal.visibility =View.GONE
                }
            }
        }
        viewModel.isShowWelcomeMessage.observe(viewLifecycleOwner){
            it?.let {
                if(it) {
                    MaterialDialog(requireContext()).show {
                            val view = DataBindingUtil.inflate<LayoutWelcomeSubscriptionDialogBinding>(layoutInflater, R.layout.layout_welcome_subscription_dialog, null, false)
                            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
                            cancelable(true)
                        view.subscriptionDialogClose.setOnClickListener {
                            dismiss()
                        }
                    }
                }

            }

        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            android.R.id.home -> {
                findNavController().popBackStack()
            }
        }
        return true
    }
    @Composable
    fun FlixUpgradeOverviewCard(details: com.app.messej.data.model.api.UserLevelUpgradeDetails?) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = "Upgrade with FliX Overview",
                style = MaterialTheme.typography.titleLarge
            )
            if (details != null) {
                Text(
                    text = "You have upgraded with FliX to ${details.userLevel ?: "-"}. Your status: ${details.userLevelUpgradeStatus ?: "-"}. Expiry: ${details.userLevelExpirationDate ?: "-"}",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                Card(
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
                    colors = CardDefaults.cardColors(containerColor = colorResource(R.color.colorSurfaceSecondaryDark))
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        OverviewRow("Status:", details.userLevelUpgradeStatus ?: "-", if ((details.userLevelUpgradeStatus ?: "").equals("Active", true)) Color.Green else Color.Red)
                        Divider(modifier = Modifier.padding(vertical = 8.dp))
                        OverviewRow("Upgraded Level:", details.userLevel ?: "-")
                        Divider(modifier = Modifier.padding(vertical = 8.dp))
                        OverviewRow("Expires on:", details.userLevelExpirationDate ?: "-")
                    }
                }
            } else {
                Text(
                    text = "No upgrade details available.",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
            }
        }
    }

    @Composable
    fun OverviewRow(label: String, value: String, valueColor: Color = MaterialTheme.colorScheme.onSurface) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(text = label, style = MaterialTheme.typography.bodyLarge)
            Text(text = value, style = MaterialTheme.typography.bodyLarge, color = valueColor, fontWeight = FontWeight.Bold)
        }
    }

}