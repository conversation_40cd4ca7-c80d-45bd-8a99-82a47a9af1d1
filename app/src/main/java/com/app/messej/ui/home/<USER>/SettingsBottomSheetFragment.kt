package com.app.messej.ui.home.settings

import android.app.UiModeManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.BuildConfig
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.Constants
import com.app.messej.data.model.enums.NightModeSetting
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserSubscriptionStatus
import com.app.messej.data.room.FlashatDatabase
import com.app.messej.data.utils.LogCatReader
import com.app.messej.databinding.FragmentSettingsBottomSheetBinding
import com.app.messej.databinding.LayoutCustomProgressDialogBinding
import com.app.messej.databinding.LayoutDaynightSwitcherDialogBinding
import com.app.messej.ui.home.businesstab.HomeBusinessFragment.Companion.TAB_STATEMENT
import com.app.messej.ui.utils.DrawerHeaderHolder
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.dialog.MaterialAlertDialogBuilder

/**
 * A simple [Fragment] subclass.
 * Use the [SettingsBottomSheetFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class SettingsBottomSheetFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentSettingsBottomSheetBinding
    private lateinit var drawerHeader: DrawerHeaderHolder
    private val viewModel: SettingsBottomSheetViewModel by activityViewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL,R.style.Widget_Flashat_SettingsBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_settings_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        binding.version = BuildConfig.VERSION_NAME

        return binding.root
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = true
        setup()
        setupNavigation()
        observe()
    }
    private var logoutLoader: MaterialDialog? = null

    private fun observe() {

        viewModel.appSettings.observe(this){
            it?: return@observe
        }
        viewModel.userAccount.observe(this) {
            Log.d("USERPROFILE", "observe: ${it?.profile}")
            drawerHeader.profile = it?.profile
        }
        viewModel.accountDetails.observe(this) { acc ->
            Log.d("USERPROFILE", "Acc detail: ${acc}")
            drawerHeader.account =  acc
        }
        viewModel.onSwitchNightMode.observe(this) {
            setDayNightMode(it)
            //For OS version below 12, Activity should be recreated to apply dark mode theme
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
                activity?.recreate()
            }
        }
        viewModel.loggingOut.observe(viewLifecycleOwner){
            if (it) {
                logoutLoader = MaterialDialog(requireContext()).show {
                    val view = DataBindingUtil.inflate<LayoutCustomProgressDialogBinding>(layoutInflater, R.layout.layout_custom_progress_dialog, null, false)
                    view.text = getString(R.string.logout_progress_message_title)
                    customView(null, view.root, dialogWrapContent = true)
                    cancelable(false)
                }
            } else {
                logoutLoader?.dismiss()
                logoutLoader = null
            }
        }
        viewModel.onLoggedOut.observe(viewLifecycleOwner){
            Toast.makeText(requireContext(), R.string.logout_toast_message, Toast.LENGTH_SHORT).show()
        }

        viewModel.logCapture.observe(viewLifecycleOwner) {
            binding.debugLogToggle.title = LogCatReader.enableActionString
        }
        viewModel.isActive.observe(viewLifecycleOwner){
            Log.d("USERPROFILE", "isActive: ${it}")
        }
    }

    private fun setup() {
        viewModel.getUserCitizenship()
        //Hiding profile button as per new requirement. and this is moved to account management screen
        // If need to show here, remove below visibility line.
        binding.profile.root.isVisible = false
        binding.profile.ivItem.setImageResource(R.drawable.ic_profile)
        binding.profile.clickableLayout.setOnClickListener {
         onProfileClick()
        }

        binding.account.apply {
            ivItem.setImageResource(R.drawable.im_user_placeholder)
            clickableLayout.setOnClickListener {
                onAccountItemClick()
            }
        }

        binding.idCard.ivItem.setImageResource(R.drawable.ic_id_card)
        binding.idCard.clickableLayout.setOnClickListener {
            onIdCardClick()
        }

        binding.gift.ivItem.setImageResource(R.drawable.ic_gift)
        binding.gift.clickableLayout.setOnClickListener {
            onGiftClick()
        }

        binding.deals.ivItem.setImageResource(R.drawable.ic_settings_deals)
        binding.deals.clickableLayout.setOnClickListener {
            onDealsClick()
        }
        binding.statusEligibility.ivItem.setImageResource(R.drawable.ic_business_tick)
        binding.statusEligibility.clickableLayout.setOnClickListener {
            onStatusEligibilityClick()
        }

        binding.authorities.ivItem.setImageResource(R.drawable.ic_authorities)
        binding.authorities.clickableLayout.setOnClickListener {
            onAuthoritiesItemClick()
        }

        binding.inviteFriend.ivItem.setImageResource(R.drawable.ic_share_filled)
        binding.inviteFriend.clickableLayout.setOnClickListener {
            shareReferralLink()
//            viewModel.countAppShare()
        }

        binding.darkMode.ivItem.setImageResource(R.drawable.ic_dark_mode)
        binding.darkMode.clickableLayout.setOnClickListener {
            showDayNightSwitchDialog()
        }

        binding.privacy.ivItem.setImageResource(R.drawable.ic_privacy)
        binding.privacy.clickableLayout.setOnClickListener {
            onPrivacyClicked()
        }

        binding.notifications.ivItem.setImageResource(R.drawable.ic_notifications)
        binding.notifications.clickableLayout.setOnClickListener {
            onActionNotificationClicked()
        }

        binding.flashLanguages.ivItem.setImageResource(R.drawable.ic_flashat_languages)
        binding.flashLanguages.clickableLayout.setOnClickListener {
            onActionLanguageClicked()
        }

        binding.announcementLanguages.ivItem.setImageResource(R.drawable.ic_annnouncement)
        binding.announcementLanguages.clickableLayout.setOnClickListener {
            onActionLanguageClicked()
        }

        binding.empowerments.ivItem.setImageResource(R.drawable.ic_empowerments)
        binding.empowerments.clickableLayout.setOnClickListener {
            onActionEmpowermentsClicked()
        }

        binding.security.ivItem.setImageResource(R.drawable.ic_security)
        binding.security.clickableLayout.setOnClickListener {
            onActionSecurityClicked()
        }

        binding.accountHelp.ivItem.setImageResource(R.drawable.ic_account)
        binding.accountHelp.clickableLayout.setOnClickListener {
            onActionAccountHelp()
        }

        binding.actionLogout.setOnClickListener {
            showLogoutAlertDialog()
        }

        if (BuildConfig.BUILD_TYPE != Constants.BuildType.RELEASE.value) {
            binding.debugItems.isVisible = true
            binding.debugRoomExplore.clickableLayout.setOnClickListener {
                val file = FlashatDatabase.export(requireContext())
                Log.w("RUMEXP", "Backed Up DB: $file")
                Toast.makeText(context, file, Toast.LENGTH_LONG).show()
            }
            binding.debugLogToggle.clickableLayout.setOnClickListener {
                LogCatReader.enableLogging(true)
                findNavController().popBackStack()
            }
        } else binding.debugItems.isVisible = false
        // Initialize the dark mode setting
    }

    private fun onStatusEligibilityClick() {
        findNavController().navigateSafe(SettingsBottomSheetFragmentDirections.actionSettingsBottomSheetFragmentToBusinessWorkStatusStandAloneFragment())
    }

    private fun onAccountItemClick() {
        findNavController().navigateSafe(
            SettingsBottomSheetFragmentDirections.actionSettingsBottomSheetFragmentToAccountManagementFragment()
        )
    }

    private fun onAuthoritiesItemClick() {
        findNavController().navigateSafe(
            SettingsBottomSheetFragmentDirections.actionSettingsBottomSheetFragmentToAuthoritiesStandAloneFragment()
        )
    }

    private fun onDealsClick() {
        if(viewModel.user?.citizenship == UserCitizenship.VISITOR) {
            findNavController().navigateSafe(SettingsBottomSheetFragmentDirections.actionSettingsBottomSheetFragmentToDealsFragment(isVisitor = true, isResident = false))
        } else {
            findNavController().navigateSafe(SettingsBottomSheetFragmentDirections.actionSettingsBottomSheetFragmentToDealsFragment(isVisitor = false, isResident = true))
        }
    }

    private fun onIdCardClick() {
        val action = SettingsBottomSheetFragmentDirections.actionGlobalPublicUserProfileFragment(viewModel.user?.id?: return, false, isSelf = true)
        findNavController().navigateSafe(action,navOptions = NavOptions.Builder()
            .build())
    }

    private var dayNightDialog: MaterialDialog? = null

    private fun showDayNightSwitchDialog() {
        val current = viewModel.appSettings.value?.appNightMode?:NightModeSetting.FOLLOW_SYSTEM
        Log.w("DNMX", "showDayNightSwitchDialog: current $current")
        dayNightDialog = MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutDaynightSwitcherDialogBinding>(layoutInflater, R.layout.layout_daynight_switcher_dialog, null, false)
            when(current) {
                NightModeSetting.FOLLOW_SYSTEM -> view.radioDayNightDefault.isChecked = true
                NightModeSetting.FORCE_LIGHT -> view.radioDay.isChecked = true
                NightModeSetting.FORCE_DARK -> view.radioNight.isChecked = true
            }
            view.cancelButton.setOnClickListener {
                dayNightDialog?.dismiss()
                dayNightDialog = null
            }
            view.okButton.setOnClickListener {
                val newMode = when(view.radioDayNight.checkedRadioButtonId) {
                    R.id.radioDayNightDefault -> NightModeSetting.FOLLOW_SYSTEM
                    R.id.radioDay -> NightModeSetting.FORCE_LIGHT
                    R.id.radioNight -> NightModeSetting.FORCE_DARK
                    else -> null
                }
                Log.w("DNMX", "showDayNightSwitchDialog: saving $newMode")
                newMode?.let {
                    viewModel.saveNightModeSetting(it)
                }
                dayNightDialog?.dismiss()
                dayNightDialog = null
            }
            customView(null, view.root, dialogWrapContent = true, horizontalPadding = false, noVerticalPadding = true)
            cancelable(true)
            cornerRadius(16f)
        }
    }

    private fun setDayNightMode(night: NightModeSetting) {
        val uiModeManager = requireActivity().getSystemService(Context.UI_MODE_SERVICE) as UiModeManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val mode = when (night) {
                NightModeSetting.FOLLOW_SYSTEM -> UiModeManager.MODE_NIGHT_AUTO
                NightModeSetting.FORCE_LIGHT -> UiModeManager.MODE_NIGHT_NO
                NightModeSetting.FORCE_DARK -> UiModeManager.MODE_NIGHT_YES
            }
            uiModeManager.nightMode
            uiModeManager.setApplicationNightMode(mode)
        }
        val mode = when (night) {
            NightModeSetting.FOLLOW_SYSTEM -> AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM
            NightModeSetting.FORCE_LIGHT -> AppCompatDelegate.MODE_NIGHT_NO
            NightModeSetting.FORCE_DARK -> AppCompatDelegate.MODE_NIGHT_YES
        }
        AppCompatDelegate.setDefaultNightMode(mode)
    }

    private fun setupNavigation() {
        Log.d("UpgradeH","${viewModel.user}")
        Log.d("UpgradeH"," premium ${viewModel.isPremiumUser}")
        drawerHeader = if (viewModel.user?.premium == true) {
            DrawerHeaderHolder.PremiumHeader(
                DataBindingUtil.inflate(layoutInflater, R.layout.layout_settings_bottom_sheet_header_premium, binding.headerHolder, false),
                object : DrawerHeaderHolder.PremiumHeader.ClickListener {

                    override fun toMyETribe() {
                        findNavController().navigateSafe(
                            SettingsBottomSheetFragmentDirections.actionGlobalMyETribeFragment()
                        )
                    }

                    override fun toProfile() {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalProfileFragment())
                    }

                    override fun dismissDialog() {
                        dialog?.dismiss()
                    }

                    override fun toFollowers() {
                        findNavController().navigateSafe(SettingsBottomSheetFragmentDirections.actionSettingsBottomSheetFragmentToPublicBroadcastStandaloneFragment())
                    }

                    override fun toFollowing() {
                        findNavController().navigateSafe(SettingsBottomSheetFragmentDirections.actionSettingsBottomSheetFragmentToPublicStarsStandaloneFragment())
                    }

                    override fun toStatements() {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalHomeBusinessFragment(destination = TAB_STATEMENT))
                    }
                }, citizenship = viewModel.citizenship, user = viewModel.user
            )
        } else {
            DrawerHeaderHolder.FreeHeader(
                DataBindingUtil.inflate(layoutInflater, R.layout.layout_settings_bottom_sheet_header_free, binding.headerHolder, false),
                object : DrawerHeaderHolder.FreeHeader.ClickListener {
                    override fun toProfile() {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalProfileFragment())
                    }

                    override fun toUpgrade() {
                        upgradeToPremium()
                    }

                    override fun dismissDialog() {
                        dialog?.dismiss()
                    }

                    override fun toFollowers() {
                        findNavController().navigateSafe(SettingsBottomSheetFragmentDirections.actionSettingsBottomSheetFragmentToPublicBroadcastStandaloneFragment())
                    }

                    override fun toFollowing() {
                        findNavController().navigateSafe(SettingsBottomSheetFragmentDirections.actionSettingsBottomSheetFragmentToPublicStarsStandaloneFragment())
                    }

                    override fun toFlaxBalance() {
                        findNavController().navigateSafe(SettingsBottomSheetFragmentDirections.actionSettingsBottomSheetFragmentToBusinessStatementFreeFragment())
                    }
                }, citizenship = viewModel.citizenship,user = viewModel.user
            )
        }
        binding.headerHolder.addView(drawerHeader.binding.root)
    }
    private fun showLogoutAlertDialog() {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle(getString(R.string.logout_confirmation_message_title))
            .setMessage(getString(R.string.logout_confirmation_message))
            .setCancelable(false)
            .setPositiveButton(getString(R.string.common_ok)) { _, _ ->
                viewModel.logout()
            }
            .setNegativeButton(getString(R.string.common_cancel)) { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }

    private fun onProfileClick() {
        findNavController().navigateSafe(SettingsBottomSheetFragmentDirections.actionGlobalProfileFragment())
    }
    private fun shareReferralLink() {
        val user = viewModel.user?: return
        val sendIntent: Intent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(
                Intent.EXTRA_TEXT, getString(R.string.title_operations_share_message, user.username, user.profile.referralLink)
            )
            type = "text/plain"
        }
        val shareIntent = Intent.createChooser(sendIntent, null)
        startActivity(shareIntent)
    }
    private fun onPrivacyClicked() {
        findNavController().navigateSafe(SettingsBottomSheetFragmentDirections.actionSettingsBottomSheetFragmentToPrivacyFragment())
    }
    private fun onActionNotificationClicked() {
        findNavController().navigateSafe(SettingsBottomSheetFragmentDirections.actionSettingsBottomSheetFragmentToSettingsNotificationFragment())
    }
    private fun onGiftClick() {
        findNavController().navigateSafe(SettingsBottomSheetFragmentDirections.actionGlobalGiftFileFragment())
    }
    private fun onActionLanguageClicked() {
        findNavController().navigateSafe(SettingsBottomSheetFragmentDirections.actionSettingsBottomSheetFragmentToSettingsLanguagesFragment())
    }
    private fun onActionEmpowermentsClicked(){
        findNavController().navigateSafe(SettingsBottomSheetFragmentDirections.actionSettingsBottomSheetFragmentToEmpowermentListsFragment())
    }
    private fun onActionSecurityClicked() {
        findNavController().navigateSafe(SettingsBottomSheetFragmentDirections.actionSettingsBottomSheetFragmentToSettingsSecurityFragment())
    }
    private fun onActionAccountHelp() {
        findNavController().navigateSafe(SettingsBottomSheetFragmentDirections.actionSettingsBottomSheetFragmentToSettingsAboutFragment())
    }


    private fun upgradeToPremium() {
        if (viewModel.isActive.value == UserSubscriptionStatus.ACTIVE || viewModel.isActive.value == UserSubscriptionStatus.EXPIRED || viewModel.user?.premium == true) {
            findNavController().navigateSafe(
                SettingsBottomSheetFragmentDirections.actionGlobalAlreadySubscribedFragment(false)
            )
        } else {
            findNavController().navigateSafe(SettingsBottomSheetFragmentDirections.actionGlobalUpgradePremiumFragment())
        }
    }
}