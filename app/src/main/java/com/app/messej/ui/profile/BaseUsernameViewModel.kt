package com.app.messej.ui.profile
import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import com.app.messej.data.Constants
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.ProfileRepository
import com.hadilq.liveevent.LiveEvent

abstract class BaseUsernameViewModel(application: Application): AndroidViewModel(application) {

    protected var profileRepo: ProfileRepository = ProfileRepository(application)
    protected var accountRepo: AccountRepository = AccountRepository(application)
    val user: CurrentUser get() = accountRepo.user

    // CREATE USERNAME
    val username = MutableLiveData<String>()

    protected val _usernameAvailable = MutableLiveData<Boolean?>(null)
    val usernameAvailable: LiveData<Boolean?> = _usernameAvailable

    protected val _usernameLoading = MutableLiveData<Boolean>(false)
    val usernameLoading: LiveData<Boolean> = _usernameLoading

    protected val _usernameAvailabilityLoading = MutableLiveData(false)
    val usernameAvailabilityLoading: LiveData<Boolean> = _usernameAvailabilityLoading

    val ruleNoSpecialChars = MutableLiveData(Constants.InputValidationResult.NONE)
    val ruleNoSpace = MutableLiveData(Constants.InputValidationResult.NONE)
    val ruleTwoSpecialChars = MutableLiveData(Constants.InputValidationResult.NONE)
    val ruleHasLetter = MutableLiveData(Constants.InputValidationResult.NONE)
    val ruleOneAlphanumeric = MutableLiveData(Constants.InputValidationResult.NONE)
    val ruleCharacterCount = MutableLiveData(Constants.InputValidationResult.NONE)
    val ruleNoSpaceAndSpecialChars = MutableLiveData(Constants.InputValidationResult.NONE)

    private val _usernameValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>(false)
        med.addSource(username) { checkUsername() }
        med
    }
    val usernameValid: LiveData<Boolean> = _usernameValid
    val onSetUsernameComplete = LiveEvent<Boolean>()
    val onCheckUsernameComplete = LiveEvent<String?>()

    protected val _setUsernameError = MutableLiveData<String?>(null)
    val setUsernameError: LiveData<String?> = _setUsernameError

    protected val _checkUsernameError = MutableLiveData<String?>(null)
    val checkUsernameError: LiveData<String?> = _checkUsernameError

    protected fun checkUsername() {
        _usernameAvailable.postValue(null)
        val noSpecialPattern = Regex("^[ A-Za-z0-9_.]*\$")
        val noSpacePattern = Regex("^\\S*\$")
        val onAlphaNumPattern = Regex("^.*[a-zA-Z0-9][^a-zA-Z0-9]*\$")
        var flagNoSpecial = false
        var flagNoSpace = false
        var flagOneAlphaNum = false
        var flagMinMax = false
        var flagMax2SpecialCharsOk: Boolean
        var flagHasLetterOk: Boolean

        if (!username.value.isNullOrEmpty()) {
            val specialCharCount = username.value?.count { !it.isLetterOrDigit() }
            flagMax2SpecialCharsOk = (specialCharCount ?: 0) <= 2
            if (flagMax2SpecialCharsOk) {
                ruleTwoSpecialChars.postValue(Constants.InputValidationResult.PASS)
            } else ruleTwoSpecialChars.postValue(Constants.InputValidationResult.FAIL)
            if (noSpecialPattern.containsMatchIn(username.value!!)) {
                flagNoSpecial = true
                ruleNoSpecialChars.postValue(Constants.InputValidationResult.PASS)
            } else ruleNoSpecialChars.postValue(Constants.InputValidationResult.FAIL)
            if (noSpacePattern.containsMatchIn(username.value!!)) {
                flagNoSpace = true
                ruleNoSpace.postValue(Constants.InputValidationResult.PASS)
            } else ruleNoSpace.postValue(Constants.InputValidationResult.FAIL)
            if (onAlphaNumPattern.containsMatchIn(username.value!!)) {
                flagOneAlphaNum = true
                ruleOneAlphanumeric.postValue(Constants.InputValidationResult.PASS)
            } else ruleOneAlphanumeric.postValue(Constants.InputValidationResult.FAIL)
            if (username.value!!.trim().length in 3..15) {
                flagMinMax = true
                ruleCharacterCount.postValue(Constants.InputValidationResult.PASS)
            } else ruleCharacterCount.postValue(Constants.InputValidationResult.FAIL)

            if (flagNoSpace && flagNoSpecial) ruleNoSpaceAndSpecialChars.postValue(Constants.InputValidationResult.PASS)
            else ruleNoSpaceAndSpecialChars.postValue(Constants.InputValidationResult.FAIL)

            val hasLetter = username.value?.any { it.isLetter() }
            flagHasLetterOk = hasLetter == true

            if (flagHasLetterOk) {
                ruleHasLetter.postValue(Constants.InputValidationResult.PASS)
            } else ruleHasLetter.postValue(Constants.InputValidationResult.FAIL)

            if (flagMinMax && flagNoSpecial && flagNoSpace && flagOneAlphaNum && flagMax2SpecialCharsOk && flagHasLetterOk) {
                _usernameValid.postValue(true)
            } else _usernameValid.postValue(false)

        }
        else {
            Log.i( "checkUsername: ", "Username is empty ${username.value}")
            ruleTwoSpecialChars.postValue(Constants.InputValidationResult.NONE)
            ruleHasLetter.postValue(Constants.InputValidationResult.NONE)
            ruleNoSpecialChars.postValue(Constants.InputValidationResult.NONE)
            ruleNoSpace.postValue(Constants.InputValidationResult.NONE)
            ruleOneAlphanumeric.postValue(Constants.InputValidationResult.NONE)
            ruleCharacterCount.postValue(Constants.InputValidationResult.NONE)
            ruleNoSpaceAndSpecialChars.postValue(Constants.InputValidationResult.NONE)
        }
    }
    abstract fun checkUsernameAvailability()
}
