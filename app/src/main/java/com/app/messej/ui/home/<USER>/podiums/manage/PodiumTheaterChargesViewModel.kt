package com.app.messej.ui.home.publictab.podiums.manage

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.data.model.enums.TheaterCharge
import com.app.messej.data.repository.PodiumRepository

class PodiumTheaterChargesViewModel(application: Application) : AndroidViewModel(application) {
    private val podiumRepository = PodiumRepository(application)

    private val _podiumId = MutableLiveData<String>()
    val podiumId: LiveData<String> = _podiumId

    private val _podiumKind = MutableLiveData<PodiumKind>()
    val podiumKind: LiveData<PodiumKind> = _podiumKind

    fun setPodiumId(podiumId: String) {
        _podiumId.postValue(podiumId)
    }

    fun setPodiumKind(kind: PodiumKind) {
        _podiumKind.postValue(kind)
    }

    private val _chargeType = MutableLiveData(TheaterCharge.SPEAKER)
    val chargeType: LiveData<TheaterCharge> = _chargeType

    fun setChargeType(chargeType: TheaterCharge) {
        if (_chargeType.value != chargeType) _chargeType.postValue(chargeType)
    }

    val theaterChargesPager = _podiumId.switchMap { id ->
        chargeType.switchMap { type ->
            podiumRepository.getTheaterChargesPager(type.name.lowercase(java.util.Locale.getDefault()), podiumId = id).liveData.cachedIn(viewModelScope)
        }
    }

    val theaterLikesPager = _podiumId.switchMap { id ->
        podiumRepository.getTheaterLikesPager(podiumId = id).liveData.cachedIn(viewModelScope)
    }
}