package com.app.messej.ui.home.publictab.podiums.live

import android.app.Dialog
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.livedata.observeAsState
import androidx.databinding.DataBindingUtil
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.app.messej.R
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.databinding.FragmentMaidanPlayerStatsBottomSheetBinding
import com.app.messej.ui.home.publictab.podiums.manage.PodiumMaidanSpeakerActionsBottomSheetFragmentDirections
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import kotlin.math.roundToInt

class MaidanPlayerStatsBottomSheetFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentMaidanPlayerStatsBottomSheetBinding
    private val viewModel: PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)
    private val args: MaidanPlayerStatsBottomSheetFragmentArgs by navArgs()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_PodiumBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(
            inflater,
            R.layout.fragment_maidan_player_stats_bottom_sheet,
            container,
            false
        )
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        if (dialog is BottomSheetDialog) {
            dialog.behavior.apply {
                isFitToContents = true
                isDraggable = true
                skipCollapsed = true
                isCancelable = true
            }
        }
        return dialog
    }

    private fun setup() {
        viewModel.getMaidanPlayerSummary(
            userId = args.userId,
            competitorId = args.competitorId.takeIf { it != -1 })
        binding.actionSwitch.setOnClickListener {
            goToCompetitorPodium()
        }
        binding.btnShare.setOnClickListener {
            sharePodium()
        }
        binding.btnExit.setOnClickListener {
            exitMaidan()
        }
    }

    private fun observe() {

        binding.maidanPlayerStatsComposeView.setContent {
            val maidanSummaryState = viewModel.maidanPlayerSummary.observeAsState()
            binding.canSwitch =
                args.userId == viewModel.podium.value?.competitorUserId && viewModel.iAmManager.value != true
            binding.canShare =
                args.userId == viewModel.podium.value?.managerId && viewModel.iAmManager.value != true
            val maidanSummary = maidanSummaryState.value
            val speakerState = viewModel.allSpeakers.observeAsState()
            val speaker = speakerState.value
            val isLoading = viewModel.maidanPlayerSummaryLoading.observeAsState(initial = false)
            if (isLoading.value) {
                Loader()
            } else {
                MaidanPlayerSummaryComposable(speaker, maidanSummary)
            }
        }
    }

    private fun goToCompetitorPodium() {
        viewModel.leave { left ->
            if (!left) return@leave
            viewModel.podium.value?.competitorPodiumId?.let { cId ->
                val options = NavOptions.Builder()
                    .setPopUpTo(R.id.nav_live_podium, inclusive = true)
                    .build()
                val action =
                    PodiumMaidanSpeakerActionsBottomSheetFragmentDirections.actionGlobalNavLivePodium(
                        podiumId = cId,
                        kind = PodiumKind.MAIDAN.ordinal,
                        joinHidden = viewModel.joinHidden
                    )
                findNavController().navigateSafe(action, options)
            }
        }
    }

    private fun sharePodium() {
        viewModel.podium.value?.let {
            if (it.type == Podium.PodiumType.PRIVATE) {
                val message =
                    getString(R.string.podium_share_text, viewModel.user.name, it.shareLink ?: "")
                sharePodiumIntent(message)
            } else {
                val message =
                    getString(R.string.podium_share_text_public, it.name, it.shareLink ?: "")
                sharePodiumIntent(message)
            }
        }
    }

    private fun sharePodiumIntent(message: String?) {
        val sendIntent: Intent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(
                Intent.EXTRA_TEXT, message
            )
            type = "text/plain"
        }
        val shareIntent = Intent.createChooser(sendIntent, null)
        startActivity(shareIntent)
    }

    private fun exitMaidan() {
        if (viewModel.iAmOnMainScreen()) {
            confirmMaidanExit {
                viewModel.exitMaidan()
            }
        } else {
            confirmLeave {
                viewModel.leave()
            }
        }
    }

    private fun confirmMaidanExit(confirm: () -> Unit) {
        val challenge = viewModel.activeChallenge.value
        val status = viewModel.maidanStatusData.value
        val exitWillBeCharged =
            challenge != null && status is PodiumLiveViewModel.MaidanStatusData.ChallengeActive
        confirmAction(
            title = R.string.podium_maidan_exit_prompt_title,
            message = if (exitWillBeCharged) getString(
                R.string.podium_maidan_exit_prompt_fee,
                challenge.challengeExitFee?.roundToInt().toString()
            )
            else getString(R.string.podium_maidan_exit_prompt),
            positiveTitle = R.string.podium_maidan_exit_prompt_ok,
            negativeTitle = R.string.common_cancel
        ) {
            confirm.invoke()
        }
    }

    private fun confirmLeave(confirm: () -> Unit) {
        confirmAction(
            title = R.string.podium_action_leave_confirm_title,
            message = R.string.podium_action_leave_confirm_message,
            positiveTitle = R.string.podium_action_leave,
            negativeTitle = R.string.common_cancel
        ) {
            confirm.invoke()
        }
    }

}