package com.app.messej.ui.home.gift.bottomSheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.databinding.FragmentBirthdayPodiumCreateBottomSheetBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class BirthdayPodiumCreateBottomSheetFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentBirthdayPodiumCreateBottomSheetBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_birthday_podium_create_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
    }

    override fun getTheme(): Int {
        return R.style.Widget_Flashat_CaseDetailsBottomSheet
    }

    private fun setUp() {
        binding.btnClose.setOnClickListener {
            findNavController().navigateUp()
        }

        binding.buttonSendBirthdayGift.setOnClickListener {
            findNavController().navigateSafe(
                direction = BirthdayPodiumCreateBottomSheetFragmentDirections.actionGlobalCreatePodiumFragment()
            )
        }
    }

}