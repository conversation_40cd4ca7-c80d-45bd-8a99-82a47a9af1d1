package com.app.messej.ui.home.publictab.podiums.create

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.AutoCompleteTextView
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.api.huddles.CreatePodiumRequest.PodiumTypeEntry
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.data.model.enums.Gender
import com.app.messej.data.model.enums.PodiumEntry
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.EnumUtil.except
import com.app.messej.databinding.FragmentCreatePodiumBinding
import com.app.messej.ui.home.publictab.common.BaseProfilePicAttachFragment
import com.app.messej.ui.home.publictab.huddles.create.CreateHuddleFragmentDirections
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.displayText
import com.app.messej.ui.home.publictab.podiums.create.CreatePodiumViewModel.Companion.NameError
import com.app.messej.ui.profile.ProfileImageAttachSourceFragment.Companion.ATTACH_SOURCE_RESULT_KEY
import com.app.messej.ui.profile.ProfileImageAttachSourceFragment.Companion.SRC_CAMERA
import com.app.messej.ui.profile.ProfileImageAttachSourceFragment.Companion.SRC_GALLERY
import com.app.messej.ui.profile.ProfileImageAttachSourceFragment.Companion.SRC_PROFILE_PHOTO
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.showSnackbar
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.TextFormatUtils.setAsMandatory

class CreatePodiumFragment : BaseProfilePicAttachFragment() {


    override val viewModel: CreatePodiumViewModel by viewModels()
    private lateinit var binding: FragmentCreatePodiumBinding
    private val args: CreatePodiumFragmentArgs by navArgs()

    private var mTypeAdapter: ArrayAdapter<PodiumTypeEntry>? = null

    override val bindingRoot: View
        get() = binding.root

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_create_podium, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }
    override fun onStart() {
        super.onStart()

        (activity as AppCompatActivity).onBackPressedDispatcher.addCallback(viewLifecycleOwner, object: OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                Log.d("NAVC", "handleOnBackPressed:")
                showBackPressAlert()
            }
        })
        (activity as MainActivity).setupActionBar(binding.toolbar)
        binding.toolbar.apply {
            setNavigationOnClickListener {
                showBackPressAlert()
            }
        }
    }
    private fun setUp() {
        viewModel.setMode(args.podiumId)
        binding.apply {
            labelWhoCanJoin.setAsMandatory()
            labelWhoCanComment.setAsMandatory()
            labelWhoCanSpeak.setAsMandatory()
            labelSpeakingFee.setAsMandatory()
            labelFeesForAudience.setAsMandatory()
            labelStageFee.setAsMandatory()
            labelJoiningFee.setAsMandatory()
        }

        binding.podiumPolicyButton.setOnClickListener {
            val documentType = DocumentType.PODIUM_POLICY
            findNavController().navigateSafe(CreateHuddleFragmentDirections.actionGlobalPolicyFragment(documentType, false))
        }

        binding.huddleDp.setOnClickListener {
            if (viewModel.createPodiumLoading.value == true) return@setOnClickListener
            val action = CreateHuddleFragmentDirections.actionGlobalProfileImageAttachSourceFragment(isHeaderHidden = false, allowUseProfilePhoto = true, isPodium = true)
            findNavController().navigateSafe(action)
        }


        binding.actionGoLiveButton.setOnClickListener {
            if (args.podiumId != "-1") {
                viewModel.editPodium(true)
            } else {
                viewModel.createPodium(true)
            }

        }

        binding.actionSaveAsDraft.setOnClickListener {
            if (args.podiumId != "-1") {
                viewModel.editPodium(false)
            } else {
                viewModel.createPodium(false)
            }
        }

        fun PodiumTypeEntry.displayText(): String {
            return when (this) {
                PodiumTypeEntry.PUBLIC_GENERAL -> getString(R.string.podium_type_public)
                PodiumTypeEntry.PRIVATE_GENERAL -> getString(R.string.podium_type_private)
                PodiumTypeEntry.PUBLIC_MEN_ONLY ->  getString(R.string.podium_type_public_men_only)
                PodiumTypeEntry.PUBLIC_WOMEN_ONLY -> getString(R.string.podium_type_public_women_only)
                PodiumTypeEntry.PRIVATE_MEN_ONLY -> getString(R.string.podium_type_private_men_only)
                PodiumTypeEntry.PRIVATE_WOMEN_ONLY -> getString(R.string.podium_type_private_women_only)
            }
        }

        val typeAdapter = object : ArrayAdapter<PodiumTypeEntry>(requireContext(), R.layout.item_general_dropdown) {

            private val mTypes: Array<PodiumTypeEntry>
                get() {
                    val types = getAvailablePodiumTypes(viewModel.user.profile.gender,viewModel.podiumToEdit.value)
                    Log.w("CPVM", "setUp: ${viewModel.user.profile.gender} | ${viewModel.podiumToEdit.value?.entry} | ${types.toList()}")
                    return types
                }

            var selectedPos: Int? = null

            override fun getItem(position: Int) = mTypes[position]
            override fun getCount() = mTypes.size

            override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
                val cView = convertView ?: layoutInflater.inflate(R.layout.item_general_dropdown, parent, false)
                getItem(position).let { type ->
                    (cView.findViewById<AppCompatTextView>(R.id.text)).text = type.displayText()
                    (cView.findViewById<AppCompatImageView>(R.id.checked_icon)).visibility = if (position == selectedPos) View.VISIBLE else View.GONE
                }
                return cView
            }
        }
        mTypeAdapter = typeAdapter
        (binding.typeDropdown.editText as? AutoCompleteTextView)?.apply {
            setAdapter(typeAdapter)
            setOnItemClickListener { _, _, position, _ ->
                typeAdapter.selectedPos = position
                val item = typeAdapter.getItem(position)
                setText(item.displayText())
                viewModel.podiumEntryType.postValue(item)
            }
            viewModel.podiumEntryType.observe(viewLifecycleOwner) { type->
                Log.d("TYPEE", "" + type.displayText())
                typeAdapter.selectedPos = typeAdapter.getPosition(type) // Set the saved type to the AutoCompleteTextView
                setText(type.displayText()) // Set the text to the displayText()
                Log.d("TYPEE", "pos  ${type.displayText()}")

            }
        }
        binding.actionPodiumDelete.setOnClickListener {
            confirmAction(
                title = null, message = R.string.podium_action_delete_podium_confirm_message
            ) {
                viewModel.deletePodium()
            }
        }

        val kindAdapter = object : ArrayAdapter<PodiumKind>(requireContext(), R.layout.item_general_dropdown) {

            private val kinds = PodiumKind.entries.except(PodiumKind.MAIDAN)
            private val mKinds = if (!DateTimeUtils.isUserBirthdayToday(dob = viewModel.user.profile.dateOfBirth)) {
                kinds.except(PodiumKind.BIRTHDAY)
            } else kinds
            var selectedPos: Int? = null

            override fun getItem(position: Int) = mKinds[position]
            override fun getCount() = mKinds.size

            override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
                val cView = convertView ?: layoutInflater.inflate(R.layout.item_general_dropdown, parent, false)
                getItem(position).let { type ->
                    (cView.findViewById<AppCompatTextView>(R.id.text)).text = type.displayText()
                    (cView.findViewById<AppCompatImageView>(R.id.checked_icon)).visibility = if (position == selectedPos) View.VISIBLE else View.GONE
                }
                return cView
            }
        }

        (binding.kindPodiumDropdown.editText as? AutoCompleteTextView)?.apply {
            setAdapter(kindAdapter)
            setOnItemClickListener { _, _, position, _ ->
                kindAdapter.selectedPos = position
                val item = kindAdapter.getItem(position)
                setText(item.displayText())
                viewModel.kind.postValue(item)
            }

            viewModel.kind.observe(viewLifecycleOwner) { kind->
                Log.d("KIND", "" + kind?.displayText())
                kindAdapter.selectedPos = kindAdapter.getPosition(kind) // Set the saved type to the AutoCompleteTextView
                setText(kind?.displayText().orEmpty()) // Set the text to the displayText()
                Log.d("KIND", "pos  ${kind?.displayText()}")

            }
        }
    }

    fun getAvailablePodiumTypes(gender: Gender? ,podium: Podium?): Array<PodiumTypeEntry> {
        var entries = PodiumTypeEntry.entries.toTypedArray()

        podium?.let { pod ->
            Log.d("CPF", "its an edit")
            if (pod.isLive) {
                // only allow supersets so that already joined users wont be kicked out
                val currentEntry = PodiumTypeEntry.combine(podium.type,podium.entry?:PodiumEntry.GENERAL)
                entries = currentEntry.superSet()
                Log.d("CPF", "podium is live, restricting to superset: ${entries.toList()}")
            }

            if (pod.entry == PodiumEntry.GENERAL) {
                //don't allow gender restrictions as there might already be admins assigned
                entries = entries.except(
                    PodiumTypeEntry.PUBLIC_WOMEN_ONLY,
                    PodiumTypeEntry.PRIVATE_WOMEN_ONLY,
                    PodiumTypeEntry.PRIVATE_MEN_ONLY,
                    PodiumTypeEntry.PUBLIC_MEN_ONLY
                )
                Log.d("CPF", "podium is not live, removing gender types: ${entries.toList()}")
            }
        }

        if (gender!=Gender.FEMALE) {
            // only women can set women only restrictions
            entries = entries.except(
                PodiumTypeEntry.PUBLIC_WOMEN_ONLY,
                PodiumTypeEntry.PRIVATE_WOMEN_ONLY
            )
            Log.d("CPF", "user is not female, removing female types: ${entries.toList()}")
        }
        if (gender!=Gender.MALE) {
            // only men can set men only restrictions
            entries = entries.except(
                PodiumTypeEntry.PUBLIC_MEN_ONLY,
                PodiumTypeEntry.PRIVATE_MEN_ONLY
            )
            Log.d("CPF", "user is not male, removing male types: ${entries.toList()}")
        }
        return entries
    }

    fun PodiumKind.displayText() = this.displayText(requireContext())

    private fun observe() {

        viewModel.podiumToEdit.observe(viewLifecycleOwner) {
            mTypeAdapter?.notifyDataSetChanged()
        }

        viewModel.nameError.observe(viewLifecycleOwner) {
            binding.textInputName.error = when (it) {
                NameError.GT_MAX -> {
                    binding.textInputName.isErrorEnabled = true
                    resources.getString(R.string.register_create_profile_error_name_max)
                }
                else -> {
                    binding.textInputName.isErrorEnabled = false
                    null
                }
            }
        }

        viewModel.kind.observe(viewLifecycleOwner) { kind ->
            when (kind) {
                PodiumKind.LECTURE -> {
                    binding.labelKindNote.isVisible = false
                }

                PodiumKind.ASSEMBLY -> {
                    binding.labelKindNote.text = getString(R.string.create_podium_kind_note, getString(R.string.podium_assembly_join_popup_message))
                    binding.labelKindNote.isVisible = true
                }

                PodiumKind.ALONE -> {
                    binding.labelKindNote.text = getString(R.string.create_podium_kind_note, getString(R.string.podium_alone_about_desc))
                    binding.labelKindNote.isVisible = true
                }

                PodiumKind.INTERVIEW -> {
                    binding.labelKindNote.text = getString(R.string.create_podium_kind_note, getString(R.string.podium_interview_about_desc))
                    binding.labelKindNote.isVisible = true
                }

                PodiumKind.MAIDAN -> {}
                PodiumKind.THEATER -> {
                    binding.labelKindNote.text = getString(R.string.create_podium_kind_note, getString(R.string.podium_theater_about_desc))
                    binding.labelKindNote.isVisible = true
                }
                PodiumKind.BIRTHDAY -> {
                    binding.labelKindNote.text = getString(R.string.create_podium_kind_note, getString(R.string.podium_birthday_note))
                    binding.labelKindNote.isVisible = true
                }

                null -> {}
            }
        }

        setFragmentResultListener(ATTACH_SOURCE_RESULT_KEY) { _, bundle ->
            when (bundle.getString(ATTACH_SOURCE_RESULT_KEY)) {
                SRC_GALLERY -> {
                    selectImageFromGallery()
                }
                SRC_CAMERA -> {
                    takeImage()
                }
                SRC_PROFILE_PHOTO -> viewModel.useProfilePhoto()
            }
        }
        viewModel.onPodiumCreated.observe(viewLifecycleOwner) {
            if(it.isLive) Toast.makeText(requireContext(), getString(R.string.podium_created), Toast.LENGTH_SHORT).show() else Toast.makeText(requireContext(), getString(R.string.podium_saved_as_draft), Toast.LENGTH_SHORT).show()

            if (it.isLive) {
                findNavController().navigateSafe(CreatePodiumFragmentDirections.actionGlobalNavLivePodium(it.id,it.kind?.ordinal?:-1),navOptions = NavOptions.Builder()
                    .setPopUpTo(R.id.createPodiumFragment, inclusive = true)
                    .build())
            } else {
                findNavController().popBackStack()
            }
        }

        viewModel.onPodiumDelete.observe(viewLifecycleOwner){
            Toast.makeText(requireContext(),it,Toast.LENGTH_SHORT).show()
            findNavController().popBackStack()
        }
        viewModel.onPodiumDeleteError.observe(viewLifecycleOwner){
            showSnackbar(it)
        }

        viewModel.onCreatePodiumError.observe(viewLifecycleOwner){
            showSnackbar(it)
        }
    }

    private fun showBackPressAlert() {
        confirmAction(
//            title = R.string.create_huddle_unsaved_alert_title,
            message = resources.getString(R.string.sure_you_want_to_cancel),
            positiveTitle = R.string.common_confirm,
            negativeTitle = R.string.common_cancel
        ) {
            if (view != null && viewLifecycleOwner.lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
                findNavController().popBackStack()
            }
        }
    }
}