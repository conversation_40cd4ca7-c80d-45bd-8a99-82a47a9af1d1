package com.app.messej.ui.home.publictab.socialAffairs.honours

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.dimensionResource
import androidx.paging.compose.collectAsLazyPagingItems
import com.app.messej.R
import com.app.messej.ui.composeComponents.ComposeShimmerListLayout
import com.app.messej.ui.composeComponents.CustomPaginationView
import com.app.messej.ui.home.publictab.socialAffairs.committee.SocialCommitteeMembersShimmerItem

@Composable
fun HonoursComposeScreen(
    viewModel: SocialHonoursViewModel,
    onUserDPClick: (userId: Int) -> Unit
) {
    val honoursList = viewModel.honoursList.collectAsLazyPagingItems()
    val countryFlagList by viewModel.countryFlagList.observeAsState()

    CustomPaginationView(
        lazyPagingItem = honoursList,
        emptyItemIcon = R.drawable.ic_empty_honours,
        loadingAnimation = {
            ComposeShimmerListLayout(
                modifier = Modifier.padding(top = dimensionResource(id = R.dimen.activity_margin)),
                verticalSpace = dimensionResource(id = R.dimen.line_spacing),
                itemCount = 10
            ) { brush ->
                SocialCommitteeMembersShimmerItem(brush = brush)
            }
        },
        verticalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.element_spacing)),
        contentPadding = PaddingValues(all = dimensionResource(id = R.dimen.activity_margin)),
        emptyViewTitle = R.string.social_no_honours_members_text,
        lazyColumnContent = {
            items(honoursList.itemCount) {
                val item = honoursList[it] ?: return@items
                val userDetail = item.userDetail
                val countryFlag = countryFlagList?.get(userDetail?.countryCode)
                val updatedItem = item.copy(userDetail = userDetail?.copy(countryFlag = countryFlag))
                HonourSingleItem(
                    item = updatedItem,
                    onUserDPClick = {
                        updatedItem.userDetail?.id?.let { id -> onUserDPClick(id) }
                    }
                )
            }
        }
    )
}