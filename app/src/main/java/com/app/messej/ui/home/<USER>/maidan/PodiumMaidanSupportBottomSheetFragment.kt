package com.app.messej.ui.home.publictab.maidan

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.data.repository.AccountRepository
import com.app.messej.databinding.FragmentPodiumMaidanSupporterBottomSheetBinding
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.checkIfJoinHidden
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class PodiumMaidanSupportBottomSheetFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentPodiumMaidanSupporterBottomSheetBinding
    private val args: PodiumMaidanSupportBottomSheetFragmentArgs by navArgs()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_PodiumBottomSheet)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = true
        setup()
        observer()
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog =  super.onCreateDialog(savedInstanceState)
        if (dialog is BottomSheetDialog) {
            dialog.behavior.apply {
                isFitToContents = true
                isDraggable = true
                skipCollapsed = true
                isCancelable=true
            }
        }
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_maidan_supporter_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    private fun observer() {

    }

    private fun setup() {
        binding.name = args.name
        binding.actionClose.setOnClickListener {
            findNavController().popBackStack()
        }
        binding.actionWatch.setOnClickListener {

            checkIfJoinHidden(AccountRepository(requireContext()).user,false) { hidden ->
                val action = NavGraphHomeDirections.actionGlobalNavLivePodium(
                    args.podiumId,
                    PodiumKind.MAIDAN.ordinal,
                    joinHidden = hidden
                )
                val options = NavOptions.Builder()
                    .setPopUpTo(R.id.podiumMaidanSupportBottomSheetFragment, inclusive = true)
                    .build()
                findNavController().navigateSafe(action, options)
            }
        }
    }
}