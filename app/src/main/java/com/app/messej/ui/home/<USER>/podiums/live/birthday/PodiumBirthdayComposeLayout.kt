package com.app.messej.ui.home.publictab.podiums.live.birthday

import android.view.SurfaceView
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import com.app.messej.ui.home.publictab.podiums.composeView.MainSpeakerActions
import com.app.messej.ui.home.publictab.podiums.composeView.PodiumMainSpeakerComposeView
import com.app.messej.ui.home.publictab.podiums.composeView.PodiumSpeakerComposeView
import com.app.messej.ui.home.publictab.podiums.composeView.PodiumSpeakerListener
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel

@Composable
fun PodiumBirthdayComposeLayout(
    viewModel: PodiumLiveViewModel,
    subSpeakerListener: PodiumSpeakerListener,
    toggleCamera: () -> Unit,
    toggleCameraFacing: () -> Unit,
    onMainSpeakerClick: () -> Unit
) {
    val showLocalVideoSurface = viewModel.showLocalVideoSurface.observeAsState(initial = false)
    val mainSpeaker = viewModel.mainScreenComposeSpeaker.observeAsState()
    val isHostTagNeededWithSpeakerName = viewModel.isHostTagNeededWithSpeakerName(speaker = mainSpeaker.value?.speaker)
    val myVideoOn = viewModel.myVideoIsTurnedOn.observeAsState(initial = false)
    val isPodiumLoading = viewModel.podiumDetailsLoading.observeAsState(initial = false)
    val podiumKind by viewModel.podiumKind.observeAsState()

    BoxWithConstraints(modifier = Modifier.fillMaxSize()) {
        //Calculate the single speaker tile size
        val speakerSize = maxWidth / 4

        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            Row(modifier = Modifier
                .height(intrinsicSize = IntrinsicSize.Max)
                .fillMaxWidth()
            ) {
                //3 Sub Speaker View (On Left Side). From top to bottom
                Column(modifier = Modifier.width(width = speakerSize)) {
                    repeat(times = 3) { position ->
                        SubSpeakersView(
                            viewModel = viewModel,
                            speakerSize = speakerSize,
                            speakerPosition = position,
                            listener = subSpeakerListener
                        )
                    }
                }

                //Main Speaker View. Visible ath the centre
                PodiumMainSpeakerComposeView(
                    modifier = Modifier.weight(weight = 1F).fillMaxHeight(),
                    showLocalVideoSurface = showLocalVideoSurface,
                    podiumKind = podiumKind,
                    mainSpeaker = mainSpeaker,
                    isHostTagNeededWithSpeakerName = isHostTagNeededWithSpeakerName,
                    isCurrentUser = mainSpeaker.value?.speaker?.id == viewModel.user.id,
                    myVideoOn = myVideoOn,
                    isLoading = isPodiumLoading,
                    listener = object : MainSpeakerActions {
                        override fun onToggleMic() {
                            viewModel.muteToggleSelf()
                        }

                        override fun onToggleVideo() {
                            toggleCamera()
                        }

                        override fun onSwitchCamera() {
                            toggleCameraFacing()
                        }

                        override fun onMainSpeakerClick() {
                            onMainSpeakerClick()
                        }

                        override fun onDetachMainScreenPreview() {
                            viewModel.detachMainScreenPreview()
                        }

                        override fun setupMainScreenPreview(surfaceView: SurfaceView) {
                            viewModel.setupMainScreenPreview(surface = surfaceView)
                        }

                        override fun setupRemoteMainScreenPreview(surfaceView: SurfaceView) {
                            viewModel.setupRemoteMainScreenPreview(
                                surface = surfaceView,
                                id = viewModel.mainScreenSpeakerId.value ?: return
                            )
                        }
                    }
                )

                //3 Sub Speaker View (On Right Side). From top to bottom
                Column(modifier = Modifier.width(width = speakerSize)) {
                    repeat(times = 3) { position ->
                        SubSpeakersView(
                            viewModel = viewModel,
                            speakerSize = speakerSize,
                            speakerPosition = position + 3,
                            listener = subSpeakerListener
                        )
                    }
                }
            }

            Row(
                modifier = Modifier.fillMaxWidth()
            ) {
                //4 Sub Speaker View(On Bottom Side). Below the main speaker view
                repeat(times = 4) { position ->
                    SubSpeakersView(
                        viewModel = viewModel,
                        speakerSize = speakerSize,
                        speakerPosition = position + 6,
                        listener = subSpeakerListener
                    )
                }
            }
        }
    }
}

@Composable
private fun SubSpeakersView(
    viewModel: PodiumLiveViewModel,
    speakerSize: Dp,
    speakerPosition: Int,
    listener: PodiumSpeakerListener
) {
    val speakers by viewModel.speakersCompose.observeAsState()
    val speaker = speakers?.getOrNull(index = speakerPosition)
    val isHostTagNeededWithSpeakerName = viewModel.isHostTagNeededWithSpeakerName(speaker = speaker?.speaker)
    val podiumKind by viewModel.podiumKind.observeAsState()
    val isLoading = viewModel.podiumDetailsLoading.observeAsState(initial = false)

    PodiumSpeakerComposeView(
        modifier = Modifier.size(size = speakerSize),
        item = speaker,
        isHostTagNeededWithSpeakerName = isHostTagNeededWithSpeakerName,
        showControls = speaker?.speaker?.id == viewModel.user.id,
        podiumKind = podiumKind,
        isLoading = isLoading,
        listener = listener
    )
}