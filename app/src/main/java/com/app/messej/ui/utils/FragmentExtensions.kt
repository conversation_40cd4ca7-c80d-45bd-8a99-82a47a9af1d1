package com.app.messej.ui.utils

import android.app.Activity
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.DialogInterface
import android.graphics.Typeface
import android.os.Build
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.util.Log
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.annotation.ColorRes
import androidx.annotation.DimenRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.AppCompatTextView
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.platform.ComposeView
import androidx.core.content.ContextCompat
import androidx.core.view.MenuHost
import androidx.core.view.MenuProvider
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.profile.UserBirthdayResponse
import com.app.messej.data.model.socket.SentGiftPayload
import com.app.messej.data.repository.datastore.FlashatDatastore
import com.app.messej.databinding.LayoutCustomProgressDialogBinding
import com.app.messej.databinding.LayoutFlaxRateChipBinding
import com.app.messej.databinding.LayoutGiftRateChipBinding
import com.app.messej.databinding.LayoutHeaderMaidanButtonBinding
import com.app.messej.databinding.LayoutLegalAffairsPayFineBinding
import com.app.messej.ui.home.CommonHomeViewModel
import com.app.messej.ui.home.publictab.authorities.legalAffairs.payFine.PayFineView
import com.app.messej.ui.utils.DataFormatHelper.DpToPx
import com.app.messej.ui.utils.DataFormatHelper.dpToPx
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.appbar.CollapsingToolbarLayout
import com.google.android.material.badge.BadgeDrawable
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.snackbar.Snackbar
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

object FragmentExtensions {

    fun Fragment.showKeyboard(view: View) {
        view.requestFocus()
        val imm: InputMethodManager = context?.getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.showSoftInput(view, 0)
    }

    fun Fragment.hideKeyboard() {
        val imm: InputMethodManager = context?.getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(this.view?.windowToken, 0)
    }

    fun Fragment.enableDismissKeyboardOnTap(input: View) {
        val view = this.view?.rootView ?: return
        view.isClickable = true
        view.isFocusableInTouchMode = true
        input.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                view.requestFocus()
                hideKeyboard()
            }
        }
    }

    val Fragment.isTabletScreen: Boolean
        get() {
            return resources.getBoolean(R.bool.isTabletScreen)
        }

    fun Fragment.ifStillAttached(operation: Context.() -> Unit) {
        if (isAdded && context != null) {
            operation(requireContext())
        }
    }

    fun BadgeDrawable.adjustForNotifications(c: Context) {
        verticalOffset = 4.DpToPx
        horizontalOffset = 4.DpToPx
        backgroundColor = ContextCompat.getColor(c, R.color.colorError)
        badgeTextColor = ContextCompat.getColor(c, R.color.textColorOnPrimary)
    }

    fun BadgeDrawable.adjustForBottomNavDot(context: Context) = adjustForBottomNavDot(ContextCompat.getColor(context, R.color.colorPrimary))

    fun BadgeDrawable.adjustForBottomNavDot(color: Int) {
        val badgeOffset = 0.DpToPx
        backgroundColor = color
        horizontalOffset = badgeOffset
        verticalOffset = badgeOffset
    }

    fun BadgeDrawable.adjustForSubNavDot(context: Context) = adjustForSubNavDot(ContextCompat.getColor(context, R.color.colorPrimary))

    fun BadgeDrawable.adjustForSubNavDot(color: Int) {
        val badgeOffset = 4.DpToPx
        backgroundColor = color
        horizontalOffset = badgeOffset
        verticalOffset = badgeOffset
    }

    fun Fragment.addAsMenuHost(remove: Boolean = false) {
        if (this is MenuProvider) {
            val menuHost: MenuHost = requireActivity()
            if (remove) {
                menuHost.removeMenuProvider(this)
            }
            menuHost.addMenuProvider(this, viewLifecycleOwner, Lifecycle.State.RESUMED)
        }
    }

    fun Fragment.setBadgeNumber(badge: BadgeDrawable?, num: Int?) {
        if (num != null && num > 0) {
            badge?.apply {
                maxCharacterCount = 3  // Allows for "99+"
                number = if (num > 99) {
                    badge.text="99+"
                    99  // This is just to ensure the badge number is set
                } else {
                    text = num.toString()
                    num
                }
                isVisible = true
            }

        } else {
            badge?.apply {
                clearNumber()
                isVisible = false
            }
        }
    }

    fun Fragment.syncToExpandingTitle(textView: AppCompatTextView, collapsingToolbar: CollapsingToolbarLayout, @DimenRes topOffset: Int? = null) {
        textView.addOnLayoutChangeListener { v, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom ->
            collapsingToolbar.apply {
                val offset = if (topOffset != null) resources.getDimensionPixelSize(topOffset) else 0
                Log.d("COLLAPSE", "syncToExpandingTitle: $offset should be ${60.dpToPx(requireContext())}")
                expandedTitleMarginTop = offset + top
                val parentWidth = (v.parent as View).measuredWidthAndState
                expandedTitleMarginStart = if (resources.configuration.layoutDirection == View.LAYOUT_DIRECTION_LTR) left else parentWidth - right
                expandedTitleMarginEnd = if (resources.configuration.layoutDirection == View.LAYOUT_DIRECTION_LTR) parentWidth - right else left
            }
        }
    }

    fun Fragment.showToast(@StringRes message: Int, length: Int = Toast.LENGTH_LONG) {
        Toast.makeText(requireContext(), message, length).show()
    }

    fun Fragment.showToast(message: String, length: Int = Toast.LENGTH_LONG) {
        Toast.makeText(requireContext(), message, length).show()
    }

    fun Context.showToast(message: String, length: Int = Toast.LENGTH_LONG) {
        Toast.makeText(this, message, length).show()
    }

    fun Context.showToast(message: Int, length: Int = Toast.LENGTH_LONG) {
        Toast.makeText(this, message, length).show()
    }

    fun Fragment.showSnackbar(@StringRes message: Int, length: Int = Snackbar.LENGTH_LONG, func: Snackbar.() -> Unit = {}): Snackbar? {
        val root = (if(this is BottomSheetDialogFragment) dialog?.window?.decorView else view) ?: return null
        return Snackbar.make(root, message, length).apply {
            this.func()
            this.show()
        }
    }

    fun Fragment.showSnackbar(message: String, length: Int = Snackbar.LENGTH_LONG, func: Snackbar.() -> Unit = {}): Snackbar? {
        val root = (if(this is BottomSheetDialogFragment) dialog?.window?.decorView else view) ?: return null
        return Snackbar.make(root, message, length).apply {
            this.func()
            this.show()
        }
    }

    fun View.showSnackbar(message: String, length: Int = Snackbar.LENGTH_LONG, func: Snackbar.() -> Unit = {}): Snackbar? {
        return Snackbar.make(this, message, length).apply {
            this.func()
            this.show()
        }
    }

    inline fun View.afterMeasured(crossinline block: () -> Unit) {
        if (measuredWidth > 0 && measuredHeight > 0) {
            block()
        } else {
            viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    if (measuredWidth > 0 && measuredHeight > 0) {
                        viewTreeObserver.removeOnGlobalLayoutListener(this)
                        block()
                    }
                }
            })
        }
    }

    fun Fragment.bindFlaxRateToolbarChip(binding: LayoutFlaxRateChipBinding) {
        val vm = ViewModelProvider(requireActivity())[CommonHomeViewModel::class.java]
        vm.flaxRatePackage.observe(viewLifecycleOwner) {
            binding.frp = it
        }

        binding.root.setOnClickListener {
            if (binding.frp != null) {
                (activity as MainActivity?)?.navController?.navigateSafe(NavGraphHomeDirections.actionGlobalPerformanceRatingFragment())
            }
        }
    }

    fun Fragment.bindGiftRateToolbarChip(binding: LayoutGiftRateChipBinding){
        val vm = ViewModelProvider(requireActivity())[CommonHomeViewModel::class.java]
        vm.giftPackage.observe(viewLifecycleOwner){
            binding.giftPackage  = it
        }

        binding.giftToolBarLayout.setOnClickListener{
            if (binding.giftPackage!= null) {
                (activity as MainActivity?)?.navController?.navigateSafe(NavGraphHomeDirections.actionGlobalGiftFileFragment())
            }
        }
    }

    fun Fragment.bindMaidanToolbarChip(binding: LayoutHeaderMaidanButtonBinding){
        val vm = ViewModelProvider(requireActivity())[CommonHomeViewModel::class.java]
        vm.unreadCounts.observe(viewLifecycleOwner) { details ->
            Log.w("MAIDAN", "bindMaidanToolbarChip: $details")
            binding.maidanLive = details?.maidanCount?.let { it > 0 } ?: false
            details?.maidanCoins?.let {
                binding.maidanCoins = DataFormatHelper.numberToK(it.toInt())
            }
        }

        binding.maidanToolBarLayout.setOnClickListener{
//            if (binding.giftPackage!= null) {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicMaidanFragment())
//            }
        }
    }

    fun Fragment.bindLegalAffairsPayFineButton(binding: LayoutLegalAffairsPayFineBinding) {
        // Uncomment if needed pay fine button on the tool bar
//        val vm = ViewModelProvider(requireActivity())[CommonHomeViewModel::class.java]
//        vm.unreadCounts.observe(viewLifecycleOwner) {
//            binding.isVisible = it.hasPendingFines
//        }
//        binding.imgPayFine.setOnClickListener {
//            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPayFineFragment(fineCategory = null))
//        }
    }

    fun Fragment.copy(message:String?){
        val clipboard = activity?.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        if(!message.isNullOrEmpty()) {
            val clip = ClipData.newPlainText(message, message)
            clipboard.setPrimaryClip(clip)
            // Only show a toast for Android 12 and lower.
            if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.S_V2)
                Toast.makeText(requireContext(), R.string.chat_text_copied, Toast.LENGTH_SHORT).show()
        }
    }

    fun Fragment.confirmAction(@StringRes title: Int? = null,
                               message: CharSequence,
                               @StringRes positiveTitle: Int = R.string.common_confirm,
                               @StringRes negativeTitle: Int = R.string.common_cancel,
                               @DrawableRes positiveIcon: Int = R.drawable.ic_chat_liked,
                               onCancel:() -> Unit = {},
                               onConfirm: () -> Unit) {
        ifStillAttached {
            showFlashatDialog {
                if (title != null) {
                    setTitle(title)
                }
                setMessage(message)
                setConfirmButton(positiveTitle, positiveIcon) {
                    ifStillAttached {
                        onConfirm.invoke()
                    }
                    true
                }
                setCloseButton(negativeTitle) {
                    ifStillAttached {
                        onCancel.invoke()
                    }
                    true
                }
                setOnDismissListener {
                    ifStillAttached {
                        onCancel.invoke()
                    }
                }
            }
        }
    }

    fun Fragment.confirmAction(@StringRes title: Int? = null,
                               @StringRes message: Int,
                               @StringRes positiveTitle: Int = R.string.common_confirm,
                               @StringRes negativeTitle: Int = R.string.common_cancel,
                               @DrawableRes positiveIcon: Int = R.drawable.ic_chat_liked,
                               onCancel:() -> Unit = {},
                               onConfirm: () -> Unit) {
        try {
            confirmAction(title, resources.getString(message), positiveTitle, negativeTitle, positiveIcon,onCancel, onConfirm)
        } catch (_: Exception) { }
    }

    fun Fragment.downloadAndShowGift(gift: SentGiftPayload) {
        val act = activity
        if(act is MainActivity) {
            act.showGiftVideo(gift)
        }
    }
    fun Fragment.downloadAndShowChallengeResultVideo(winner: Boolean) {
        val act = activity
        if(act is MainActivity) {
            act.showChallengeResultVideo(winner)
        }
    }

    fun Fragment.downloadAndShowBirthdayResultVideo(birthdayUrl: String) {
        val act = activity
        if(act is MainActivity) {
            act.showBirthdayVideo(birthdayUrl)
        }
    }

    fun Fragment.setFragmentResultListenerOnActivity(
        requestKey: String,
        listener: ((requestKey: String, bundle: Bundle) -> Unit),
    ) {
        requireActivity().supportFragmentManager.setFragmentResultListener(requestKey,viewLifecycleOwner,listener)
    }

    fun MenuItem.setupMenuItemTextColor(
        @ColorRes color: Int,
        context: Context,
        isTextBold:Boolean = false
    ) {
        title = SpannableString(title).apply {
            setSpan(ForegroundColorSpan(ContextCompat.getColor(context, color)), 0, this.length, 0)
            if (isTextBold) setSpan(StyleSpan(Typeface.BOLD), 0, this.length, 0)
        }
    }

    fun MenuItem.setAsDestructiveMenuItem(context: Context) {
        this.setupMenuItemTextColor(R.color.colorError, context)
    }

    fun Fragment.setPresidentNotification(response:UserBirthdayResponse) {
        val act = activity
        if(act is MainActivity) {
            act.setPresidentNotification(response)
        }
    }
    fun MenuItem.setAsDestructiveMenuItemParams(context: Context, prefix: String? = null,isJoin:Boolean) {
        val originalTitle = this.title?.toString()?.uppercase()
        val fullTitle = if (!prefix.isNullOrEmpty()) { "$prefix $originalTitle" } else { originalTitle }

        val spannableString = SpannableString(fullTitle)

        val startIndex = prefix?.length?.plus(1) ?: 0
        val endIndex = startIndex + (originalTitle?.length ?: 0)
        spannableString.setSpan(ForegroundColorSpan(if(isJoin)ContextCompat.getColor(context, R.color.colorPass)else ContextCompat.getColor(context, R.color.colorError) ), startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)

        this.title = spannableString
    }

    fun MainActivity.showInsufficientBalanceAlert(message: String) {
        showFlashatDialog {
            setMessage(message)
            setConfirmButton(R.string.title_buy_coins,R.drawable.ic_coin,false) {
                navController.navigateSafe(NavGraphHomeDirections.actionGlobalBuyflaxFragment(isBuyCoin = true))
                true
            }
        }
    }
    fun MainActivity.showInsufficientBalanceAlert(@StringRes message: Int) = showInsufficientBalanceAlert(getString(message))
    fun Fragment.showInsufficientBalanceAlert(message: String,header:String?=null,isBuyCoin:Boolean?=true)  {
        showFlashatDialog {
            setTitle(header)
            setMessage(message)
            setConfirmButton((if(isBuyCoin == true)R.string.title_buy_coins else R.string.title_buy_flix), (if(isBuyCoin == true)R.drawable.ic_coin else R.drawable.ic_flax_coin), false) {
                if (isBuyCoin == true) findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalBuyflaxFragment(isBuyCoin = true)) else findNavController().navigateSafe(
                    NavGraphHomeDirections.actionGlobalBuyflaxFragment(isBuyCoin = false)
                )
                true
            }
        }
    }
    fun Fragment.showInsufficientBalanceAlert(@StringRes message: Int,@StringRes header:Int?=null) = showInsufficientBalanceAlert(getString(message), header?.let { getString(it) })


    fun Fragment.confirmPaidLikeAction(
        onConfirm: () -> Unit,
    ) {
        val datastore = FlashatDatastore()
        lifecycleScope.launch(Dispatchers.IO) {
            if (datastore.getPaidLikeSkipConfirm()) {
                withContext(Dispatchers.Main) {
                    onConfirm.invoke()
                }
            } else {
                withContext(Dispatchers.Main) {
                    showFlashatDialog {
                        setMessage(R.string.podium_paid_like_confirmation)
                        setCheckBox(R.string.podium_paid_like_check_box_validation, false)
                        setConfirmButton(R.string.send, R.drawable.ic_chat_send) {
                            if (checkBoxChecked) {
                                CoroutineScope(Dispatchers.IO).launch {
                                    datastore.setPaidLikeSkipConfirm(true)
                                }
                            }
                            onConfirm.invoke()
                            true
                        }
                        setCloseButton(R.string.common_cancel,R.drawable.ic_close) {
                            true
                        }
                    }
                }
            }
        }
    }
    fun Fragment.setTopInsets(view: View) {
        ViewCompat.setOnApplyWindowInsetsListener(view) { v, windowInsets ->
            val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.updateLayoutParams<ViewGroup.MarginLayoutParams> {
                topMargin = insets.top
            }
            WindowInsetsCompat.CONSUMED
        }
    }
    fun Fragment.showLoader(message: String = getString(R.string.common_loading),content: String? = null): MaterialDialog {
        val loader = MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutCustomProgressDialogBinding>(layoutInflater, R.layout.layout_custom_progress_dialog, null, false)
            view.text = message
            view.header = content
            view.progressBar.isVisible = false
            customView(null, view.root, dialogWrapContent = true)
            cancelable(false)
        }
        lifecycle.apply {
            addObserver(object: LifecycleEventObserver {
                override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                    if (!event.targetState.isAtLeast(Lifecycle.State.INITIALIZED)) {
                        if (loader.isShowing) loader.dismiss()
                    }
                }
            })
        }
        return loader
    }

    fun Fragment.showAlertWithSingleButton(
        @StringRes title: Int? = null,
        @StringRes message: Int,
        @StringRes positiveTitle: Int = R.string.common_close,
        isButtonTextRedColor: Boolean = false,
        isCancelable: Boolean = true,
        onClick: () -> Unit
    ) {
        val alertDialog = MaterialAlertDialogBuilder(requireContext())
            .setMessage(getString(message))
            .setCancelable(isCancelable)
            .setPositiveButton(getString(positiveTitle)) { _, _ ->
                onClick()
            }.create()
        title?.let { alertDialog.setTitle(getString(it)) }
        alertDialog.show()
        if (isButtonTextRedColor) {
            val negativeButton = alertDialog.getButton(DialogInterface.BUTTON_POSITIVE)
            negativeButton.setTextColor(ContextCompat.getColor(requireContext(), R.color.colorError))
        }
    }

    fun Fragment.setupPayFineIcon(composeView: ComposeView) {
        val vm = ViewModelProvider(requireActivity())[CommonHomeViewModel::class.java]
        composeView.setContent {
            val unreadCounts = vm.unreadCounts.observeAsState().value
            AnimatedVisibility (visible = unreadCounts?.hasPendingFines == true, enter = slideInHorizontally { it }, exit = slideOutHorizontally { it }) {
                PayFineView(onClick = {
                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPayFineFragment(fineCategory = null))
                })
            }
        }
    }

    fun AlertDialog.show(owner: LifecycleOwner) {
        val job = owner.lifecycleScope.launch {
            owner.lifecycle.currentStateFlow.collect { state ->
                Log.w("SUG", "onStateChanged: $state")
                if (!state.isAtLeast(Lifecycle.State.STARTED)) {
                    if (isShowing) {
                        dismiss()
                        this.cancel()
                    }
                }
            }
        }

        setOnDismissListener {
            job.cancel()
        }
        show()
    }


    private val fragmentClickTimestamps = mutableMapOf<String, Long>()

    fun Fragment.doubleClickPrevent(delayMillis: Long = 2000L): Boolean {
        val key = this::class.java.name // uniquely identifies the fragment class
        val lastClickTime = fragmentClickTimestamps[key] ?: 0L
        val currentTime = System.currentTimeMillis()

        return if (currentTime - lastClickTime >= delayMillis) {
            fragmentClickTimestamps[key] = currentTime
            true
        } else {
            false
        }
    }


}