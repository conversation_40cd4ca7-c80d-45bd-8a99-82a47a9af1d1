package com.app.messej.ui.home.publictab.podiums.live

import android.content.res.AssetFileDescriptor
import android.media.MediaPlayer
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.ViewPropertyAnimator
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.Toast
import androidx.annotation.RawRes
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.appcompat.widget.PopupMenu
import androidx.compose.ui.platform.ComposeView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.map
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.NavGraphHomeDirections
import com.app.messej.NavLivePodiumDirections
import com.app.messej.R
import com.app.messej.data.model.api.gift.GiftUtils
import com.app.messej.data.model.api.gift.GiftUtils.canSendGifts
import com.app.messej.data.model.api.podium.PodiumChallengeSetupEvent
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.api.podium.challenges.PodiumChallengeScore
import com.app.messej.data.model.entity.NickName.Companion.nickNameOrName
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.FollowerType
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.data.model.enums.PodiumTab
import com.app.messej.data.model.enums.StarType
import com.app.messej.data.model.socket.PodiumMaidanScoreUpdatePayload
import com.app.messej.data.model.socket.SentGiftPayload
import com.app.messej.data.utils.BeepUtils.playTone
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.databinding.FragmentPodiumLiveMaidanBinding
import com.app.messej.databinding.ItemPodiumLikesContainerBinding
import com.app.messej.databinding.ItemPodiumSpeakerMainBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.databinding.LayoutPodiumChatPausedEmptyBinding
import com.app.messej.ui.home.promobar.CommonPromoBarUtil.setupPodiumPromoBoard
import com.app.messej.ui.home.publictab.maidan.PodiumMaidanTopSupporterQuickAdapter
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.showPodiumLikeInfoDialog
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.ChatDisableReason.CHALLENGE_RUNNING
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.ChatDisableReason.DISABLED_BY_ADMIN
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.ChatDisableReason.DISABLED_BY_MANAGER
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.ChatDisableReason.UNKNOWN
import com.app.messej.ui.home.publictab.podiums.manage.PodiumMaidanSpeakerActionsBottomSheetFragmentDirections
import com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel.ActiveSpeakerUIModel
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.DataFormatHelper.formatDecimalWithRemoveTrailingZeros
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.confirmPaidLikeAction
import com.app.messej.ui.utils.FragmentExtensions.showInsufficientBalanceAlert
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.bumptech.glide.Glide
import com.google.android.material.button.MaterialButton
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.kennyc.view.MultiStateView
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import render.animations.Attention
import render.animations.Render
import render.animations.Zoom
import java.time.Duration
import java.time.ZonedDateTime
import kotlin.math.roundToInt

class PodiumLiveMaidanFragment : PodiumLiveAbstractFragment() {
    override val viewModel: PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)

    val args: PodiumLiveMaidanFragmentArgs by navArgs()

    override val podiumIdArg: String
        get() = args.podiumId

    override val enableScrollForTab: PodiumTab?
        get() = PodiumTab.fromInt(args.enableScrollForTab)

    override fun canExit(): Boolean {
        return if (viewModel.iAmPartOfRunningChallenge.value == true) {
            showAlertOnLiveChallenge(R.string.podium_live_challenge_exit_alert)
            false
        } else true
    }

    override lateinit var binding: FragmentPodiumLiveMaidanBinding

    private lateinit var mTopSupporterListAdapter : PodiumMaidanTopSupporterQuickAdapter

    override val liveBindingElements = object: LiveBindingElements {

        override val liveChat: RecyclerView
            get() = binding.liveChat

        override fun showLocalVideoSurface(show: Boolean) {
            binding.mainScreen.showVideo = show
        }

        override fun onPodiumLoading(loading: Boolean) {
            binding.podiumHeaderMultiStateView.viewState = if (loading) MultiStateView.ViewState.LOADING else MultiStateView.ViewState.CONTENT
        }

        override val likesContainer: ItemPodiumLikesContainerBinding
            get() = binding.likesContainer

        override val chatSend: MaterialButton
            get() = binding.chatSendButton

        override val actionPaidLike: MaterialButton?
            get() = null

        override val actionShare: MaterialButton
            get() = binding.shareButton

        override val actionDecorHolderTop: LinearLayoutCompat?
            get() = null

        override val liveCounter: ViewGroup
            get() = binding.liveCounter

        override val scrollerLayout: SmartRefreshLayout
            get() = binding.refreshLayout

        override val anthemOverlay: ComposeView?
            get() = null

    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_live_maidan, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        Log.w("PLMF", "onViewCreated: joinHidden: ${args.joinHidden}", )
        viewModel.setMaidanJoinHidden(args.joinHidden)
        super.onViewCreated(view, savedInstanceState)
        setup()
        initAdapter()
        addObservers()
    }

    override fun ItemPodiumSpeakerMainBinding.onMainScreenSetup() {
        showCameraToggle = true
        speakerHeader.root.isVisible = false
        chatFlag.isVisible = false
    }

    private fun setup() {
        setEmptyView()

        binding.podiumDp.setOnClickListener {
            showMoreMenu(it)
        }

        binding.mainScreen.clickTarget.setOnClickListener {
            val item = viewModel.mainScreenSpeaker.value ?: return@setOnClickListener
            onSpeakerItemClick(item)
        }

        binding.mainScreenTwo.clickTarget.setOnClickListener {
            val item = viewModel.secondMainScreenSpeaker.value ?: return@setOnClickListener
            onSpeakerItemClick(item)
        }

        binding.likeButton.setOnClickListener {
            confirmLike {
                viewModel.sendMaidanLike()
            }
        }
        binding.giftButton.setOnClickListener {
            val pod = viewModel.podium.value ?: return@setOnClickListener
            Log.w("PLMF", "canSendGift: pod not null")
            val challenge = viewModel.activeChallenge.value ?: return@setOnClickListener
            Log.w("PLMF", "canSendGift: challenge not null")
            if (viewModel.podium.value?.podiumGiftPaused != true) {
                Log.w("PLMF", "canSendGift: podium gift not paused")
                if (viewModel.iAmManager.value!=true) {
                    val manager = viewModel.mainScreenComposeSpeaker.value?.speaker ?: return@setOnClickListener
                    if (!viewModel.isManager(manager.id)) return@setOnClickListener

                    Log.w("PLMF", "canSendGift: ${viewModel.user.canSendGifts()}")
                    checkGiftEligibility(receiver = manager) {
                        val action =
                            PodiumLiveAloneFragmentDirections.actionGlobalGiftFragment(
                                pod.managerId,
                                giftContext = GiftContext.GIFT_PODIUM_MAIDAN,
                                managerId = pod.managerId,
                                giftContextId = pod.id,
                                challengeId = challenge.challengeId,
                                challengeEndTimeStampUTC = challenge.endTimeUTC ?: 0
                            )
                        findNavController().navigateSafe(action)
                    }
                }
            }
        }

        binding.followManagerButton.setOnClickListener {
            viewModel.followManager()
        }

        binding.challengeAgainButton.setOnClickListener {
            Log.d("PodiumLVM", "challengeAgain click: ${viewModel.lastChallenge}")
            val challenge = viewModel.lastChallenge?: return@setOnClickListener
            confirmAction(
                message = if ((challenge.challengeFee ?: 0.0) == 0.0) getString(R.string.podium_maidan_challenge_again_text)
                else getString(R.string.podium_maidan_challenge_again_prompt,challenge.challengeFee.toString()),
                positiveTitle = R.string.common_proceed,
                negativeTitle = R.string.common_cancel
            ) {
                viewModel.challengeAgain()
            }
        }

        binding.challengeEndButton.setOnClickListener {
            exitMaidan()
        }

        binding.presenter = object : DataPresenter() {
            override fun getTimerText(status: Podium.MaidanStatus?): String?  {
                return when(status) {
                    Podium.MaidanStatus.EXTRA_TIME -> getString(R.string.podium_maidan_extra_time)
                    Podium.MaidanStatus.TALKTIME -> getString(R.string.podium_maidan_talk_time)
                    else -> null
                }
            }

            override fun getRelation(relation: FollowerType?): String? {
                return when(relation) {
                    FollowerType.DEAR -> getString(R.string.profile_dear)
                    FollowerType.FAN -> getString(R.string.profile_fan)
                    FollowerType.LIKER -> getString(R.string.profile_liker)
                    null -> null
                }
            }

            override fun getStarType(type: StarType?): String? {
                return when(type) {
                    StarType.SUPERSTAR ->  getString(R.string.profile_superstar)
                    StarType.STAR -> getString(R.string.profile_star)
                    else -> null
                }
            }
        }

        mTopSupporterListAdapter =
            PodiumMaidanTopSupporterQuickAdapter(mutableListOf())
                .apply {
                    animationEnable = false
                    isAnimationFirstOnly = false
                    setDiffCallback(PodiumMaidanTopSupporterQuickAdapter.DiffCallback())
                }

        binding.topSupporters.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            setHasFixedSize(true)
            adapter = mTopSupporterListAdapter
        }

        binding.opponentScore.setOnClickListener {
            if (viewModel.meAsSpeaker.value!=null) return@setOnClickListener
            goToCompetitorPodium()
        }

        binding.supportersHolder.setOnClickListener {
            findNavController().navigateSafe(
                PodiumLiveMaidanFragmentDirections.actionPodiumLiveMaidanFragmentToTopSupportersBottomSheetFragment(
                    podiumId = viewModel.podiumId.value.orEmpty(),
                    challengeId = viewModel.activeChallenge.value?.challengeId.orEmpty()
                )
            )
        }

        binding.challengeNewButton.setOnClickListener {
            findNavController().navigateSafe(PodiumLiveMaidanFragmentDirections.actionPodiumLiveMaidanFragmentToMaidanInviteBottomSheetFragment())
        }

        binding.secondEds.setOnClickListener {
            if(viewModel.podium.value?.canChallenge!=true) return@setOnClickListener
            viewModel.podium.value?.privateInvitedUserId?.let { invitedId ->
                if(invitedId!=viewModel.user.id) {
                    showFlashatDialog {
                        setMessage(R.string.podium_maidan_challenge_error_private)
                    }
                    return@setOnClickListener
                }
            }
            findNavController().navigateSafe(NavLivePodiumDirections.actionGlobalPodiumMaidanChallengeBottomSheetFragment(viewModel.podiumId.value?: return@setOnClickListener, fromPodium = true))
        }

        binding.secondEdsInvite.setOnClickListener {
            findNavController().navigateSafe(PodiumLiveMaidanFragmentDirections.actionPodiumLiveMaidanFragmentToMaidanInviteBottomSheetFragment())
        }

        setupPodiumPromoBoard(binding.ticker)

        binding.likeCounter.setOnClickListener {
            showPodiumLikeInfoDialog()
        }

        setupPlayer()
    }

    abstract class DataPresenter {
        abstract fun getTimerText(status: Podium.MaidanStatus?): String?
        abstract fun getStarType(type: StarType?): String?
        abstract fun getRelation(relation: FollowerType?): String?
    }

    private fun onSpeakerItemClick(item: ActiveSpeakerUIModel) {

        val podium = viewModel.podium.value ?: return
        val action =
            PodiumLiveMaidanFragmentDirections.actionPodiumLiveMaidanFragmentToMaidanPlayerStatsBottomSheetFragment(
                userId = item.speaker.id,
                competitorId = podium.competitorUserId.takeIf { item.speaker.id == podium.managerId }
                    ?: -1)
        findNavController().navigateSafe(action)
    }

    private fun addObservers() {
        viewModel.podium.map {
            it?: return@map null
            Pair(it.id,it.parentId)

        }.distinctUntilChanged().observe(viewLifecycleOwner) {
            it?: return@observe
            val parent = it.second?: return@observe
            ActivePodiumTracker.registerActiveScreen(ActivePodiumTracker.ActivePodiumScreen.MaidanRoom(it.first,parent), viewLifecycleOwner)
        }
        viewModel.iAmPartOfRunningChallenge.observe(viewLifecycleOwner) {
            Log.w("PLVM", "observe: iAmPartOfRunningChallenge $it")
        }

        viewModel.chatDisabled.observe(viewLifecycleOwner) {
            binding.liveChatMultiStateView.viewState = MultiStateView.ViewState.CONTENT

            binding.chatTextBoxMultiStateView.viewState = if (it == null) MultiStateView.ViewState.CONTENT
            else if (viewModel.iAmManager.value == true) MultiStateView.ViewState.CONTENT
            else when (it) {
                CHALLENGE_RUNNING -> MultiStateView.ViewState.CONTENT
                DISABLED_BY_MANAGER -> {
                    chatTextBoxEmptyView.edsEmptyMessage.setText(R.string.podium_comments_disabled_manager)
                    MultiStateView.ViewState.EMPTY
                }

                DISABLED_BY_ADMIN -> {
                    chatTextBoxEmptyView.edsEmptyMessage.setText(R.string.podium_comments_disabled_admin)
                    MultiStateView.ViewState.EMPTY
                }

                UNKNOWN -> {
                    chatTextBoxEmptyView.edsEmptyMessage.setText(R.string.podium_comments_disabled)
                    MultiStateView.ViewState.EMPTY
                }
            }
        }

        viewModel.mainScreenSpeaker.observe(viewLifecycleOwner) {
            Log.w("PLVM", "observe: mainScreenSpeaker $it")
            setMainScreenSpeaker(binding.mainScreen,it)
        }

        viewModel.secondMainScreenSpeaker.observe(viewLifecycleOwner) {
            Log.w("PLVM", "observe: mainScreenSpeakerTwo $it")
            setMainScreenSpeaker(binding.mainScreenTwo,it)
        }
        viewModel.mainScreenSpeakerId.observe(viewLifecycleOwner) {
            it?: return@observe
            binding.mainScreen.setupMainScreen(it)
        }
        viewModel.secondMainScreenSpeakerId.observe(viewLifecycleOwner) {
            Log.w("PLVM", "secondMainScreenSpeakerId: $it")
            it?: return@observe
            binding.mainScreenTwo.setupMainScreen(it)
        }
        viewModel.myVideoIsTurnedOn.observe(viewLifecycleOwner) {
            it?: return@observe
            val screen = if (viewModel.mainScreenSpeakerId.value == viewModel.user.id) binding.mainScreen
            else if(viewModel.secondMainScreenSpeakerId.value == viewModel.user.id) binding.mainScreenTwo
            else return@observe
            screen.setupMainScreen(viewModel.user.id)
        }
        viewModel.showSecondVideoSurface.observe(viewLifecycleOwner) {
            Log.w("PLVM", "observe: showSecondVideoSurface $it")
            binding.mainScreenTwo.showVideo = it
        }

        viewModel.maidanStatusData.observe(viewLifecycleOwner) { status ->
            Log.w("PLVM", "observe: maidanStatus $status")
            stopTimer()
            when(status) {
                is PodiumLiveViewModel.MaidanStatusData.ChallengeActive -> {
                    val cd = Duration.between(ZonedDateTime.now(), status.endTime)
                    binding.timer.setTextColor(ContextCompat.getColor(requireContext(),R.color.textColorOnPrimary))
                    tickerJob = DateTimeUtils.countDownTimerFlow(cd.toMillis()).onEach {
                        binding.timer.text = DateTimeUtils.formatSeconds(it)
                        if (it in 1..10) {
                            binding.timer.setTextColor(ContextCompat.getColor(requireContext(),R.color.colorError))
                            val render = Render(requireContext())
                            render.setAnimation(Attention().Pulse(binding.timer))
                            render.setDuration(500)
                            render.start()
                            playSound(MaidanSoundEffect.TICK)
                        }
                    }.onCompletion {
                        binding.timer.text = DateTimeUtils.formatSeconds(0)
                    }.launchIn(lifecycleScope)
                }
                is PodiumLiveViewModel.MaidanStatusData.TalkTime -> {
                    startTalkTimer(status.startTime) { durationToNow->
                        binding.timer.text = DateTimeUtils.formatDuration(durationToNow)
                        if (durationToNow.seconds%2==0L) {
                            binding.challengeAgainButton.apply {
                                if (!isVisible) return@apply
                                val render = Render(requireContext())
                                render.setAnimation(Attention().Pulse(this))
                                render.setDuration(500)
                                render.start()
                            }
                        }
                    }
                }
                PodiumLiveViewModel.MaidanStatusData.Waiting -> {}
            }
        }

        viewModel.maidanProgress.observe(viewLifecycleOwner) {
            Log.w("PLVM", "observe: maidanProgress $it")
            it?: return@observe
            val width = binding.maidanProgress.width
            val tx = width.toFloat() * it.progress/100
            thumbAnimator?.cancel()
            thumbAnimator = binding.progressThumb.animate().translationX(ViewUtils.localeAwareTranslation(tx)).setDuration(500).setInterpolator(AccelerateDecelerateInterpolator())
            binding.maidanProgress.setProgress(it.progress,true)
        }

        viewModel.activeChallenge.observe(viewLifecycleOwner) { acs ->
//            Log.d("PLVM", "observe: activeChallengeAndScores $acs")
            acs?: return@observe
            mTopSupporterListAdapter.apply {
                acs.topSupporters?.take(20)?.mapIndexed { i, user ->
                    user.apply { supporterRank = i+1 }
                }?.let { sl ->
                    setNewInstance(sl.toMutableList())
                }
            }
            startTimeOutCheckIfRequired(acs)
        }

        viewModel.onChallengeGameOver.observe(viewLifecycleOwner) { scores ->
            Log.w("PLVM_GO","onChallengeGameOver: $scores")
            if (scores.find { sc -> sc.winner }==null) return@observe
            viewModel.mainScreenSpeakerId.value?.let { id ->
                Log.w("PLVM_GO","onChallengeGameOver: play animation for $id")
                scores.find { it.id == id }?.let {
                    binding.mainScreenOneResult.playResultAnimation(it)
                    playSound(if(it.winner) MaidanSoundEffect.WINNER else MaidanSoundEffect.LOSER)
                }
            }
            viewModel.secondMainScreenSpeakerId.value?.let { id ->
                Log.w("PLVM_GO","onChallengeGameOver: play animation for $id")
                scores.find { it.id == id }?.let {
                    binding.mainScreenTwoResult.playResultAnimation(it)
                }
            }
        }

        viewModel.onChallengeContributorRequest.observe(viewLifecycleOwner) { pl ->
            Log.d("PLVM", "onChallengeContributorRequest: ${pl.first} | ${pl.second}")
            showContributorActionAlert(pl.first)
        }
        viewModel.onContributorRequestResponded.observe(viewLifecycleOwner) {
            if (it.second) Toast.makeText(requireContext(), getString(R.string.podium_challenge_contributor_coins_debited, it.first.roundToInt().toString()), Toast.LENGTH_SHORT).show()
            else showToast(R.string.common_rejected)
        }
        viewModel.onContributorRequestRespondError.observe(viewLifecycleOwner) {
            showToast(it)
        }
        viewModel.onMaidanLikeError.observe(viewLifecycleOwner) {
            showToast(it)
        }

        viewModel.onChallengeSetupEvent.observe(viewLifecycleOwner) {
            when (it) {
                is PodiumChallengeSetupEvent.ContributorDeclined -> {
                    showToast(getString(R.string.podium_maidan_decline_invite, viewModel.nickNames.nickNameOrName(it.contributor)))
                }
                else -> {}
            }
        }

        viewModel.onMaidanLikeInsufficientBalance.observe(viewLifecycleOwner) {
            showInsufficientBalanceAlert(getString(R.string.podium_paid_like_balance_error))
        }
    }

    private var tickerJob: Job? = null

    private fun stopTimer() {
        tickerJob?.apply {
            cancel()
            tickerJob = null
        }
    }

    private fun startTalkTimer(startTime: ZonedDateTime, onTick: (Duration) -> Unit) {
        stopTimer()
        tickerJob = lifecycleScope.launch {
            try {
                while (true) {
                    val durationToNow = DateTimeUtils.durationToNowFromPast(startTime) ?: return@launch
                    onTick.invoke(durationToNow)
                    delay(1000)
                }
            } catch (e: Exception) {
                Log.e("PSLT", "startLiveTimer: failed", e)
            } finally {
                Log.w("PSLT", "live timer cancelled")
            }
        }
    }

    private var timeoutJob: Job? = null

    private fun startTimeOutCheckIfRequired(challenge: PodiumChallenge) {
        timeoutJob?.cancel()
        timeoutJob = null
        challenge.contributorRequestedTimeRemaining.let {
            if (it.seconds>0) {
                timeoutJob = DateTimeUtils.countDownTimerFlow(it.toMillis()).onCompletion {
                    viewModel.recheckCompetitorStatus()
                }.launchIn(lifecycleScope)
            }
        }
    }

    private fun AppCompatImageView.playResultAnimation(score: PodiumChallengeScore) {
        Log.w("PLVM_GO","onChallengeGameOver: play animation: $score")
        val gif = if(score.winner) R.drawable.im_maidan_winner_gif else R.drawable.im_maidan_loser_gif
        Glide.with(requireContext()).asGif().load(gif).into(this)
        lifecycleScope.launch {
            isVisible = true
            Render(requireContext()).apply {
                setAnimation(Zoom().In(this@playResultAnimation))
                setDuration(500)
                start()
            }
            delay(10000)
            Render(requireContext()).apply {
                setAnimation(Zoom().Out(this@playResultAnimation))
                setDuration(500)
                start()
            }
            delay(1000)
            isVisible = false
        }
    }

    private var thumbAnimator: ViewPropertyAnimator?=null

    override fun onSecondResume() {
        viewModel.mainScreenSpeakerId.value?.let { mss ->
            binding.mainScreen.setupMainScreen(mss)
        }
        viewModel.secondMainScreenSpeakerId.value?.let { mss ->
            binding.mainScreenTwo.setupMainScreen(mss)
        }
    }

    private lateinit var liveChatEmptyView: LayoutListStateEmptyBinding
    private lateinit var chatTextBoxEmptyView: LayoutPodiumChatPausedEmptyBinding

    fun setEmptyView() {
        val emptyView: View = binding.liveChatMultiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        liveChatEmptyView = LayoutListStateEmptyBinding.bind(emptyView).apply {
            prepare(message = R.string.podium_comments_disabled)
        }

        val textBoxEmptyView: View = binding.chatTextBoxMultiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        chatTextBoxEmptyView = LayoutPodiumChatPausedEmptyBinding.bind(textBoxEmptyView)
    }

    private fun showContributorActionAlert(challenge: PodiumChallenge) {
        val timeRemaining = challenge.contributorRequestedTimeRemaining.seconds
        if (timeRemaining <= 0L) return
        var timerJob: Job? = null
        playTone(R.raw.podium_sound_two)
        val managerName = challenge.facilitator?.name
        val isChallengeAgain = viewModel.iAmSpeaker.value == true
        val message = if (isChallengeAgain) getString(R.string.podium_maidan_challenge_again_request_prompt, managerName, challenge.competitorFee.toString())
        else getString(R.string.podium_maidan_invite_message, managerName, challenge.competitorFee.toString())
        val alert = MaterialAlertDialogBuilder(requireContext())
            .setMessage(message)
            .setCancelable(false)
            .setPositiveButton(getString(R.string.common_accept)) { dialog, _ ->
                viewModel.respondToChallengeAgain(challenge.podiumId,challenge.challengeId,true)
                dialog.dismiss()
                timerJob?.cancel()
            }
            .setNegativeButton(getString(R.string.common_cancel)) { dialog, _ ->
                viewModel.respondToChallengeAgain(challenge.podiumId,challenge.challengeId,false)
                dialog.dismiss()
                timerJob?.cancel()
            }.show()

        timerJob = DateTimeUtils.countDownTimerFlow(timeRemaining*1000).onCompletion {
            if (alert.isShowing) {
                alert.dismiss()
            }
        }.launchIn(lifecycleScope)
    }

    override fun onExit() {
        exitMaidan()
    }

    private fun exitMaidan() {
        if (viewModel.iAmOnMainScreen()) {
            confirmMaidanExit {
                viewModel.exitMaidan()
            }
        } else {
            confirmLeave {
                viewModel.leave()
            }
        }
    }

    private fun confirmMaidanExit(confirm: () -> Unit) {
        val challenge = viewModel.activeChallenge.value
        val status = viewModel.maidanStatusData.value
        val exitWillBeCharged = challenge!=null && status is PodiumLiveViewModel.MaidanStatusData.ChallengeActive
        val maidanProgress = viewModel.maidanProgress.value

        val isFreeMaidan = challenge?.challengeFee == 0.0
        val freeMaidanExitFee = (((maidanProgress?.firstPoints?:0).toDouble()) * (viewModel.user.userRatingPercent.toDouble())) / 1000.0
        val challengeExitFee = (if (isFreeMaidan) freeMaidanExitFee.formatDecimalWithRemoveTrailingZeros() else challenge?.challengeExitFee?.roundToInt().toString())

        confirmAction(
            title = R.string.podium_maidan_exit_prompt_title,
            message = if (exitWillBeCharged) getString(R.string.podium_maidan_exit_prompt_fee,challengeExitFee)
            else getString(R.string.podium_maidan_exit_prompt),
            positiveTitle = R.string.podium_maidan_exit_prompt_ok,
            negativeTitle = R.string.common_cancel
        ) {
            confirm.invoke()
        }
    }

    override fun toLiveChatActions(userId: Int) {
        val action = PodiumLiveMaidanFragmentDirections.actionPodiumLiveMaidanFragmentToPodiumLiveChatActionsBottomSheetFragment(userId)
        findNavController().navigateSafe(action)
    }

    override fun toBuyCamera(buy: Boolean) {
        val action = PodiumLiveMaidanFragmentDirections.actionPodiumLiveMaidanFragmentToPodiumBuyCameraBottomSheetFragment(buy)
        findNavController().navigateSafe(action)
    }

    override fun toLiveUsersList() {
        val action = PodiumLiveMaidanFragmentDirections.actionPodiumLiveMaidanFragmentToPodiumLiveUsersListBottomSheetFragment()
        findNavController().navigateSafe(action)
    }

    override fun toRestrictedUsers() {
        val action = PodiumLiveMaidanFragmentDirections.actionPodiumLiveMaidanFragmentToPodiumBlockedUsersBottomSheetFragment()
        findNavController().navigateSafe(action)
    }

    private fun initAdapter() {

    }

    private fun goToCompetitorPodium() {
       viewModel.leave { left ->
           if(!left) return@leave
           viewModel.podium.value?.competitorPodiumId?.let { cId ->
               val options = NavOptions.Builder()
                   .setPopUpTo(R.id.nav_live_podium, inclusive = true)
                   .build()
               val action = PodiumMaidanSpeakerActionsBottomSheetFragmentDirections.actionGlobalNavLivePodium(podiumId = cId, kind = PodiumKind.MAIDAN.ordinal)
               findNavController().navigateSafe(action,options)
           }
       }
    }

    private fun confirmLike(onConfirm: () -> Unit) {
        viewModel.confirmFollowing { following ->
            if (following) {
                confirmPaidLikeAction {
                    onConfirm.invoke()
                }
            } else {
                showToast(R.string.podium_maidan_like_not_followed)
                return@confirmFollowing
            }
        }
    }

    private fun showAlertOnLiveChallenge(res: Int) {
        MaterialAlertDialogBuilder(requireContext()).setMessage(getString(res)).setCancelable(false).setPositiveButton(getString(R.string.common_ok)) { dialog, _ ->
            dialog.dismiss()
        }.show()
    }

    override fun showMoreMenu(v: View): PopupMenu {

        val popup = PopupMenu(requireContext(), v)
        popup.menuInflater.inflate(R.menu.menu_podium_live_maidan_actions, popup.menu)
        popup.setForceShowIcon(true)
        popup.menu.apply {
            findItem(R.id.podium_timer)?.apply {
                timerMenuItem = this
                setupTimerMenuItem(time = viewModel.liveTimer.value)
            }
            findItem(R.id.action_sessions).title = getString(R.string.podium_maidan_sessions, viewModel.podium.value?.sessionCount?: "0")
        }

        popup.setOnMenuItemClickListener { onMenuClick(it) }
        popup.setOnDismissListener {
            this.popup = null
            timerMenuItem = null
        }
        popup.show()
        this.popup = popup
        return popup
    }

    override fun onMenuClick(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_exit -> {
                onExit()
            }
            R.id.action_share -> {
                sharePodium()
            }
            else -> super.onMenuClick(menuItem)
        }
        return true
    }

    private lateinit var player: MediaPlayer

    enum class MaidanSoundEffect(@RawRes val resourceFile: Int) {
        WINNER(R.raw.maidan_winner),
        LOSER(R.raw.maidan_loser),
        TICK(R.raw.podium_tick)
    }

    private fun setupPlayer() {
        player = MediaPlayer()
    }

    private var currentSoundEffect: MaidanSoundEffect? = null

    private fun playSound(sound: MaidanSoundEffect) {
        player.apply {
            if (currentSoundEffect == sound) {
                seekTo(0)
                start()
                return@apply
            }
            reset()
            val afd: AssetFileDescriptor = resources.openRawResourceFd(sound.resourceFile) ?: return
            setDataSource(afd)
            afd.close()
            currentSoundEffect = sound
            prepare()
            start()
            isLooping = false
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        releasePlayer()
    }

    private fun releasePlayer() {
        try {
            if (player.isPlaying) {
                player.stop()
            }
            player.release()
        } catch (e: IllegalStateException) {
            e.printStackTrace()
        }
    }

    override fun onGiftReceived(gift: SentGiftPayload) {
        Log.w("PLMF", "onGiftReceived Maidan: $gift")
        if ((gift.coins ?: 0) >= PodiumMaidanScoreUpdatePayload.MIN_COINS_FOR_VIDEO) {
            super.onGiftReceived(gift)
        }
    }
}