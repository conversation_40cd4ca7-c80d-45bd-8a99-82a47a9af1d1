package com.app.messej.ui.chat

import android.app.Application
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.TerminalSeparatorType
import androidx.paging.cachedIn
import androidx.paging.insertSeparators
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractChatMessageWithMedia
import com.app.messej.data.model.AttachLocation
import com.app.messej.data.model.ReplyTo
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.TempMedia
import com.app.messej.data.model.api.huddles.PrivateChatUserInfo
import com.app.messej.data.model.entity.PrivateChat
import com.app.messej.data.model.entity.PrivateChatMessage
import com.app.messej.data.model.entity.PrivateChatMessageWithMedia
import com.app.messej.data.model.entity.PrivateChatRoomInfo
import com.app.messej.data.model.entity.PrivateChatWithRoomInfo
import com.app.messej.data.model.enums.ChatTextColor
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.enums.PrivateChatPrivacy
import com.app.messej.data.model.socket.HuddleVoicePlayBackPayload
import com.app.messej.data.model.socket.UserLastSeen
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.repository.SettingsRepository
import com.app.messej.data.socket.repository.PrivateChatEventRepository
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.ResultOf
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class PrivateChatViewModel(application: Application) : BaseChatViewModel(application) {

    private val huddleRepo = HuddlesRepository(getApplication())
    private val settingsRepo = SettingsRepository(getApplication())

    private val eventRepo = PrivateChatEventRepository

    private var roomId= MutableLiveData<String?>(null)
    private var receiverId= MutableLiveData<Int?>(null)

    private var isOfflineChat = MutableLiveData<Boolean>(false)

    private var cachedChat: PrivateChatWithRoomInfo? = null

    private val _chat: LiveData<PrivateChatWithRoomInfo?> = isOfflineChat.switchMap {
        val id = roomId.value?: return@switchMap null
        if(isOfflineChat.value==true) huddleRepo.getPrivateChatWithRoomInfoLiveData(id) else MutableLiveData(cachedChat)
    }

    val isFlaxRateFull: LiveData<Boolean> = _accountDetails.map { accountDetails ->
        accountDetails?.flaxRatePercentage==100
    }


    val messageStatus: LiveData<ChatStatus?> = _chat.map {
        it?: return@map null
        if (it.chat.receiverDetails.deletedAccount) return@map ChatStatus.DeletedUser
        if (it.chat.isAdminMessage) return@map ChatStatus.AdminMessage
        val info = it.roomInfo?: return@map ChatStatus.None
        val receiverInfo = info.userInfo(it.chat.receiver)
        val senderInfo = info.userInfo(user.id)

        return@map if (isUserBanned) ChatStatus.ChatSenderBanned
        else if (info.isBlackListed == true || info.isBannedOrSuspectedBan == true) ChatStatus.ChatReceiverBanned
        else if (info.chatBlockedBySender == true) ChatStatus.ChatBlockedBySender
        else if (info.chatDisabledBySender == true) ChatStatus.ChatDisabledBySender
        else if (info.chatDisabledByReceiver == true) ChatStatus.ChatDisabledByReceiver
        else if (info.chatRoom.chatType == PrivateChat.ChatType.REQUEST
            || it.chat.type == PrivateChat.ChatType.REQUEST) ChatStatus.ChatRequest
//        else if (receiverInfo?.followed==true && senderInfo?.followed==true) ChatStatus.None
        else if (info.userBlockedBySender == true) ChatStatus.UserBlockedBySender
        else if (info.userBlockedByReceiver == true) ChatStatus.UserBlockedByReceiver
//        else if (it.chat.privateMessageTab == PrivateChat.PrivateMessageTabType.INTRUDER) ChatStatus.IntruderChatRequest(it.chat.isIgnored == true)
        else if (it.chat.followedByMe == false) ChatStatus.IntruderChatRequest(it.chat.isIgnored == true)
        else if (senderInfo?.followed == false) ChatStatus.FollowToChat
        else if (receiverInfo?.followed == false) {
            if (senderInfo?.messagesLeft != null && senderInfo.messagesLeft > 0) ChatStatus.LimitedMessages(senderInfo.messagesLeft)
            else ChatStatus.FollowBackToChat
        }
        else if(info.isNewChat == true){
            when (info.privateChatSettings?.whoCanStartPrivateChatEnum) {
                PrivateChatPrivacy.ANYONE -> ChatStatus.None

                PrivateChatPrivacy.NO_ONE -> ChatStatus.StartPrivateChat(PrivateChatPrivacy.NO_ONE)

                PrivateChatPrivacy.ONLY_DEARS -> {
                    if (user.superStarId == receiverId.value && user.premium) {
                        ChatStatus.None
                    } else {
                        ChatStatus.StartPrivateChat(PrivateChatPrivacy.ONLY_DEARS)
                    }
                }

                PrivateChatPrivacy.ONLY_DEARS_FANS -> {
                    if (receiverId.value == user.superStarId) {
                        ChatStatus.None
                    } else {
                        ChatStatus.StartPrivateChat(PrivateChatPrivacy.ONLY_DEARS_FANS)
                    }
                }

                PrivateChatPrivacy.ONLY_PREMIUM -> {
                    if (user.premium) {
                        ChatStatus.None
                    } else {
                        ChatStatus.StartPrivateChat(PrivateChatPrivacy.ONLY_PREMIUM)
                    }
                }

                PrivateChatPrivacy.ONLY_PREMIUM_WITH_100 -> {
                    if (user.premium && isFlaxRateFull.value == true) {
                        ChatStatus.None
                    } else {
                        ChatStatus.StartPrivateChat(PrivateChatPrivacy.ONLY_PREMIUM_WITH_100)
                    }
                }

                PrivateChatPrivacy.ONLY_USERS_WITH_100 -> {
                    if (isFlaxRateFull.value == true) {
                        ChatStatus.None
                    } else {
                        ChatStatus.StartPrivateChat(PrivateChatPrivacy.ONLY_USERS_WITH_100)
                    }
                }

                else -> ChatStatus.None
            }

        }
        else if (info.isNewChat == false) {
            when (info.privateChatSettings?.whoCanContinuePrivateChatEnum) {
                PrivateChatPrivacy.ANYONE -> ChatStatus.None

                PrivateChatPrivacy.NO_ONE -> ChatStatus.StartPrivateChat(PrivateChatPrivacy.NO_ONE)

                PrivateChatPrivacy.ONLY_DEARS -> {
                    if (user.superStarId == receiverId.value && user.premium) {
                        ChatStatus.None
                    } else {
                        ChatStatus.StartPrivateChat(PrivateChatPrivacy.ONLY_DEARS)
                    }
                }

                PrivateChatPrivacy.ONLY_DEARS_FANS -> {
                    if (receiverId.value == user.superStarId) {
                        ChatStatus.None
                    } else {
                        ChatStatus.StartPrivateChat(PrivateChatPrivacy.ONLY_DEARS_FANS)
                    }
                }

                PrivateChatPrivacy.ONLY_PREMIUM -> {
                    if (user.premium) {
                        ChatStatus.None
                    } else {
                        ChatStatus.StartPrivateChat(PrivateChatPrivacy.ONLY_PREMIUM)
                    }
                }

                PrivateChatPrivacy.ONLY_PREMIUM_WITH_100 -> {
                    if (user.premium && isFlaxRateFull.value == true) {
                        ChatStatus.None
                    } else {
                        ChatStatus.StartPrivateChat(PrivateChatPrivacy.ONLY_PREMIUM_WITH_100)
                    }
                }

                PrivateChatPrivacy.ONLY_USERS_WITH_100 -> {
                    if (isFlaxRateFull.value == true) {
                        ChatStatus.None
                    } else {
                        ChatStatus.StartPrivateChat(PrivateChatPrivacy.ONLY_USERS_WITH_100)
                    }
                }

                else -> ChatStatus.None
            }
        }
        else ChatStatus.None
    }

    override val canInteractWithChat = messageStatus.map {
        it?: return@map false
        return@map it is ChatStatus.None || it is ChatStatus.LimitedMessages
    }

    val isAdminMessage = _chat.map {
        it?.chat?.isAdminMessage == true
    }

    /*(it.chat.receiverDetails.citizenship == UserCitizenship.VISITOR) ||*/
    var isGiftBlocked = _chat.map { it?.let { chat ->
        if (it.chat.isAdminMessage) return@map true /* For Admin Private Message No need to check the below conditions. always gift blocked here*/
        val info = chat.roomInfo ?: return@map false
        val senderInfo = info.userInfo(user.id)
        ((it.chat.type == PrivateChat.ChatType.REQUEST) ||(info.chatDisabledBySender == true) || (senderInfo?.followed == false) || (it.chat.receiverDetails.citizenship?.isVisitor == true) || (isVisitor) || (it.chat.receiverDetails.citizenship?.isGolden == true)||
        (info.chatBlockedBySender == true) || (info.chatDisabledByReceiver == true) || (it.chat.followedByMe == false) || (info.userBlockedBySender == true) || (info.userBlockedByReceiver == true) || (it.chat.receiverDetails.deletedAccount)
                || (info.userInfo(user.id)?.messagesLeft ?: 0) < 0 || (it.chat.receiverDetails.blockedByAdminOrEmpowerUser == true)|| (it.chat.receiverDetails.blockedByLeader == true))
    } ?: true }
    override val deleteTimeout = accountRepo.getAccountDetailsFlow().map {
        it?.privateMessageDeleteTimeoutInSeconds
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly,
        initialValue = null
    )

    private val nickName = receiverId.switchMap {
        if(it!=null) profileRepo.getNickNameLiveData(it) else null
    }

    val chat: MediatorLiveData<PrivateChat?> by lazy {
        val med = MediatorLiveData<PrivateChat?>()
        fun update() {
            val chat = _chat.value?.chat
            chat?.let {
                nickName.value?.nickName?.let { nn ->
                    if(nn.isNotEmpty()) {
                        it.receiverDetails.name = nn
                    }
                }
            }
            med.postValue(chat)
        }
        med.addSource(_chat) { update() }
        med.addSource(nickName) { update() }
        med
    }

    override val showChats = chat.map {
        return@map true
    }

    fun setRoomId(id: String, receiver: Int) {
//        roomId.value = null
        viewModelScope.launch(Dispatchers.IO) {
            _dataLoading.postValue(true)
            var offline = false
            val offlineChat = huddleRepo.getPrivateChatWithRoomInfo(id)

            if (offlineChat != null) {
                offline = true
                cachedChat = offlineChat
                val last = huddleRepo.getLastReadMessageByUnreadCount(offlineChat.chat)
                Log.d("PCVM", "setRoomId: last read message is ${last?.displayMessage}")
                lastReadMessage = last?.messageId
                withContext(Dispatchers.Main) {
                    roomId.value = id
                    receiverId.value = receiver
                    isOfflineChat.value = offline
                }
                if (offlineChat.roomInfo != null) {
                    _dataLoading.postValue(false)
                }
                getPrivateChatRoomInfo(receiver)?.let {
                    cachedChat?.roomInfo = it
                    huddleRepo.insertRoomInfo(it)
                }
            } else {
                var onlineChat = getPrivateChatInfo(receiver)
                val privateRoomInfo = getPrivateChatRoomInfo(receiver) ?: return@launch
                //TODO show loading - handle network fail - toast and pop
                if (onlineChat?.id == null) {
                    val receiverInfo = getPrivateReceiverInfo(receiver) ?: return@launch
                    onlineChat = PrivateChat(
                        id = privateRoomInfo.chatRoom.id,
                        sender = user.id,
                        receiver = receiver,
                        receiverDetails = SenderDetails(id = receiverInfo.id, _username = receiverInfo.username, name = receiverInfo.name, premium = receiverInfo.premiumUser, verified = receiverInfo.verified, thumbnail = receiverInfo.thumbnail, citizenship = receiverInfo.citizenship),
                        roomId = privateRoomInfo.chatRoom.id
                    )
                }
                cachedChat = PrivateChatWithRoomInfo(onlineChat,privateRoomInfo)
                offline = if (privateRoomInfo.chatRoom.roomStatus == "ACTIVE"){
                    huddleRepo.savePrivateChat(onlineChat)
                    huddleRepo.insertRoomInfo(privateRoomInfo)
                    true
                } else false
                withContext(Dispatchers.Main) {
                    roomId.value = id
                    receiverId.value = receiver
                    isOfflineChat.value = offline
                }
            }
            _dataLoading.postValue(false)
            if (offline) {
                announceChatEntry(true)
                doInitialSetup()
            }

            cachedChat?.chat?.let {
                if(it.type != PrivateChat.ChatType.REQUEST) {
                    val last = huddleRepo.getOldestUnreadMessage(it)
                    last?.messageId?.let { messageId ->
                        eventRepo.setReadAll(it.roomId, messageId)
                    }
                }
            }
        }
    }

    private suspend fun getPrivateChatInfo(receiver: Int): PrivateChat? {
        return when (val result: ResultOf<PrivateChat> = huddleRepo.getPrivateChatInfo(receiver)) {
            is ResultOf.APIError -> {
                null
            }
            is ResultOf.Error -> {
                null
            }
            is ResultOf.Success -> {
                result.value
            }
        }
    }
    private suspend fun getPrivateChatRoomInfo(receiver: Int): PrivateChatRoomInfo? {
        return when (val result: ResultOf<PrivateChatRoomInfo> = huddleRepo.getPrivateChatInfoForReceiver(receiver)) {
            is ResultOf.APIError -> {
                null
            }
            is ResultOf.Error -> {
                null
            }
            is ResultOf.Success -> {
                result.value
            }
        }
    }

    private suspend fun getPrivateReceiverInfo(receiver: Int): PrivateChatUserInfo? {
        return when (val result: ResultOf<PrivateChatUserInfo> = huddleRepo.getPrivateChatReceiverInfo(receiver)) {
            is ResultOf.APIError -> {
                null
            }
            is ResultOf.Error -> {
                null
            }
            is ResultOf.Success -> {
                result.value
            }
        }
    }

    private fun doInitialSetup() {
        viewModelScope.launch(Dispatchers.IO) {
            cachedChat?.let {
                // TODO reimplement this logic
//                if (it.huddleType == HuddleType.PRIVATE) {
//                    huddleEventRepo.setReadAll(accountRepo.user.id, it.lastMessage?.messageId ?: "")
//                } else if (isOfflineHuddle && it.huddleType == HuddleType.PUBLIC) {
//                    huddleEventRepo.announceChatEnter(it.id)
//                }
//                huddleRepo.resetUnreadCount(it.id)
            }
        }
    }

    private var lastReadMessage: String? = null

    override val _chatList = roomId.switchMap {
        it ?: return@switchMap null
        huddleRepo.getPrivateChatsPager(it).liveData.map { pagingData: PagingData<PrivateChatMessageWithMedia> ->
            val pgData = pagingData.map { msg ->
                ChatMessageUIModel.ChatMessageModel(msg, false)
            }
            checkIfNeedsSending(pgData)
            pgData.setShowName().insertDateSeparators()
                //insert unread header
                .insertSeparators(TerminalSeparatorType.SOURCE_COMPLETE) { before, after ->
                    if (after != null && after is ChatMessageUIModel.ChatMessageModel && before != null && (after.chat.message.messageId == lastReadMessage)) {
                        Log.d("PCVM", "inserting unread(${cachedChat?.chat?.unreadCount}) after ${after.message.displayMessage}")
                        ChatMessageUIModel.UnreadSeparatorModel(cachedChat?.chat?.unreadCount ?: 0)
                    } else null
                }
        }.cachedIn(viewModelScope)
    }


    override fun likeItem(item: AbstractChatMessage) {
        // Not required for private chat
    }

    private val _lastSeenInfo = eventRepo.lastSeenInfo.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    val lastSeenInfo : LiveData<UserLastSeen?> by lazy {
        val med = MediatorLiveData<UserLastSeen?>(null)
        fun update(){
            val rec = receiverId.value
            med.postValue(if (rec!=null)_lastSeenInfo.value?.getOrDefault(rec, null) else null)
        }
        med.addSource(_lastSeenInfo) {update()}
        med.addSource(receiverId) {update()}
        med
    }

    override fun playMedia(message: AbstractChatMessageWithMedia, pos: Int) {
        super.playMedia(message, pos)
        if(message.message.mediaType!=MediaType.AUDIO) return
        message.message.roomId?.let { roomId->
            val playBack= HuddleVoicePlayBackPayload(messageId = message.message.messageId, playedBy = user.id, roomId = roomId, type = "chat")
            eventRepo.privateChatVoicePlayBackStatus(playBack)
        }
    }

    override fun onReplyClick(item: AbstractChatMessage) {
        super.onReplyClick(item)
        if(item.mediaType!=MediaType.AUDIO) return
        item.roomId?.let { roomId->
            val playBack= HuddleVoicePlayBackPayload(messageId = item.messageId, playedBy = user.id, roomId = roomId, type = "chat")
            eventRepo.privateChatVoicePlayBackStatus(playBack)
        }
    }

    private suspend fun performMessageUpload(msg: PrivateChatMessageWithMedia) = chatRepo.sendPrivateChatMessage(msg)

    override suspend fun sendMessage(message: String, media: TempMedia?, replyTo: ReplyTo?, location: AttachLocation?, color: ChatTextColor?): AbstractChatMessage? {
        cachedChat ?: return null
        val chat = _chat.value ?: return null
        if (isOfflineChat.value == false) {
            huddleRepo.savePrivateChat(chat.chat)
            chat.roomInfo?.let { huddleRepo.insertRoomInfo(it) }
            isOfflineChat.postValue(true)
        }
        chat.roomInfo?.let { roomInfo ->
            val senderInfo = roomInfo.userInfo(chat.chat.sender)
            if (roomInfo.userInfo(chat.chat.receiver)?.followed == false) {
                senderInfo?.let {
                    val msgLeft = it.messagesLeft ?: 0
                    if (msgLeft > 0) {
                        val newInfo = it.copy(
                            messagesLeft = msgLeft - 1
                        )
                        roomInfo.setUserInfo(chat.chat.sender, newInfo)
                        huddleRepo.insertRoomInfo(roomInfo)
                    }
                }
            }
        }
        Firebase.crashlytics.log("send message ${media?.path}")
        val msg = chatRepo.createChatMessage(chat.chat, message, media, replyTo, location, color)
        Log.w("PCVM", "sendMessage: chat returned from db: $msg" )
        if (msg.message.messageType.canSendInstantly()) {
            // Trigger a send immediately as there is no media to send
            chatRepo.sendPrivateChatMessage(msg)
        }
        return msg.message
    }

    override fun onTriggerUpload(msg: AbstractChatMessageWithMedia) {
        viewModelScope.launch(Dispatchers.IO) {
            performMessageUpload(msg as PrivateChatMessageWithMedia)
        }
    }

    override val typingListener = object: TypingListener {
        override fun onTyping(typing: Boolean) {
            // TODO send typing event
//            huddleEventRepo.announceChatTyping(huddleID.value!!,typing)
        }
    }

    fun deleteSelection(forEveryone: Boolean) {
        val list = _selectedChatsList.map{ it as PrivateChatMessage }
        if (list.isEmpty()) {
            exitSelectionMode()
            return
        }
        eventRepo.deletePrivateChatMessage(list,forEveryone)
        exitSelectionMode()
    }



    fun deleteMessage(msg: AbstractChatMessage,forEveryone: Boolean) {
        eventRepo.deletePrivateChatMessage(listOf(msg as PrivateChatMessage), forEveryone)
    }

    private var inChat = false

    fun announceChatEntry(enter: Boolean) {
        if (inChat==enter) return
        cachedChat?.chat?.let {
            inChat = enter
            viewModelScope.launch(Dispatchers.IO) {
                huddleRepo.resetPrivateChatUnreadCount(it.roomId)
                eventRepo.announceOnlineStatus(it.sender, enter)
                eventRepo.announceOnlineStatus(it.receiver, enter) //sending event for both sender and receiver as per BE
            }
        }
    }

    override fun getSenderForReply(msg: AbstractChatMessage): SenderDetails? {
        return if((msg as PrivateChatMessage).sender==user.id) {
            SenderDetails.from(user)
        } else {
            cachedChat?.chat?.receiverDetails
        }
    }

    private val _chatActionLoading = MutableLiveData(false)
    val chatActionLoading: LiveData<Boolean> = _chatActionLoading

    fun followUser() {
        _chatActionLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO){
            val id = receiverId.value?: return@launch
            val profileResponse = profileRepo.getPublicUserDetails(id)
            if (profileResponse !is ResultOf.Success) return@launch
            val user = profileResponse.value
            when(val result: ResultOf<String> = if (_chat.value?.chat?.privateMessageTab == PrivateChat.PrivateMessageTabType.INTRUDER)
                profileRepo.followIntruder(user, _chat.value?.chat!!) else profileRepo.followUser(user)) {
                is ResultOf.APIError -> {
                    _chatActionLoading.postValue(false)
                }
                is ResultOf.Error -> {
                    _chatActionLoading.postValue(false)
                }
                is ResultOf.Success -> {
                    delay(500)
                    setRoomId(roomId.value!!,receiverId.value!!)
                }
            }
        }
    }

    fun acceptChatRequest(){
        viewModelScope.launch(Dispatchers.IO){
            when(val result: ResultOf<String> =
                huddleRepo.privateChatRequestAcceptAction(roomId.value!!)){
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {

                }
                is ResultOf.Success -> {
                    delay(500)
                    setRoomId(roomId.value!!,receiverId.value!!)
                }
            }
        }
    }

    val onDeleteChat = LiveEvent<Boolean>()

    fun deleteChatRequest(){
        viewModelScope.launch(Dispatchers.IO){
            when(val result: ResultOf<String> =
                huddleRepo.privateChatRequestDeleteAction(roomId.value!!)){
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {

                }
                is ResultOf.Success -> {
                    onDeleteChat.postValue(true)
                }
            }
        }
    }

    fun deleteRestrictedUserChat(){
        viewModelScope.launch(Dispatchers.IO){
            when(huddleRepo.deleteRestrictedUserChat(roomId.value!!)){
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {

                }
                is ResultOf.Success -> {
                    onDeleteChat.postValue(true)
                }
            }
        }
    }

    fun unblockPrivateMessages() {
        _chatActionLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            val room = roomId.value?: return@launch
            val receiver = receiverId.value?: return@launch
            when (val result: ResultOf<String> = profileRepo.unblockUser(receiver)) {
                is ResultOf.Success -> {
                    delay(500)
                    setRoomId(room,receiver)
                }
                is ResultOf.APIError -> {
                    _chatActionLoading.postValue(false)
                }
                is ResultOf.Error -> {
                    _chatActionLoading.postValue(false)
                }
            }
        }
    }

    fun blockChat(){
        viewModelScope.launch {
            val room = roomId.value?: return@launch
            val id = receiverId.value ?: return@launch
            val sent = eventRepo.toggleChatBlock(id)
            if (sent) {
                huddleRepo.deletePrivateChat(room)
                onDeleteChat.postValue(true)
            }
        }
    }

    fun unblockChat(){
        viewModelScope.launch {
            val id = receiverId.value ?: return@launch
            val sent = eventRepo.toggleChatBlock(id)
            if (sent) {
                val room = roomId.value ?: return@launch
                delay(500)
                setRoomId(room, id)
            }
        }
    }

    val chatIgnored = LiveEvent<Boolean>()
    fun ignoreChat(){
        viewModelScope.launch {
            val room = roomId.value?: return@launch
            val id = chat.value?.receiver ?: return@launch
            val type = chat.value?.type?: return@launch
            val sent = eventRepo.ignoreChat(id, type, room)
            if (sent) chatIgnored.postValue(true)
        }
    }

    fun clearPrivateMessages() {
        viewModelScope.launch(Dispatchers.IO){
            val room = roomId.value?: return@launch
            val receiver = receiverId.value?: return@launch
            when(val result: ResultOf<String> = chatRepo.clearPrivateChatMessages(room)){
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {

                }
                is ResultOf.Success -> {
                    delay(500)
                    setRoomId(room,receiver)
                }
            }
        }
    }

    //Delete should be enabled for both sender and receiver
    override val canDeleteSelection = _selectedChats.map { it.isNotEmpty() && it.all { ch -> !ch.deleted } }

    override fun canDeleteMessage(msg: AbstractChatMessageWithMedia) = !msg.message.deleted

    override val canDeleteSelectionForEveryone = _selectedChats.map {
        val timeout = deleteTimeout.value
        it.all { ch ->
            //Delete all should be available only for the sender
            if (ch.sender == user.id) {
                return@map if (timeout == null) {
                    true
                } else {
                    // The time should not be exceed the timeout value
                    (DateTimeUtils.durationToNowFromPast(ch.parsedCreatedTime)?.seconds ?: 0) < timeout
                }
            } else false
        }
    }

    override fun canDeleteMessageForEveryOne(msg: AbstractChatMessageWithMedia): Boolean {
        msg.apply {
            val timeout = deleteTimeout.value
            Log.w("BCVM", "delete timeout: $timeout")
            return if (timeout == null) {
                true
            } else {
                (DateTimeUtils.durationToNowFromPast(message.parsedCreatedTime)?.seconds ?: 0) < timeout && message.sender == user.id
            }
        }
    }

    val userDeleted = LiveEvent<Boolean>()
    fun deleteUser() {
        roomId.value?: return
        if (chat.value != null && chat.value!!.type == PrivateChat.ChatType.REQUEST) deleteRestrictedUserChat()
        else viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<String> = huddleRepo.privateChatIntruderDeleteAction(roomId.value!!)) {
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
                is ResultOf.Success -> {
                    userDeleted.postValue(true)
                }
            }
        }
    }

    init {
        viewModelScope.launch(Dispatchers.IO) {
            settingsRepo.getPrivacyStatus(force = false)
        }
        viewModelScope.launch {
            settingsRepo.privacySettingsFlow().collect {
                voiceRecordLimit = it?.otomVoiceMessageLengthInSeconds
            }
        }
        viewModelScope.launch {
            //To trigger chat read event
            eventRepo.onReadAction.filter { it.roomId == roomId.value }.collect {
                eventRepo.setReadStatus(it.roomId, it.messageId)
            }
        }
        viewModelScope.launch {
            eventRepo.onRoomUpdate.filter { it.roomId == roomId.value }.collect {
                if (isOfflineChat.value == false) {
                    cachedChat?.roomInfo?.chatRoom = it.chatRoomInfo
                    isOfflineChat.postValue(false)
                }
            }
        }
    }

    sealed class ChatStatus {
        data object None : ChatStatus()
        data object FollowToChat : ChatStatus()
        data class LimitedMessages(val remainingMessages: Int) : ChatStatus()
        data object FollowBackToChat : ChatStatus()
        data object ChatRequest : ChatStatus()
        data object Unblock : ChatStatus()
        data object DeletedUser : ChatStatus()
        data object ChatBlockedBySender : ChatStatus()
        data object UserBlockedBySender : ChatStatus()
        data object UserBlockedByReceiver : ChatStatus()
        data object ChatDisabledBySender : ChatStatus()
        data object ChatDisabledByReceiver : ChatStatus()
        data class IntruderChatRequest(val isIgnored: Boolean) : ChatStatus()
        data object ChatReceiverBanned : ChatStatus()
        data object ChatSenderBanned : ChatStatus()
        data object AdminMessage : ChatStatus()

        data class StartPrivateChat(val privacyEnum: PrivateChatPrivacy?) : ChatStatus()

        data class ContinuePrivateChat(val privacyEnum: PrivateChatPrivacy?) : ChatStatus()
    }
}