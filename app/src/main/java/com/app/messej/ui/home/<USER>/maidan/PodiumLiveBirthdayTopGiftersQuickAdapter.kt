package com.app.messej.ui.home.publictab.maidan

import androidx.recyclerview.widget.DiffUtil
import com.app.messej.R
import com.app.messej.data.model.entity.Podium
import com.app.messej.databinding.ItemPodiumBirthdayTopGiftersBinding
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder

class PodiumLiveBirthdayTopGiftersQuickAdapter(data: MutableList<Podium.TopGifters>):
    BaseQuickAdapter<Podium.TopGifters, BaseDataBindingHolder<ItemPodiumBirthdayTopGiftersBinding>>(R.layout.item_podium_birthday_top_gifters, data) {

    class DiffCallback: DiffUtil.ItemCallback<Podium.TopGifters>() {
        override fun areItemsTheSame(oldItem: Podium.TopGifters, newItem: Podium.TopGifters): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Podium.TopGifters, newItem: Podium.TopGifters): Boolean {
            return oldItem == newItem
        }
    }
    override fun convert(
        holder: BaseDataBindingHolder<ItemPodiumBirthdayTopGiftersBinding>,
        item: Podium.TopGifters
    ) {
        holder.dataBinding?.topGifter = item
    }
}