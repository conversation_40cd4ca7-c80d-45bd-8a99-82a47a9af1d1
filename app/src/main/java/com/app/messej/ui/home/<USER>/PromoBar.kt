package com.app.messej.ui.home.promobar

import androidx.compose.animation.AnimatedContent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.SubcomposeLayout
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.app.messej.R
import com.app.messej.data.model.UserIdAndName
import com.app.messej.data.model.enums.AppLocale
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.socket.PromoAnnouncement
import com.app.messej.ui.utils.DataFormatHelper
import com.app.messej.ui.utils.EnumUtils.displayText


//@Preview
@Composable
fun PromoBarPreview() {
    Column {
        PromoBar(
            PromoAnnouncement.AdminMessage("هذه رسالة اختبار لاختبار اللوحة الترويجية ويجب أن تكون طويلة إلى حد ما  لتشغيل رسوم متحركة للتمرير", "arabic")
        )
        Spacer(modifier = Modifier.height(8.dp))
        PromoBar(
            PromoAnnouncement.AdminMessage(" HELLO RECENTLY هذه إلى للتمرير", "arabic")
        )
        Spacer(modifier = Modifier.height(8.dp))
        PromoBar(
            PromoAnnouncement.AdminMessage("This is a Test message for testing Promo board and this has to be rather long to trigger a scroll animation", "english")
        )
        Spacer(modifier = Modifier.height(8.dp))
        PromoBar(
            PromoAnnouncement.UserUpgrade(0, "John Doe", UserCitizenship.OFFICER)
        )
        Spacer(modifier = Modifier.height(8.dp))
        PromoBar(
            PromoAnnouncement.TopUserPodium("0", "متحركة للتمرير", 69)
        )
        Spacer(modifier = Modifier.height(8.dp))
        PromoBar(
            PromoAnnouncement.TopLikedPodium("0", "Podie McPodiumFace", 69)
        )
        Spacer(modifier = Modifier.height(8.dp))
        PromoBar(
            PromoAnnouncement.MaidanResult(1, "Peter Parker", 2, "Mary Jane", "dfdfgs")
        )
        Spacer(modifier = Modifier.height(8.dp))
        PromoBar(
            PromoAnnouncement.CaseReported(1)
        )
        Spacer(modifier = Modifier.height(8.dp))
        PromoBar(
            PromoAnnouncement.Birthday(
                userType = PromoAnnouncement.Birthday.BirthdayType.PRESIDENT,
                user = UserIdAndName (1204, "Appu")
            )
        )
        Spacer(modifier = Modifier.height(8.dp))
        PromoBar(
            PromoAnnouncement.Birthday(
                userType = PromoAnnouncement.Birthday.BirthdayType.MINISTER,
                user = UserIdAndName (1204, "Appu")
            )
        )
    }
}

@Preview
@Composable
fun PromoBarPreviewSingle() {
    Column {
        PromoBar(
            PromoAnnouncement.MaidanResult(1, "Peter Parker", 2, "Mary Jane", "dfdfgs")
        )
    }
}

@Composable
fun PromoBar(message: PromoAnnouncement?, onDelayCalculated: ((Long) -> Unit)? = null, onClick: (PromoAnnouncement) -> Unit = {}) {

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .background(color = colorResource(R.color.colorAlwaysDarkSurfaceSecondary))
    ) {

        val borderWidth = 6.dp

        Column {

            Row {
                Image(
                    painter = painterResource(R.drawable.im_podium_board_corner_top_left),
                    contentDescription = "corner top left",
                    modifier = Modifier
                        .width(borderWidth)
                        .height(borderWidth)
                )

                Image(
                    painter = painterResource(R.drawable.im_podium_board_border_top),
                    contentDescription = "border top",
                    modifier = Modifier
                        .height(borderWidth)
                        .weight(1f),
                    contentScale = ContentScale.FillBounds
                )

                Image(
                    painter = painterResource(R.drawable.im_podium_board_corner_top_right),
                    contentDescription = "corner top right",
                    modifier = Modifier
                        .width(borderWidth)
                        .height(borderWidth)
                )
            }

            Row(
                modifier = Modifier.height(IntrinsicSize.Min)
            ) {
                Box(modifier = Modifier
                    .width(borderWidth)
                    .fillMaxHeight()) {
                    Image(
                        painter = painterResource(R.drawable.im_podium_board_border_left),
                        contentDescription = "border left",
                        modifier = Modifier.matchParentSize(),
                        contentScale = ContentScale.FillBounds
                    )
                }

                var size by remember { mutableStateOf(0.dp) }
                val density = LocalDensity.current

                Box(modifier = Modifier
                    .weight(1f)
                    .clickable(enabled = message != null, onClick = {
                        if (message != null) {
                            onClick(message)
                        }
                    })
                    .onSizeChanged {
                        with(density) {
                            size = it.width.toDp()
                        }
                    }) {
                    val text = when (message) {
                        is PromoAnnouncement.AdminMessage -> message.message.orEmpty().lines().joinToString(" ")
                        is PromoAnnouncement.UserUpgrade -> {
                            val citizenship = stringResource(message.citizenship.displayText())
                            stringResource(R.string.promo_announcement_user_upgrade, message.name, citizenship)
                        }
                        is PromoAnnouncement.TopLikedPodium -> stringResource(R.string.promo_announcement_top_likes, message.name, DataFormatHelper.numberToK(message.likes))
                        is PromoAnnouncement.TopUserPodium -> stringResource(R.string.promo_announcement_top_users, message.name, DataFormatHelper.numberToK(message.liveUsers))
                        is PromoAnnouncement.MaidanResult -> stringResource(R.string.promo_announcement_maidan_result, message.winnerName, message.loserName)
                        is PromoAnnouncement.CaseReported -> stringResource(R.string.promo_announcement_new_case)
                        is PromoAnnouncement.StrongestTribe -> stringResource(R.string.promo_announcement_tribe, message.tribeName, message.managerName, DataFormatHelper.numberToK(message.members))
                        is PromoAnnouncement.MostGenerousUser -> stringResource(R.string.promo_announcement_generous_user, message.name, DataFormatHelper.numberToK(message.coinsContributed))
                        is PromoAnnouncement.MostGenerousPodium -> stringResource(R.string.promo_announcement_generous_podium, message.name, DataFormatHelper.numberToK(message.coinsContributed))
                        is PromoAnnouncement.AdminAnnouncementScheduled -> message.message.orEmpty().lines().joinToString(" ")
                        is PromoAnnouncement.Birthday -> {
                            when (message.userType){
                                PromoAnnouncement.Birthday.BirthdayType.PRESIDENT -> {
                                    stringResource(R.string.promo_announcement_birthday_president, message.user.name)
                                }
                                PromoAnnouncement.Birthday.BirthdayType.MINISTER -> {
                                    stringResource(R.string.promo_announcement_birthday_minister, message.user.name)
                                }
                            }
                        }
                        is PromoAnnouncement.SocialCases -> stringResource(id = R.string.promo_announcement_social_case, "${message.caseCount ?: 0}")
                        null -> stringResource(R.string.podium_announcement_title)
                    }

                    val colorRes = when (message) {
                        is PromoAnnouncement.AdminMessage -> R.color.colorPromoBoardRed
                        is PromoAnnouncement.TopLikedPodium -> R.color.colorPromoBoardPink
                        is PromoAnnouncement.TopUserPodium -> R.color.colorPromoBoardPurple
                        is PromoAnnouncement.UserUpgrade -> R.color.colorPromoBoardYellow
                        is PromoAnnouncement.MaidanResult -> R.color.colorPromoBoardOrange
                        is PromoAnnouncement.CaseReported -> R.color.colorPromoBoardBlue
                        is PromoAnnouncement.StrongestTribe -> R.color.colorPromoBoardGreen
                        is PromoAnnouncement.MostGenerousUser -> R.color.colorPromoBoardGreen
                        is PromoAnnouncement.MostGenerousPodium -> R.color.colorPromoBoardGreen
                        is PromoAnnouncement.AdminAnnouncementScheduled -> R.color.colorPromoBoardRed
                        is PromoAnnouncement.Birthday -> {
                            when(message.userType) {
                                PromoAnnouncement.Birthday.BirthdayType.PRESIDENT -> R.color.colorPresidentHuddleTagGolden
                                PromoAnnouncement.Birthday.BirthdayType.MINISTER -> R.color.colorPromoBoardPurple
                            }
                        }
                        is PromoAnnouncement.SocialCases -> R.color.colorPromoBoardGreen
                        null -> R.color.colorPromoBoardRed
                    }
                    AnimatedContent(
                        targetState = text,
                        label = "animated content"
                    ) { tt ->
                        val annotatedString = formatByLanguage(tt)
                        FadedMarqueeText(
                            modifier = Modifier
                                .fillMaxWidth()
//                                .wrapContentHeight(),
                                .height(18.dp),
                            text = annotatedString,
                            style = TextStyle(
                                color = colorResource(colorRes),
//                                shadow = Shadow(
//                                    color = colorResource(colorRes), offset = Offset(0f, 0f), blurRadius = 5f
//                                ),
                                fontSize = 16.sp,
                                letterSpacing = 1.sp,
                                fontFamily = FontFamily(Font(R.font.dotspot_regular))
                            ),
                            forceDirection = if(message is PromoAnnouncement.AdminMessage) {
                                if (message.isRtl) LayoutDirection.Rtl else LayoutDirection.Ltr
                            } else if(message is PromoAnnouncement.AdminAnnouncementScheduled) {
                                if (message.isRtl) LayoutDirection.Rtl else LayoutDirection.Ltr
                            } else null,
//                            forceDirection = LayoutDirection.Rtl,
                            spacerWidthDp = size
                        ){
                            onDelayCalculated?.invoke(it)
                        }
                    }
                }

                Box(modifier = Modifier
                    .fillMaxHeight()
                    .width(borderWidth)) {
                    Image(
                        painter = painterResource(R.drawable.im_podium_board_border_right),
                        contentDescription = "border right",
                        modifier = Modifier.matchParentSize(),
                        contentScale = ContentScale.FillBounds
                    )
                }
            }

            Row {
                Image(
                    painter = painterResource(R.drawable.im_podium_board_corner_bottom_left),
                    contentDescription = "corner bottom left",
                    modifier = Modifier
                        .width(borderWidth)
                        .height(borderWidth)
                )

                Image(
                    painter = painterResource(R.drawable.im_podium_board_border_bottom),
                    contentDescription = "border bottom",
                    modifier = Modifier
                        .height(borderWidth)
                        .weight(1f),
                    contentScale = ContentScale.FillBounds
                )

                Image(
                    painter = painterResource(R.drawable.im_podium_board_corner_bottom_right),
                    contentDescription = "corner bottom right",
                    modifier = Modifier
                        .width(borderWidth)
                        .height(borderWidth)
                )
            }

        }
    }
}

fun String.toAnnotatedString(): AnnotatedString {
    return buildAnnotatedString {
        append(this@toAnnotatedString)
    }
}

private fun formatByLanguage(input: String): AnnotatedString {
    val arabicRanges =
        listOf(
            '\u0600'..'\u06FF',
            '\u0750'..'\u077F',
            '\u08A0'..'\u08FF',
            '\uFB50'..'\uFDFF',
            '\uFE70'..'\uFEFF'
        )

    val result = mutableListOf<Pair<AppLocale,String>>()
    val currentSegment = StringBuilder()
    var lastLanguage: AppLocale? = null

    for (char in input) {
        val currentLanguage = when {
            char == ' ' -> lastLanguage
            arabicRanges.any { char in it } -> AppLocale.ARABIC
            else -> AppLocale.ENGLISH
        }

        if (currentLanguage == lastLanguage || lastLanguage == null) {
            currentSegment.append(char)
        } else {
            result.add(Pair(lastLanguage,currentSegment.toString()))
            currentSegment.clear()
            currentSegment.append(char)
        }

        lastLanguage = currentLanguage
    }

    // Add the final segment
    if (currentSegment.isNotEmpty()) {
        result.add(Pair(lastLanguage?:AppLocale.ENGLISH, currentSegment.toString()))
    }

    return buildAnnotatedString {
        val englishFont = FontFamily(Font(R.font.dotspot_regular))
        val arabicFont = FontFamily(Font(R.font.arialbd))
        result.forEach { segment ->
            val isArabic = segment.first==AppLocale.ARABIC
            withStyle(style = SpanStyle(
                fontFamily = if (isArabic) arabicFont else englishFont,
                fontSize = if(isArabic) 14.sp else 16.sp
            )) {
                append(segment.second)
            }
        }
    }
}

@Composable
fun VerticallyCenteredTextWithOverflow(text: String, parentHeight: Dp = 40.dp) {
    SubcomposeLayout { constraints ->
        val textPlaceable = subcompose("Text") {
            Text(
                text = text,
                style = TextStyle(
                    fontSize = 80.sp
                ),
                onTextLayout = { textLayoutResult ->
                    // You can inspect the TextLayoutResult for debugging
                    val textHeight = textLayoutResult.size.height
                }
            )
        }.first().measure(constraints.copy(minHeight = 0, maxHeight = Constraints.Infinity))

        // Determine the size of the parent container
        val containerHeight = parentHeight.roundToPx()
        val textHeight = textPlaceable.height

        // Calculate the top offset to center the text, even if it overflows
        val topOffset = (containerHeight - textHeight) / 2

        layout(width = constraints.maxWidth, height = containerHeight) {
            textPlaceable.place(x = 0, y = topOffset) // Prevent negative offset
        }
    }
}