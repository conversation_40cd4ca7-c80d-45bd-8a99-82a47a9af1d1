package com.app.messej.ui.home.gift

import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.R
import com.app.messej.data.model.api.gift.GiftItem
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.data.model.enums.GiftType
import com.app.messej.data.model.socket.PodiumMaidanScoreUpdatePayload
import com.app.messej.data.utils.EnumUtil.except
import com.app.messej.ui.utils.FragmentExtensions.downloadAndShowGift
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe


class GiftListBottomSheetFragment : GiftListBottomSheetBaseFragment() {
    val args: GiftListBottomSheetFragmentArgs by navArgs()
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    companion object {
        const val GIFT_REQUEST_KEY = "giftRequest"
        const val GIFT_REQUEST_PAYLOAD = "giftPayLoad"
        const val CONTEXT_ID ="context_ID"
    }

    private fun setup() {
        viewModel.setParams(GiftListingViewModel.GiftParams(
            receiver = args.receiverId,
            giftContext = args.giftContext,
            contextId = args.giftContextId,
            birthday = args.birthday,
            userCongrats = args.userLevelCongrats,
            challengeId = args.challengeId,
            challengeEndTimeStampUTC = args.challengeEndTimeStampUTC,
            managerId = args.managerId
        ))
        setUpTabData()
//        viewModel.loadGift(args.giftContext)
    }

    override fun getTabs(): List<GiftType> {
        if (args.giftContext== GiftContext.GIFT_PODIUM_MAIDAN) {
            return GiftType.entries.filter { it != GiftType.BANK }
        } else if (args.birthday) {
            return GiftType.entries.except(GiftType.VIP, GiftType.BANK).toList()
        }
        return super.getTabs()
    }

    private fun observe() {
        viewModel.onGiftSent.observe(viewLifecycleOwner) { item ->
            setFragmentResult(
                GIFT_REQUEST_KEY, bundleOf(
                    GIFT_REQUEST_PAYLOAD to item.copy(podiumId =if(args.giftContext.isPodiumType) args.giftContextId else null, challengeId = args.challengeId), CONTEXT_ID to args.giftContextId
                )
            )
            giftCommonViewModel.getGiftList()
            Log.d("GLVM","onGiftSent: $item")
            if (args.giftContext== GiftContext.GIFT_PODIUM_MAIDAN && (item.coins?:0)<PodiumMaidanScoreUpdatePayload.MIN_COINS_FOR_VIDEO) {
                findNavController().popBackStack()
            }
            else if (item.hasVideo) {
                findNavController().popBackStack()
                downloadAndShowGift(item)
            } else {
                val action = GiftListBottomSheetFragmentDirections.actionGlobalNotificationLottieBottomSheetFragment(item.id, args.receiverId, false)
                val options = NavOptions.Builder().setPopUpTo(R.id.giftListFragment, inclusive = true).build()
                findNavController().navigateSafe(action, options)
//                Toast.makeText(requireContext(), getString(R.string.gift_sent_successfully, viewModel.nameOrNickname.value), Toast.LENGTH_SHORT).show()
            }
        }
    }

    override fun onGiftItemClick(item: GiftItem, preview:Boolean) {
        Log.d("QQQQ,", "" + preview)
        if(viewModel.isGiftLoading.value==true && preview) return
        viewModel.setGiftLoading(true)
        viewModel.sendGift(item,preview)
    }

}