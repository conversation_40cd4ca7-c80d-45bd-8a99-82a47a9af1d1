package com.app.messej.ui.utils

import android.content.Context
import android.content.res.Resources
import android.icu.number.Notation
import android.icu.number.NumberFormatter
import android.icu.number.Precision
import android.icu.text.CompactDecimalFormat
import android.os.Build
import android.util.TypedValue
import androidx.core.content.res.ResourcesCompat
import com.app.messej.R
import com.app.messej.data.model.enums.BusinessColor
import java.math.RoundingMode
import java.util.Locale
import kotlin.math.roundToInt

object DataFormatHelper {

    @JvmStatic
    fun numberToK(number: Int): String {
        if (number > 999) {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                NumberFormatter.with()
                    .notation(Notation.compactShort())
                    .precision(Precision.minMaxSignificantDigits(1, 2))
                    .roundingMode(RoundingMode.FLOOR)
                    .locale(Locale.US)
                    .format(number)
                    .toString()
            } else {
                CompactDecimalFormat
                    .getInstance(Locale.US, CompactDecimalFormat.CompactStyle.SHORT)
                    .format(number)
                    .toString()
            }
        } else {
            return number.toString()
        }
    }

    @JvmStatic
    fun numberToK(number: Double) = when{
        number<1000 -> formatDecimals(number)
        else -> numberToK(number.roundToInt())
    }

    @JvmStatic
    fun Int.numberToKWithFractions(): String {
        return when {
            (this >= 1000000000) -> String.format(Locale.US,"%.5fB", this / 1000000000.0)
            (this >= 1000000) -> String.format(Locale.US,"%.3fM", this / 1000000.0)
            (this >= 1000) -> String.format(Locale.US,"%.2fK", this / 1000.0)
            else -> this.toString()
        }
    }

    @JvmStatic
    fun Double.numberToKWithFractions(): String {
        return when {
            (this >= 1000000000) -> String.format(Locale.US,"%.5fB", this / 1000000000.0)
            (this >= 1000000) -> String.format(Locale.US,"%.3fM", this / 1000000.0)
            (this >= 1000) -> String.format(Locale.US,"%.2fK", this / 1000.0)
            (this == 0.0) -> "0"
            else -> String.format(Locale.US,"%.2f", this)
        }
    }

    @JvmStatic
    fun formatDecimals(value: Double?,points: Int = 2): String {
        return String.format(Locale.getDefault(), "%.${points}f", value)
    }

    @JvmStatic
    fun Double?.formatDecimalWithRemoveTrailingZeros(points: Int = 2): String {
        val value = String.format(Locale.US, "%.${points}f", (this ?: 0.0)).toBigDecimal().stripTrailingZeros().toPlainString()
        return value ?: "0"
    }

    val Number.DpToPx get() = TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_DIP,
        this.toFloat(),
        Resources.getSystem().displayMetrics).toInt()

    fun businessColorToColorRes(color: BusinessColor, context: Context) = ResourcesCompat.getColor(context.resources, businessColorToColorRes(color), null)

    fun businessColorToColorRes(color: BusinessColor) = when(color) {
        BusinessColor.RED -> R.color.colorBusinessRed
        BusinessColor.ORANGE -> R.color.colorBusinessOrange
        BusinessColor.YELLOW -> R.color.colorBusinessYellow
        BusinessColor.LIGHT_GREEN->R.color.colorBusinessLightGreen
        BusinessColor.GREEN -> R.color.colorBusinessGreen
        BusinessColor.GREY -> R.color.colorBusinessGrey
    }

    fun Number.dpToPx(context: Context) =
        TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, this.toFloat(), context.resources.displayMetrics).toInt()

    fun Number.pxToDp(context: Context) =
        (this.toFloat() / context.resources.displayMetrics.density).toInt()

    fun Number.spToPx(context: Context) =
        TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP, this.toFloat(), context.resources.displayMetrics).toInt()

    @JvmStatic
    fun isEmpty(string: String?): Boolean {
        return string.isNullOrEmpty()
    }
}