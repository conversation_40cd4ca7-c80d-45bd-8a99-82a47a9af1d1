package com.app.messej.ui.home.publictab.socialAffairs

import android.content.Context
import androidx.annotation.ColorRes
import androidx.annotation.StringRes
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.SocialAffairUpgradeSupportStatus
import com.app.messej.data.model.api.socialAffairs.SocialVoteErrorResponse
import com.app.messej.data.model.enums.MySocialSupportActionMenuItem
import com.app.messej.data.model.enums.RestoreType
import com.app.messej.data.model.enums.SocialActionButtonType
import com.app.messej.data.model.enums.SocialActiveCaseMainTab
import com.app.messej.data.model.enums.SocialActiveTab
import com.app.messej.data.model.enums.SocialCaseFilter
import com.app.messej.data.model.enums.SocialCaseStatus
import com.app.messej.data.model.enums.SocialVoteAction
import com.app.messej.data.model.enums.UpgradeSupportStatus
import com.app.messej.data.model.socket.SocialPendingApprovalPayload
import com.app.messej.ui.home.HomeFragmentDirections
import com.app.messej.ui.utils.DataFormatHelper.formatDecimalWithRemoveTrailingZeros
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

object SocialAffairUtils {
    fun Modifier.drawRoundRectBehind(borderColor: Color) : Modifier {
        return drawBehind {
            drawRoundRect(
                color = borderColor,
                topLeft = Offset(x = -9F, y = 0F),
                cornerRadius = CornerRadius(x = 25F, y = 25F),
                size = size.copy(width = size.width + 4F, height = size.height + 9F)
            )
        }
    }

    @StringRes
    fun UpgradeSupportStatus.setTitle() : Int = when(this) {
        UpgradeSupportStatus.ResidentLevel -> R.string.social_affair_resident_level
        UpgradeSupportStatus.HundredRating -> R.string.social_affair_hundred_rating
        UpgradeSupportStatus.FlashatAge -> R.string.social_affair_flashat_age
        UpgradeSupportStatus.GainedFlix -> R.string.social_affair_gained_flix_description
        UpgradeSupportStatus.NeverAppliedPayout -> R.string.social_affair_gained_flix_never_applied_payout
        UpgradeSupportStatus.CompletedTasks -> R.string.social_affair_completed_all_tasks
        UpgradeSupportStatus.HaveCoinBalance -> R.string.social_affair_have_coin_balance_text
        UpgradeSupportStatus.ReceivedGiftValue -> R.string.social_affair_not_send_gift_value_text
    }

    fun UpgradeSupportStatus.isCompleted(status: SocialAffairUpgradeSupportStatus?) : Boolean? = when(this) {
        UpgradeSupportStatus.ResidentLevel -> status?.isResidentLevelSatisfied
        UpgradeSupportStatus.HundredRating -> status?.ratingSatisfied
        UpgradeSupportStatus.FlashatAge -> status?.flashatAgeSatisfied
        UpgradeSupportStatus.GainedFlix -> status?.gainedFlixSatisfied
        UpgradeSupportStatus.NeverAppliedPayout -> status?.neverAppliedPayout
        UpgradeSupportStatus.CompletedTasks -> status?.completedTasks
        UpgradeSupportStatus.HaveCoinBalance -> status?.haveCoinBalance
        UpgradeSupportStatus.ReceivedGiftValue -> status?.isGiftSatisfied
    }

    @StringRes
    fun SocialActiveCaseMainTab.setText() = when(this) {
        SocialActiveCaseMainTab.Donate -> R.string.social_affair_donate
        SocialActiveCaseMainTab.NewCases -> R.string.social_affair_new_cases
    }

    @StringRes
    fun SocialActiveTab.setText() = when(this) {
        SocialActiveTab.All -> R.string.social_all_cases
        SocialActiveTab.Personal -> R.string.social_personal_cases
        SocialActiveTab.Upgrade -> R.string.social_help_upgrade_cases
        SocialActiveTab.Social -> R.string.social_engage_cases
    }

    @StringRes
    fun SocialCaseFilter.setText() = when(this) {
        SocialCaseFilter.All -> R.string.social_all_cases
        SocialCaseFilter.Declined -> R.string.common_declined
        SocialCaseFilter.UnmetVoting -> R.string.social_unmet_voting
        SocialCaseFilter.Pending -> R.string.social_filter_pending
        SocialCaseFilter.Closed -> R.string.social_filter_closed
    }

    @ColorRes
    fun SocialActionButtonType.getBackgroundColor(isSupport: Boolean) = when(this) {
        SocialActionButtonType.ActiveButton -> if (isSupport) R.color.colorSocialGreen else R.color.colorSocialRed
        SocialActionButtonType.DisabledButton -> R.color.textColorAlwaysDarkSecondaryLight
        SocialActionButtonType.ExpiredButton -> R.color.colorSurfaceSecondaryDark
    }

    @ColorRes
    fun SocialActionButtonType.getTextColor(isSupport: Boolean) = when(this) {
        SocialActionButtonType.ActiveButton -> R.color.white
        SocialActionButtonType.DisabledButton -> R.color.textColorAlwaysDarkHint
        SocialActionButtonType.ExpiredButton -> if (isSupport) R.color.colorSocialVoteUpExpired else R.color.colorSocialVoteDownExpired
    }

    @Composable
    fun SocialActionButtonType.getBorderColor(isSupport: Boolean) : List<Color> = when(this) {
        SocialActionButtonType.ActiveButton ->
            if (isSupport) listOf(colorResource(id = R.color.colorSocialGreen), colorResource(id = R.color.colorSocialDarkestGreen))
            else listOf(colorResource(id = R.color.colorSocialRed), colorResource(id = R.color.colorSocialDarkRed))
        SocialActionButtonType.DisabledButton -> List(size = 2) { colorResource(id = R.color.textColorAlwaysDarkHint) }
        SocialActionButtonType.ExpiredButton -> List(size = 2) { Color.Transparent }
    }

    fun Fragment.showVotingConfirmationAlert(
        onConfirm : () -> Unit
    ) {
        showFlashatDialog {
            setMessage(getString(R.string.social_support_confirmation_message))
            setConfirmButton(title = R.string.podium_live_challenge_continue, icon = R.drawable.ic_chat_liked, tint = true) {
                onConfirm()
                true
            }
        }
    }

    fun Fragment.showSocialErrorAlert(
        error: SocialVoteErrorResponse.SocialError?,
        isNewPersonalRequest: Boolean = false,
        onConfirmDeleteArchiveOrActiveCase: (() -> Unit)? = null
    ) {
        //Checking which type of alerts need to be shown in error.
        //RATING_REQUIRED, AGE_REQUIRED, ACTIVE_CASE, MSA_VOTE
        //In some cases no need to show confirm icon -> it's all handled in below conditions
        val remainingDayForVote = (error?.requiredAge ?: 0) - (error?.flashatAge ?: 0)

        //Message in alert dialog
        val message = when(error?.reason) {

            SocialVoteErrorResponse.SocialVoteErrorReason.RATING_REQUIRED ->
                if (isNewPersonalRequest) getString(R.string.social_support_submit_new_case_rating_error, "${error.requiredRating ?: 0}")
                else getString(R.string.social_voting_improve_rating_message, "${error.requiredRating ?: 0}")

            SocialVoteErrorResponse.SocialVoteErrorReason.AGE_REQUIRED ->
                if (isNewPersonalRequest) getString(R.string.social_support_submit_new_case_age_error, "${error.requiredAge ?: 0}", "$remainingDayForVote")
                else getString(R.string.social_vote_age_error, "${error.requiredAge ?: 0}", "$remainingDayForVote")

            SocialVoteErrorResponse.SocialVoteErrorReason.ACTIVE_CASE ->
                getString(
                    when(error.activeCaseStatus) {
                        SocialCaseStatus.ACTIVE, SocialCaseStatus.PENDING_APPROVAL -> R.string.social_active_case_delete_alert
                        SocialCaseStatus.ARCHIVED -> R.string.social_active_archive_case_delete_alert
                        else -> R.string.social_new_case_delete_alert
                    }
                )

            SocialVoteErrorResponse.SocialVoteErrorReason.MSA_VOTE -> "MSA Vote"
            SocialVoteErrorResponse.SocialVoteErrorReason.USER_BANNED -> getString(R.string.social_user_banned_for_received_donation)
            else -> ""
        }

        //Confirm Icon, if no need to show confirm icon, make it null
        val confirmIcon = when(error?.reason) {
            SocialVoteErrorResponse.SocialVoteErrorReason.RATING_REQUIRED -> R.drawable.ic_restore_rating
            SocialVoteErrorResponse.SocialVoteErrorReason.ACTIVE_CASE -> if (error.activeCaseStatus?.isActive == true || error.activeCaseStatus?.isPendingApproval == true) null else R.drawable.ic_delete
            else -> null
        }

        //Confirm Icon Text, if no need to show confirm icon text, make it null
        val confirmIconText = when(error?.reason) {
            SocialVoteErrorResponse.SocialVoteErrorReason.RATING_REQUIRED -> R.string.restore_rating_header
            SocialVoteErrorResponse.SocialVoteErrorReason.ACTIVE_CASE -> R.string.common_delete
            else -> null
        }

        //Confirm button action, if confirm button is visible.
        val confirmAction = {
            when(error?.reason) {
                SocialVoteErrorResponse.SocialVoteErrorReason.RATING_REQUIRED -> {
                    findNavController().navigateSafe(direction = NavGraphHomeDirections.actionHomeBusinessFragmentToRestoreRatingFragment(isResident = false,RestoreType.FOR_ME))
                }
                SocialVoteErrorResponse.SocialVoteErrorReason.ACTIVE_CASE -> {
                    showSocialConfirmAlertDialog(
                        message = getString(if (error.activeCaseStatus?.isArchived == true) R.string.social_support_archive_delete_confirmation else R.string.social_support_delete_confirmation),
                        onConfirm = {
                            onConfirmDeleteArchiveOrActiveCase?.let { it() }
                        }
                    )
                }
                else -> null
            }
        }

        showFlashatDialog {
            setMessage(message = message)
            setCloseButton(title = if (confirmIcon == null) R.string.common_close else R.string.common_cancel)
            setConfirmButtonVisible(visible = confirmIcon != null)
            if (confirmIconText != null && confirmIcon != null) {
                setConfirmButton(title = confirmIconText, icon = confirmIcon, tint = true) {
                    confirmAction()
                    true
                }
            }
        }
    }

    @StringRes
    fun MySocialSupportActionMenuItem.setText() = when(this) {
        MySocialSupportActionMenuItem.Edit -> R.string.common_edit
        MySocialSupportActionMenuItem.Archive -> R.string.common_archive
        MySocialSupportActionMenuItem.UnArchive -> R.string.common_un_archive
        MySocialSupportActionMenuItem.Delete -> R.string.common_delete
        MySocialSupportActionMenuItem.Draft -> R.string.common_drafts
    }

    @StringRes
    fun SocialVoteAction.setText() = when(this) {
        SocialVoteAction.Support -> R.string.social_supported
        SocialVoteAction.Oppose -> R.string.social_opposed
    }

    @ColorRes
    fun SocialVoteAction.setTextColor() = when(this) {
        SocialVoteAction.Support -> R.color.colorSocialGreen
        SocialVoteAction.Oppose -> R.color.colorSocialRed
    }

    fun SocialCaseStatus.setText(context: Context): String? = when(this) {
        SocialCaseStatus.ACTIVE, SocialCaseStatus.NEW -> context.getString(R.string.common_active)
        SocialCaseStatus.DELETED -> context.getString(R.string.common_deleted)
        SocialCaseStatus.UNMET_VOTING -> context.getString(R.string.social_unmet_voting)
        SocialCaseStatus.PENDING_APPROVAL -> context.getString(R.string.social_filter_pending)
        SocialCaseStatus.CLOSED -> context.getString(R.string.social_filter_closed)
        SocialCaseStatus.DECLINED -> context.getString(R.string.common_declined)
        //Don't add here. for draft and archived no need to show the status in my personal case single item view
//        SocialCaseStatus.DRAFT -> TODO()
//        SocialCaseStatus.ARCHIVED -> TODO()
        else -> null
    }

    @ColorRes
    fun SocialCaseStatus.setTextColor() : Int = when(this) {
        SocialCaseStatus.ACTIVE, SocialCaseStatus.PENDING_APPROVAL, SocialCaseStatus.NEW -> R.color.colorDonatedGradient
        SocialCaseStatus.DELETED, SocialCaseStatus.UNMET_VOTING, SocialCaseStatus.CLOSED, SocialCaseStatus.DECLINED -> R.color.colorSocialRed
//        SocialCaseStatus.DRAFT -> TODO()
//        SocialCaseStatus.ARCHIVED -> TODO()
        else -> R.color.textColorPrimary
    }
    
    fun Fragment.socialEngageDevelopmentAlert() {
        showFlashatDialog {
            setMessage(message = R.string.social_engage_development_message)
            setConfirmButtonVisible(visible = false)
        }
    }

    fun Fragment.showSocialDonateConfirmationAlert(coins: String?, onConfirm: () -> Unit) {
        showFlashatDialog {
            setMessage(message = getString(R.string.social_donate_confirmation_message, coins ?: "0"))
            setCloseButton(title = R.string.common_cancel)
            setConfirmButton(title = R.string.common_confirm, icon = R.drawable.ic_chat_liked, tint = true) {
                onConfirm()
                true
            }
        }
    }

    fun Fragment.showSocialConfirmAlertDialog(
        message: String,
        onConfirm: () -> Unit
    ) {
        showFlashatDialog {
            setCloseButton(title = R.string.common_cancel)
            setMessage(message = message)
            setConfirmButton(title = R.string.common_confirm, icon = R.drawable.ic_chat_liked, tint = true) {
                onConfirm()
                true
            }
        }
    }

    fun MainActivity.showPendingCaseAlertDialog(data: SocialPendingApprovalPayload?) {
        data?.caseId ?: return
        showFlashatDialog {
            setTitle(title = getString(R.string.social_pending_case_event_title, data.name))
            setMessage(message = getString(R.string.social_pending_case_event_description, data.amount.formatDecimalWithRemoveTrailingZeros()))
            setConfirmButton(title = R.string.common_view, icon = R.drawable.ic_file, tint = true) {
                navController.navigateSafe(direction = HomeFragmentDirections.actionGlobalSocialCaseInfoFragment(id = data.caseId, isUpgradeSupport = false))
                true
            }
        }
    }
}