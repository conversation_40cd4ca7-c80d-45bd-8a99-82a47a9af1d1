package com.app.messej.ui.home

import android.app.Application
import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.gift.GiftNotificationResponse
import com.app.messej.data.model.entity.NickName.Companion.nickNameOrName
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.data.model.socket.SentGiftPayload
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.BaseMediaUploadRepository
import com.app.messej.data.repository.GiftRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.socket.repository.GiftEventRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.home.gift.GiftListingViewModel
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import java.io.File


class CommonGiftViewModel(application: Application) : AndroidViewModel(application) {
    private val accountRepo: AccountRepository = AccountRepository(application)
    private val giftRepository = GiftRepository(application)
    private val giftEventRepo = GiftEventRepository
    private val profileRepo = ProfileRepository(application)

    val onGiftReceived = LiveEvent<SentGiftPayload>()
    val user: CurrentUser get() = accountRepo.user

    var onSayThanksLoading by mutableStateOf(false)

    init {
        viewModelScope.launch {
            giftEventRepo.giftPayLoad.collect { gift ->
                onGiftReceived.postValue(gift)
            }
        }
    }

    fun getNickName(id: Int, name: String?): String? {
        if(name == null) return name
        return nickNames.nickNameOrName(id, name)
    }

    private val nickNames = profileRepo.getNickNamesFlow().stateIn(
        scope = viewModelScope, started = SharingStarted.Eagerly, initialValue = listOf()
    )

    private val nickNamesLiveData = nickNames.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    val onGiftVideoAvailable = LiveEvent<Boolean>()
    var isUserBdayFromDailySyncAPi = false

    private var readyToPlayGiftVideo = false
    private var giftVideoQueue = mutableListOf<Pair<SentGiftPayload?, File>>()

    private val downloadLock = Mutex()

    fun readyToPlayGifts(ready: Boolean = true) {
        Log.w("GFTV", "readyToPlayGifts: $ready")
        synchronized(giftVideoQueue) {
            readyToPlayGiftVideo = if (ready) {
                if (giftVideoQueue.isEmpty()) {
                    true
                } else {
                    Log.w("GFTV", "posting gift from queue: ${giftVideoQueue[0].first?.translatedName}")
                    onGiftVideoAvailable.postValue(true)
                    false
                }
            } else {
                false
            }
        }
    }

    fun consumeGiftVideo() : Pair<SentGiftPayload?, File>? {
        if (giftVideoQueue.isEmpty()) return null
        return giftVideoQueue.removeAt(0)
    }

    fun showGiftVideo(gift: SentGiftPayload) {
        if (!gift.hasValidAnimationUrl) return
        viewModelScope.launch(Dispatchers.IO) {
            try {
                Log.w("GFTV", "received gift ${gift.translatedName} from ${gift.senderName}.")
                downloadLock.withLock {
                    Log.w("GFTV", "starting download of ${gift.translatedName}.")
                    giftRepository.downloadGiftVideo(gift).collect {
                        when (it) {
                            is BaseMediaUploadRepository.VideoDownloadResult.Progress -> {
                                Log.d("GFTV", "progress VM: ${it.percent}")
                            }

                            is BaseMediaUploadRepository.VideoDownloadResult.Complete -> {
                                if (readyToPlayGiftVideo) {
                                    Log.w(
                                        "GFTV",
                                        "posting gift immediately: ${gift.translatedName}."
                                    )
                                    giftVideoQueue.add(Pair(gift, it.file))
                                    onGiftVideoAvailable.postValue(true)
                                    readyToPlayGiftVideo = false
                                } else {
                                    Log.w("GFTV", "adding gift to queue: ${gift.translatedName}.")
                                    giftVideoQueue.add(Pair(gift, it.file))
                                }
                                giftRepository.saveGiftVideo(gift, it.file)
                                Log.d("GFTV", it.file.toString())
                            }

                            is BaseMediaUploadRepository.VideoDownloadResult.Error -> {
                                throw it.error
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e("GFTV", "showGiftVideo:",e)
            }
        }
    }

    fun showChallengeResultVideo(winner: Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            Log.w("GFTV", "showChallengeResultVideo $winner.")
            downloadLock.withLock {
                Log.w("GFTV", "starting download")
                giftRepository.downloadChallengeResultVideo(winner).collect {
                    when (it) {
                        is BaseMediaUploadRepository.VideoDownloadResult.Progress -> {
                            Log.d("GFTV", "progress VM: ${it.percent}")
                        }

                        is BaseMediaUploadRepository.VideoDownloadResult.Complete -> {
                            giftVideoQueue.add(Pair(null, it.file))
                            if (readyToPlayGiftVideo) {
                                Log.w("GFTV", "posting winner video immediately.")
                                onGiftVideoAvailable.postValue(true)
                                readyToPlayGiftVideo = false
                            } else {
                                Log.w("GFTV", "adding winner video to queue.")
                            }
                        }

                        is BaseMediaUploadRepository.VideoDownloadResult.Error -> {
                            Log.e("GFTV", "showChallengeResultVideo:",it.error)
                        }
                    }
                }
            }
        }
    }

    fun showBirthdayResultVideo(birthdayUrl: String, isUserBdayFromDailySyncAPI: Boolean = false) {
        viewModelScope.launch(Dispatchers.IO) {

            downloadLock.withLock {
                Log.w("GFTV", "starting download")
                giftRepository.downloadBirthdayResultVideo(birthdayUrl).collect {
                    when (it) {
                        is BaseMediaUploadRepository.VideoDownloadResult.Progress -> {
                            Log.d("GFTV", "progress VM: ${it.percent}")
                        }

                        is BaseMediaUploadRepository.VideoDownloadResult.Complete -> {
                            giftVideoQueue.add(Pair(null, it.file))
                            if (readyToPlayGiftVideo) {
                                Log.w("GFTV", "posting birthday video immediately.")
                                onGiftVideoAvailable.postValue(true)
                                <EMAIL> = isUserBdayFromDailySyncAPI
                                readyToPlayGiftVideo = false
                            } else {
                                Log.w("GFTV", "adding birthday video to queue.")
                            }
                        }

                        is BaseMediaUploadRepository.VideoDownloadResult.Error -> {
                            Log.e("GFTV", "showBirthdayResultVideo:",it.error)
                        }
                    }
                }
            }
        }
    }




    val onGiftReplySent = LiveEvent<Boolean>()

    fun sayThanks(gift: SentGiftPayload) {
        onSayThanksLoading = true
        viewModelScope.launch(Dispatchers.IO) {
            val tg = getGiftSayThanks()?: return@launch
            gift.senderId?.let { senderId->
                when (val result = giftRepository.sendGift(tg.id, GiftListingViewModel.GiftParams(senderId, GiftContext.GIFT_SAY_THANKS))) {
                    is ResultOf.Success -> {
                        onGiftReplySent.postValue(true)
                    }
                    is ResultOf.APIError -> {
                        if (result.code == 400) {
                            onGiftSendInsufficientBalance.postValue(true)
                        } else {
                            onGiftSendError.postValue(result.error.message)
                        }
                    }
                    is ResultOf.Error -> {
                        onGiftSendError.postValue(result.exception.message)
                    }
                }
                onSayThanksLoading = false
            }
        }
    }

    private var thanksGift: GiftNotificationResponse? = null
    val onGiftSendInsufficientBalance = LiveEvent<Boolean>()
    val onGiftSendError = LiveEvent<String>()

    private suspend fun getGiftSayThanks(): GiftNotificationResponse? = withContext(Dispatchers.IO) {
        thanksGift?.let { return@withContext it }

        when (val result = giftRepository.getSayThanksGift()) {
            is ResultOf.Success -> {
                thanksGift = result.value
                return@withContext result.value
            }
            else -> { }
        }
        return@withContext null

    }


     fun onFlixDeductionAccountUpdate(flix: Int?) {
         viewModelScope.launch(Dispatchers.IO) {
             val accountDetails = accountRepo.getAccountDetailsFlow().firstOrNull()
             if (accountDetails?.id == accountRepo.user.id) {
                 val currentFlixBalance =  accountDetails?.activePoints
                 accountDetails?.activePoints = currentFlixBalance?.minus(flix?.toDouble() ?: 0.0)
                 accountDetails?.let { details ->
                     accountRepo.saveAccountDetails(details)
                 }
             }
         }
     }

    val isReadyToShowCreateBirthdayPodiumBottomSheet = LiveEvent<Boolean>()
    fun setBirthdayBottomSheetVisibility(isVisible: Boolean) {
        isReadyToShowCreateBirthdayPodiumBottomSheet.postValue(isVisible)
    }

    }
