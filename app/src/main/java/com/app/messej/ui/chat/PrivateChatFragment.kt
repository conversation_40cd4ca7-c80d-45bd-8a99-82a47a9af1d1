package com.app.messej.ui.chat

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.Toast
import androidx.appcompat.view.ActionMode
import androidx.appcompat.widget.PopupMenu
import androidx.core.view.MenuProvider
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.observe
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractChatMessageWithMedia
import com.app.messej.data.model.entity.OfflineMedia
import com.app.messej.data.model.enums.AttachmentSource
import com.app.messej.data.model.enums.ForwardSource
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.data.model.enums.PrivateChatPrivacy
import com.app.messej.data.model.enums.PrivateChatPrivacy.*
import com.app.messej.data.model.enums.UserProfileContext
import com.app.messej.databinding.ItemCustomActionBarPrivateChatBinding
import com.app.messej.databinding.LayoutImageViewerHeaderBinding
import com.app.messej.databinding.LayoutIntruderMessageRequestBinding
import com.app.messej.databinding.LayoutMessageBannedUserBinding
import com.app.messej.databinding.LayoutPrivateMessageLimitedChatBinding
import com.app.messej.databinding.LayoutPrivateMessageRequestBinding
import com.app.messej.databinding.LayoutPrivateMessagesFollowUserBinding
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.validateAndConfirmJoinFromGreenDot
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.stfalcon.imageviewer.StfalconImageViewer
import kotlinx.coroutines.launch

class PrivateChatFragment : BaseChatFragment(), MenuProvider {

    private val args: PrivateChatFragmentArgs by navArgs()

    override val viewModel: PrivateChatViewModel by navGraphViewModels(R.id.nav_chat_private)

    private lateinit var actionBarBinding: ItemCustomActionBarPrivateChatBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        val root = super.onCreateView(inflater, container, savedInstanceState)
        inflateActionBar()
        return root
    }

    private fun inflateActionBar() {
        binding.actionBarStub.apply {
            viewStub?.apply {
                setOnInflateListener { _, inflated ->
                    actionBarBinding = ItemCustomActionBarPrivateChatBinding.bind(inflated)
                }
                layoutResource = R.layout.item_custom_action_bar_private_chat
                inflate()
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.apply {
            setupActionBar(actionBarBinding.toolbar)
        }
        viewModel.announceChatEntry(true)
    }

    override fun onStop() {
        super.onStop()
        viewModel.announceChatEntry(false)
    }

    private fun setup() {
        viewModel.setRoomId(args.roomId, args.receiver)

        actionBarBinding.toolbar.setOnClickListener {
            if (viewModel.isAdminMessage.value == true) return@setOnClickListener
            val action = PrivateChatFragmentDirections.actionGlobalPublicUserProfileFragment(args.receiver, context = UserProfileContext.PRIVATE_CHAT)
            findNavController().navigateSafe(action)
        }
        actionBarBinding.chatDp.setOnClickListener {
            if (viewModel.chat.value?.receiverDetails?.userLivePodium == true && viewModel.chat.value?.receiverDetails?.userLivePodiumId != null) {
                val podiumId = viewModel.chat.value?.receiverDetails?.userLivePodiumId ?: return@setOnClickListener
                validateAndConfirmJoinFromGreenDot(podiumId, viewModel.chat.value?.receiverDetails?.name ?: return@setOnClickListener, viewModel.user)
            } else {
                val action = PrivateChatFragmentDirections.actionGlobalPublicUserProfileFragment(args.receiver, context = UserProfileContext.PRIVATE_CHAT)
                findNavController().navigateSafe(action)
            }
        }

        ActiveChatTracker.registerActiveScreen(ActiveChatTracker.ActiveChatScreen.PrivateChat(args.roomId), viewLifecycleOwner)

        ViewCompat.setOnApplyWindowInsetsListener(binding.chatContainer) { v, insets ->
            val imeHeight = insets.getInsets(WindowInsetsCompat.Type.ime()).bottom
            v.setPadding(0, 0, 0, imeHeight) // Apply only keyboard insets
            insets
        }
    }

    private fun observe() {

        viewModel.isGiftBlocked.observe(viewLifecycleOwner){
            requireActivity().invalidateOptionsMenu()
        }

        viewModel.isAdminMessage.observe(viewLifecycleOwner) {
            requireActivity().invalidateOptionsMenu()
            isAdminMessage = it
        }

        viewModel.chat.observe(viewLifecycleOwner) {
            actionBarBinding.chat = it
            initChatStatusLayout(viewModel.messageStatus.value)
        }

        viewModel.lastSeenInfo.observe(viewLifecycleOwner) {
            actionBarBinding.lastSeen = it
        }

        viewModel.messageStatus.observe(viewLifecycleOwner) {
            Log.d("MESSAGE_STATUSS","ssggs${it}")
            it?: return@observe
            initChatStatusLayout(it)
        }

        //TODO implement typing info

//        viewModel.typingInfo.observe(viewLifecycleOwner) {
//            actionBarBinding.typingInfo = it
//        }

        viewModel.onDeleteChat.observe(viewLifecycleOwner) {
            findNavController().popBackStack()
        }

        viewModel.chatIgnored.observe(viewLifecycleOwner){
            if (it) Toast.makeText(requireContext(), resources.getString(R.string.private_intruder_ignored), Toast.LENGTH_SHORT).show()
        }

        viewModel.userDeleted.observe(viewLifecycleOwner){
            if (it) findNavController().popBackStack()
        }

        // TODO allowReplySwipe needs to be able to respond to live changes. Create a better implementation if possible. Had to manually add this to fix FLS-2905
//        viewModel.enableChatInteraction.observe(viewLifecycleOwner) {
////            mAdapter?.allowReplySwipe = it?:false
//         //   mAdapter?.notifyDataSetChanged()
//        }
        viewModel.isFlaxRateFull.observe(viewLifecycleOwner){
            Log.d("User_Rating","rating${it}")
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        menuInflater.inflate(R.menu.menu_chat_private_actions, menu)
        menu.findItem(R.id.action_gift).isVisible = viewModel.isGiftBlocked.value == false
        menu.findItem(R.id.action_more).isVisible = viewModel.isAdminMessage.value == false
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_more -> showMoreMenu(menuItem)
            R.id.action_gift->findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalGiftFragment(args.receiver, giftContext = GiftContext.GIFT_BUDDIES))
            else -> return false
        }
        return true
    }

    override fun onForwardClick(item: AbstractChatMessage, position: Int) {
        findNavController().navigateSafe(GroupChatFragmentDirections.actionGlobalForwardHomeFragment(srcType = ForwardSource.MESSAGES, messageId = item.messageId))
    }

    private fun showMoreMenu(v: MenuItem) {
        val view = activity?.findViewById<View>(v.itemId)?: return
        val popup = PopupMenu(requireContext(),view)
        popup.menuInflater.inflate(R.menu.menu_chat_private_action_more, popup.menu)
//        popup.setForceShowIcon(true)
        popup.menu.findItem(R.id.action_user_info).isVisible = viewModel.messageStatus.value != PrivateChatViewModel.ChatStatus.DeletedUser
        popup.setOnMenuItemClickListener { menuItem ->
            when(menuItem.itemId) {
                R.id.action_clear -> viewModel.clearPrivateMessages()
                R.id.action_delete_user -> viewModel.deleteUser()
                R.id.action_user_info -> {
//                    val userType = PublicUserProfileLoaderFragment.getUserType(viewModel.chat.value?.receiverDetails?.citizenship)
                    val action = PrivateChatFragmentDirections.actionGlobalPublicUserProfileFragment(args.receiver, context = UserProfileContext.PRIVATE_CHAT)
                    findNavController().navigateSafe(action)
                }
                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
    }

    override fun showSelectionMode(show: Boolean) {
        if(show) {
            actionMode = (requireActivity() as MainActivity).startSupportActionMode(object: ActionMode.Callback{
                override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    requireActivity().menuInflater.inflate(R.menu.menu_private_chat_selection,menu)
                    return true
                }
                override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    return false
                }
                override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                    when(item?.itemId) {
                        R.id.action_reply -> viewModel.replyToSelection()
                        R.id.action_copy -> viewModel.copySelection()
                        R.id.action_forward->findNavController().navigateSafe(PrivateChatFragmentDirections.actionGlobalForwardHomeFragment(srcType = ForwardSource.MESSAGES, messageId = viewModel.selectedChats.value?.get(0)))
                        R.id.action_delete -> confirmDelete(R.string.chat_delete_confirm_title, R.string.chat_delete_confirm_message, true,
                        viewModel.canDeleteSelectionForEveryone.value == true) {
                            viewModel.deleteSelection(it)
                        }
                        else -> return false
                    }
                    return true
                }
                override fun onDestroyActionMode(mode: ActionMode?) {
                    viewModel.exitSelectionMode()
                }
            })
        }
        else {
            actionMode?.finish()
            actionMode = null
        }
    }

    override fun customizeImageOverlay(viewer: StfalconImageViewer<OfflineMedia>, headerBinding: LayoutImageViewerHeaderBinding, msg: AbstractChatMessageWithMedia) {
        headerBinding.toolbar.addMenuProvider(object: MenuProvider {
            override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
                menuInflater.inflate(R.menu.menu_chat_image_fullscreen_with_actions,menu)
                menu.findItem(R.id.action_delete).isVisible = viewModel.canDeleteMessage(msg)
            }

            override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
                when (menuItem.itemId) {
                    R.id.action_save_to_gallery -> viewModel.saveToGallery(msg.offlineMedia)
                    R.id.action_delete -> confirmDelete(R.string.chat_delete_confirm_title, R.string.chat_delete_confirm_message, true, viewModel.canDeleteMessageForEveryOne(msg)) {
                        viewModel.deleteMessage(msg.message, it)
                        viewer.close()
                    }
                    else -> return false
                }
                return true
            }

        },viewLifecycleOwner)
    }

    override fun showAttachDialog() {
        val src = AttachmentSource.entries.toTypedArray()
        val action = PrivateChatFragmentDirections.actionGlobalImageAttachSourceFragment(src)
        findNavController().navigateSafe(action)
    }

    override fun showImagePreview() {
        findNavController().navigateSafe(PrivateChatFragmentDirections.actionPrivateChatFragmentToPrivateChatImageAttachFragment(viewModel.chat.value?.receiverDetails?.name))
    }

    override fun showVideoPreview() {
        findNavController().navigateSafe(PrivateChatFragmentDirections.actionPrivateChatFragmentToPrivateChatVideoAttachFragment(viewModel.chat.value?.receiverDetails?.name))
    }

    override fun navigateToLocationSelect() {
        Log.w("PCF", "navigateToLocationSelect: navigating")
        findNavController().navigateSafe(PrivateChatFragmentDirections.actionPrivateChatFragmentToPrivateChatLocationAttachFragment())
    }

    override fun showDocumentPreview() {
        findNavController().navigateSafe(PrivateChatFragmentDirections.actionPrivateChatFragmentToPrivateChatDocumentAttachFragment(viewModel.chat.value?.receiverDetails?.name))
    }

    override val shouldAutoplayNextAudio: Boolean = true

    private fun initChatStatusLayout(status: PrivateChatViewModel.ChatStatus?) {
        status?: return
        binding.actionDecorHolder.apply {
            removeAllViews()
            when (status){

                PrivateChatViewModel.ChatStatus.AdminMessage -> {
                    binding.footer.visibility = View.GONE
                }

                PrivateChatViewModel.ChatStatus.ChatSenderBanned -> {
                    binding.footer.visibility = View.GONE
                    val binding = DataBindingUtil.inflate<LayoutMessageBannedUserBinding>(layoutInflater, R.layout.layout_message_banned_user, this, false)
                    binding.text = getString(R.string.chat_banned_user_message)
                    addView(binding.root)
                }

                PrivateChatViewModel.ChatStatus.ChatReceiverBanned -> {
                    binding.footer.visibility = View.GONE
                    val binding = DataBindingUtil.inflate<LayoutMessageBannedUserBinding>(layoutInflater, R.layout.layout_message_banned_user, this, false)
                    binding.text = getString(R.string.chat_banned_other_user_message)
                    addView(binding.root)
                }

                PrivateChatViewModel.ChatStatus.FollowBackToChat -> {
                    val binding = DataBindingUtil.inflate<LayoutPrivateMessageLimitedChatBinding>(layoutInflater, R.layout.layout_private_message_limited_chat, this, false)
                    binding.prompt.text = resources.getString(R.string.private_message_cannot_chat_text, viewModel.chat.value?.receiverDetails?.name)
                    addView(binding.root)
                }

                PrivateChatViewModel.ChatStatus.FollowToChat -> {
                    val binding = DataBindingUtil.inflate<LayoutPrivateMessagesFollowUserBinding>(layoutInflater, R.layout.layout_private_messages_follow_user, this, false)
                    binding.loading=viewModel.chatActionLoading
                    binding.lifecycleOwner = viewLifecycleOwner
                    binding.prompt.text = resources.getString(R.string.private_message_join_text, viewModel.chat.value?.receiverDetails?.name)
                    binding.button.setText(R.string.user_action_follow)
                    binding.button.setOnClickListener {
                        viewModel.followUser()
                    }
                    addView(binding.root)
                }

                is PrivateChatViewModel.ChatStatus.LimitedMessages -> {
                    val binding = DataBindingUtil.inflate<LayoutPrivateMessageLimitedChatBinding>(layoutInflater, R.layout.layout_private_message_limited_chat, this, false)
                    binding.prompt.text = resources.getString(R.string.private_message_chat_count_text, status.remainingMessages, viewModel.chat.value?.receiverDetails?.name)
                    addView(binding.root)
                }

                PrivateChatViewModel.ChatStatus.None -> {}

                PrivateChatViewModel.ChatStatus.ChatRequest -> {
                    val binding = DataBindingUtil.inflate<LayoutPrivateMessageRequestBinding>(layoutInflater, R.layout.layout_private_message_request, this, false)
                    binding.acceptButton.setOnClickListener { viewModel.acceptChatRequest() }
                    binding.blockButton.setOnClickListener {  viewModel.blockChat() }
                    binding.deleteButton.setOnClickListener { viewModel.deleteChatRequest() }
                    addView(binding.root)
                }

                PrivateChatViewModel.ChatStatus.ChatBlockedBySender -> {
                    val binding = DataBindingUtil.inflate<LayoutPrivateMessagesFollowUserBinding>(layoutInflater, R.layout.layout_private_messages_follow_user, this, false)
                    binding.loading=viewModel.chatActionLoading
                    binding.lifecycleOwner = viewLifecycleOwner
                    binding.prompt.text = resources.getString(R.string.private_message_chat_unblock_chat, viewModel.chat.value?.receiverDetails?.name)
                    binding.button.setText(R.string.common_unblock)
                    binding.button.setOnClickListener {
                        viewModel.unblockChat()
                    }
                    addView(binding.root)
                }

                PrivateChatViewModel.ChatStatus.ChatDisabledByReceiver,  PrivateChatViewModel.ChatStatus.UserBlockedByReceiver -> {
                    val binding = DataBindingUtil.inflate<LayoutPrivateMessageLimitedChatBinding>(layoutInflater, R.layout.layout_private_message_limited_chat, this, false)
                    binding.prompt.text = resources.getString(R.string.private_message_chat_receiver_disabled)
                    addView(binding.root)
                }

                PrivateChatViewModel.ChatStatus.ChatDisabledBySender -> {
                    val binding = DataBindingUtil.inflate<LayoutPrivateMessageLimitedChatBinding>(layoutInflater, R.layout.layout_private_message_limited_chat, this, false)
                    binding.prompt.text = resources.getString(R.string.private_message_chat_sender_disabled)
                    addView(binding.root)
                }

                PrivateChatViewModel.ChatStatus.DeletedUser -> {
                    binding.footer.visibility = View.GONE
                    val binding = DataBindingUtil.inflate<LayoutPrivateMessageLimitedChatBinding>(layoutInflater, R.layout.layout_private_message_limited_chat, this, false)
                    binding.prompt.text = resources.getString(R.string.private_message_chat_deleted_user)
                    addView(binding.root)
                }

                PrivateChatViewModel.ChatStatus.Unblock -> {}

                PrivateChatViewModel.ChatStatus.UserBlockedBySender -> {
                    val binding = DataBindingUtil.inflate<LayoutPrivateMessagesFollowUserBinding>(layoutInflater, R.layout.layout_private_messages_follow_user, this, false)
                    binding.loading=viewModel.chatActionLoading
                    binding.lifecycleOwner = viewLifecycleOwner
                    binding.prompt.text = resources.getString(R.string.private_message_chat_unblock_user, viewModel.chat.value?.receiverDetails?.name)
                    binding.button.setText(R.string.common_unblock)
                    binding.button.setOnClickListener {
                        viewModel.unblockPrivateMessages()
                    }
                    addView(binding.root)
                }

                is PrivateChatViewModel.ChatStatus.IntruderChatRequest -> {
                    val binding = DataBindingUtil.inflate<LayoutIntruderMessageRequestBinding>(layoutInflater, R.layout.layout_intruder_message_request, this, false)
                    binding.apply {
                        ignored = status.isIgnored
                        acceptButton.setOnClickListener { viewModel.followUser() }
                        blockButton.setOnClickListener {  viewModel.blockChat() }
                        ignoreButton.setOnClickListener { viewModel.ignoreChat() }
                    }
                    addView(binding.root)
                }
                is PrivateChatViewModel.ChatStatus.ContinuePrivateChat -> {
                    handleBlockType(status.privacyEnum, this)
                }

                is PrivateChatViewModel.ChatStatus.StartPrivateChat -> {
                    handleBlockType(status.privacyEnum, this)
                }
            }
        }
    }

    private fun handleBlockType(blockType: PrivateChatPrivacy?, layout: LinearLayout) {

            binding.footer.isEnabled = true
            val bannedBinding = DataBindingUtil.inflate<LayoutMessageBannedUserBinding>(
                layoutInflater,
                R.layout.layout_message_banned_user,
                layout,
                false
            )
            bannedBinding.text = blockType?.getDisplayText()
            layout.addView(bannedBinding.root)
    }

    fun PrivateChatPrivacy.getDisplayText(): String {
        return when(this) {
            ONLY_PREMIUM_WITH_100 -> resources.getString(R.string.private_block_message_premium_with_hundred)
            NO_ONE -> resources.getString(R.string.private_block_message_no_one)
            ONLY_USERS_WITH_100 -> resources.getString(R.string.private_block_message_user_hundred)
            ONLY_DEARS -> resources.getString(R.string.private_block_message_only_dears)
            ONLY_DEARS_FANS -> resources.getString(R.string.private_block_message_only_dear_fans)
            ONLY_PREMIUM -> resources.getString(R.string.private_message_block_only_premium)
            ANYONE -> ""
        }
    }

}