package com.app.messej.ui.home.publictab.podiums.manage

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.databinding.FragmentPodiumTheaterChargesBottomSheetBinding
import com.app.messej.ui.utils.ExpandableListBottomSheetDialogFragment
import com.app.messej.ui.utils.ViewUtils

class PodiumTheaterChargesBottomSheetFragment : ExpandableListBottomSheetDialogFragment() {

    private var mTheaterChargesAdapter: PodiumTheaterChargesAdapter? = null
    private val viewModel: PodiumTheaterChargesViewModel by viewModels()
    private lateinit var binding: FragmentPodiumTheaterChargesBottomSheetBinding
    private val args: PodiumTheaterChargesBottomSheetFragmentArgs by navArgs()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_PodiumBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_theater_charges_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        addObservers()
    }

    private fun setup() {
        viewModel.setPodiumId(args.podiumId)
        viewModel.setChargeType(args.chargeType)
        viewModel.setPodiumKind(kind = args.podiumKind)

        mTheaterChargesAdapter = PodiumTheaterChargesAdapter()

        binding.rvCharges.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            setHasFixedSize(true)
            adapter = mTheaterChargesAdapter
        }

        mTheaterChargesAdapter?.apply {
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount)
                binding.multiStateView.viewState = state
            }
        }
    }

    private fun addObservers() {
        viewModel.theaterChargesPager.observe(viewLifecycleOwner) { pagingData ->
            pagingData?.let {
                mTheaterChargesAdapter?.submitData(viewLifecycleOwner.lifecycle, pagingData)
            }
        }
    }
}