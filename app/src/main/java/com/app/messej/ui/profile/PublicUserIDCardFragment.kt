package com.app.messej.ui.profile

import android.content.Context
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatTextView
import androidx.appcompat.widget.PopupMenu
import androidx.core.content.ContextCompat
import androidx.core.view.MenuCompat
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.Constants
import com.app.messej.data.model.ReportPackage
import com.app.messej.data.model.api.gift.GiftUtils.canReceiveGifts
import com.app.messej.data.model.api.gift.GiftUtils.canSendGifts
import com.app.messej.data.model.enums.BroadcastTab
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.data.model.enums.ReportType
import com.app.messej.data.model.enums.RestoreType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserProfileContext
import com.app.messej.databinding.FragmentPublicUserIdCardBinding
import com.app.messej.databinding.LayoutIdCardPopularityInfoBinding
import com.app.messej.databinding.LayoutIdCardToolTipInformationBinding
import com.app.messej.databinding.LayoutNicknameIdCardEditBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureReportBanAllowed
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureReportUserAllowed
import com.app.messej.ui.legal.report.ReportUtils.canBan
import com.app.messej.ui.legal.report.ReportUtils.canReport
import com.app.messej.ui.utils.BindingExtensions.getRankColor
import com.app.messej.ui.utils.EnumUtils.displayText
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.hideKeyboard
import com.app.messej.ui.utils.FragmentExtensions.setupMenuItemTextColor
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.TextFormatUtils.highlightOccurrences
import com.bumptech.glide.Glide
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.kennyc.view.MultiStateView
import com.skydoves.balloon.ArrowPositionRules
import com.skydoves.balloon.Balloon
import com.skydoves.balloon.BalloonAnimation
import com.skydoves.balloon.BalloonSizeSpec
import com.skydoves.balloon.createBalloon
import com.stfalcon.imageviewer.StfalconImageViewer


class PublicUserIDCardFragment : BaseUserProfileFragment() {

    lateinit var binding: FragmentPublicUserIdCardBinding
    private val viewModel: PublicUserProfileViewModel by navGraphViewModels(R.id.nav_profile)
    private val args: PublicUserIDCardFragmentArgs by navArgs()
    override val shouldPopOnAction: Boolean
        get() = args.popOnAction

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_public_user_id_card, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.setUserId(args.useId,args.context)
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.title_cloud_identity_card)
    }

    fun observe() {
        viewModel.profile.observe(viewLifecycleOwner) { profile ->
            if (profile == null) return@observe
            Log.w("PRFL", "observe: ${profile.citizenship}")

            val tribeCount= profile.tribeParticipantsCount
            val count= getString(R.string.id_card_tribe_count,tribeCount.toString())

//            binding.textTribeCount.text = count.highlightOccurrences(tribeCount.toString()) {
//                StyleSpan(Typeface.BOLD)
//            }
            profile.citizenship?.let {
                binding.userType = resources.getString(it.displayText())
            } ?: run {
                binding.userType = ""  /**default*/
            }

            binding.rankTintColor = getRankColor(profile.popularity, requireContext())
            binding.textRank.text = if (profile.popularity!=null)getString(R.string.popularity_rank_template, profile.popularity) else "-"


            when (profile.citizenship) {
                UserCitizenship.CITIZEN -> {
                    binding.layoutPremiumRatingTribeBanner.backgroundTintList = ContextCompat.getColorStateList(requireContext(), R.color.colorPodiumSpeakerCitizen)
                    binding.textViewPremiumRating.setTextColor(ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1))
                    binding.textViewPremiumTribe.setTextColor(ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1))
                    binding.viewSeparator.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1))
                    binding.usertypeColor = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    binding.idCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_citizen)
                    binding.idCardUserStrengthDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_citizen)
                    binding.textIdCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_citizen_ambassador)
                    binding.textAboutDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_citizen_ambassador)
                    binding.idColor = ContextCompat.getColor(requireContext(), R.color.colorHuddleTagCitizen)
                    binding.textColor = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    binding.labelTextColor = binding.textColor
                    binding.userIconTint = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    binding.iconTintColor = ContextCompat.getColor(requireContext(), R.color.textColorSecondaryLight)
                    binding.isResident=false
                    binding.isVisitor = false
                    binding.userIconTextTint =ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    binding.tribeDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_tribe_citizen_count)
                    binding.haveTribe=true
                    binding.actionHuddle.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorPrimary))
                    binding.actionPostat.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorPrimary))
                    binding.actionFlash.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorPrimary))
                    binding.actionItemColor =ContextCompat.getColor(requireContext(), R.color.colorHuddleTagCitizen)
                    val colorStateList = ContextCompat.getColorStateList(requireContext(), R.color.colorPodiumSpeakerCitizen)
                    listOf(
                        binding.cardLv,
                        binding.cardRankRating,
                        binding.cardPl,
                        binding.cardOthers,
                        binding.cardOthersCount,
                        binding.cardStars
                    ).forEach { view ->
                        view.backgroundTintList = colorStateList
                    }
                }
                UserCitizenship.OFFICER -> {
                    binding.layoutPremiumRatingTribeBanner.backgroundTintList = ContextCompat.getColorStateList(requireContext(), R.color.colorPodiumSpeakerOfficer)
                    binding.textViewPremiumRating.setTextColor(ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1))
                    binding.textViewPremiumTribe.setTextColor(ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1))
                    binding.viewSeparator.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1))
                    binding.usertypeColor = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    binding.idCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_officer)
                    binding.idCardUserStrengthDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_officer)
                    binding.textIdCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_citizen_ambassador)
                    binding.textAboutDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_citizen_ambassador)
                    binding.idColor = ContextCompat.getColor(requireContext(), R.color.colorHuddleTagCitizen)
                    binding.textColor = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    binding.labelTextColor = binding.textColor
                    binding.userIconTint = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    binding.iconTintColor = ContextCompat.getColor(requireContext(), R.color.textColorSecondaryLight)
                    binding.isResident=false
                    binding.isVisitor = false
                    binding.userIconTextTint =ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    binding.tribeDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_tribe_officer_count)
                    binding.haveTribe=true
                    binding.actionHuddle.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorPrimary))
                    binding.actionPostat.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorPrimary))
                    binding.actionFlash.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorPrimary))
                    binding.actionItemColor =ContextCompat.getColor(requireContext(), R.color.colorHuddleTagCitizen)
                    val colorStateList = ContextCompat.getColorStateList(requireContext(), R.color.colorOfficerIdCardBackground)
                    listOf(
                        binding.cardLv,
                        binding.cardRankRating,
                        binding.cardPl,
                        binding.cardOthers,
                        binding.cardOthersCount,
                        binding.cardStars
                    ).forEach { view ->
                        view.backgroundTintList = colorStateList
                    }
                }

                UserCitizenship.AMBASSADOR -> {
                    binding.layoutPremiumRatingTribeBanner.backgroundTintList = ContextCompat.getColorStateList(requireContext(), R.color.colorPodiumSpeakerAmbassador)
                    binding.textViewPremiumRating.setTextColor(ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1))
                    binding.textViewPremiumTribe.setTextColor(ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1))
                    binding.viewSeparator.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1))
                    binding.usertypeColor = ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1)
                    binding.idCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_ambassador)
                    binding.idCardUserStrengthDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_ambassador)
                    binding.textIdCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_citizen_ambassador)
                    binding.textAboutDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_citizen_ambassador)
                    binding.idColor = ContextCompat.getColor(requireContext(), R.color.colorHuddleTagCitizen)
                    binding.textColor = ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1)
                    binding.labelTextColor = binding.textColor
                    binding.userIconTint = ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1)
                    binding.iconTintColor = ContextCompat.getColor(requireContext(), R.color.textColorSecondaryLight)
                    binding.isResident=false
                    binding.isVisitor = false
                    binding.userIconTextTint =ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1)
                    binding.tribeDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_tribe_ambassador_count)
                    binding.haveTribe=true
                    binding.actionHuddle.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorPrimary))
                    binding.actionPostat.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorPrimary))
                    binding.actionFlash.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorPrimary))
                    binding.actionItemColor =ContextCompat.getColor(requireContext(), R.color.colorHuddleTagCitizen)

                    val colorStateList = ContextCompat.getColorStateList(requireContext(), R.color.colorPodiumSpeakerAmbassador)
                    listOf(
                        binding.cardLv,
                        binding.cardRankRating,
                        binding.cardPl,
                        binding.cardOthers,
                        binding.cardOthersCount,
                        binding.cardStars
                    ).forEach { view ->
                        view.backgroundTintList = colorStateList
                    }

                }
                UserCitizenship.MINISTER -> {
                    binding.layoutPremiumRatingTribeBanner.backgroundTintList = ContextCompat.getColorStateList(requireContext(), R.color.colorPodiumSpeakerAdmin)
                    binding.textViewPremiumRating.setTextColor(ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary))
                    binding.textViewPremiumTribe.setTextColor(ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary))
                    binding.viewSeparator.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1))
                    binding.usertypeColor = ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary)
                    binding.idCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_leader)
                    binding.idCardUserStrengthDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_leader)
                    binding.textIdCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_leader)
                    binding.textAboutDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_leader)
                    binding.idColor = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    binding.textColor = ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary)
                    binding.labelTextColor = binding.textColor
                    binding.userIconTint = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    binding.iconTintColor = ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary)
                    binding.isResident=false
                    binding.isVisitor = false
                    binding.userIconTextTint =ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    binding.tribeDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_tribe_leader_count)
                    binding.haveTribe=true
                    binding.actionHuddle.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorAlwaysLightSecondary))
                    binding.actionPostat.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorAlwaysLightSecondary))
                    binding.actionFlash.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorAlwaysLightSecondary))
                    binding.actionItemColor =ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    val colorStateList = ContextCompat.getColorStateList(requireContext(), R.color.colorPodiumSpeakerAmbassador)
                    listOf(
                        binding.cardLv,
                        binding.cardRankRating,
                        binding.cardPl,
                        binding.cardOthers,
                        binding.cardOthersCount,
                        binding.cardStars
                    ).forEach { view ->
                        view.backgroundTintList = colorStateList
                    }

                }

                UserCitizenship.PRESIDENT -> {
                    binding.apply {
                        binding.layoutPremiumRatingTribeBanner.background = ContextCompat.getDrawable(requireContext(), R.drawable.bg_president_id_card)
                        binding.textViewPremiumRating.setTextColor(ContextCompat.getColor(requireContext(), R.color.colorPresidentIDText))
                        binding.textViewPremiumTribe.setTextColor(ContextCompat.getColor(requireContext(), R.color.colorPresidentIDText))
                        binding.viewSeparator.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1))
                        usertypeColor = ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1)
                        idCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_president)
                        idCardUserStrengthDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_president)
                        textIdCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_president)
                        textAboutDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_about_president_card)
                        idColor = ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1)
                        textColor = ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1)
                        labelTextColor = ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1)
                        userIconTint = ContextCompat.getColor(requireContext(), R.color.colorAlwaysLightPrimary)
                        iconTintColor = ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1)
                        subTitleColor = ContextCompat.getColor(requireContext(), R.color.colorPresidentIDText)
                        isResident = false
                        binding.isVisitor = false
                        userIconTextTint = ContextCompat.getColor(requireContext(), R.color.colorAlwaysLightPrimary)
                        tribeDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_tribe_president_count)
                        haveTribe = true
                        isPresident = true
                        val layoutParams = textIdCard.layoutParams as ViewGroup.MarginLayoutParams
                        layoutParams.setMargins(16,30,0,0)
                        textIdCard.layoutParams = layoutParams
                        layoutHeader.setPadding(48, 32, 60, 40)
                        textCitizenship.setPadding(0, 0, 16, 0)
                        layoutUserStrength.setPadding(40, 32, 40, 32)
                        cardViewItems.radius = 0F

                        val elementSpacing = resources.getDimensionPixelOffset(R.dimen.element_spacing)
                        val layoutParamsTextViewStrength = textStrength.layoutParams as ViewGroup.MarginLayoutParams
                        layoutParamsTextViewStrength.marginStart = elementSpacing
                        textStrength.layoutParams = layoutParamsTextViewStrength

                        
                        val tribeCount = profile.tribeParticipantsCount
                        val countText = getString(R.string.id_card_tribe_count, tribeCount.toString())
                        textViewPremiumTribe.text = countText.highlightOccurrences(tribeCount.toString()) {
                            ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1))
                        }

//                        // Highlight the rating in pink
                        val rating = profile.flaxRatePercentage?.toInt()
                        val ratingText = getString(R.string.user_strength_rating_prefix, rating.toString())
                        textViewPremiumRating.text = ratingText.highlightOccurrences("$rating%") {
                            ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1))
                        }

                        binding.actionHuddle.surfaceColor.background = GradientDrawable(GradientDrawable.Orientation.LEFT_RIGHT, intArrayOf(0XFFA67E20.toInt(),0XFFD3B55B.toInt(),0XFFFFEC95.toInt(),0XFFD3B55B.toInt(),0XFFA67E20.toInt()))
                        binding.actionPostat.surfaceColor.background = GradientDrawable(GradientDrawable.Orientation.LEFT_RIGHT, intArrayOf(0XFFA67E20.toInt(),0XFFD3B55B.toInt(),0XFFFFEC95.toInt(),0XFFD3B55B.toInt(),0XFFA67E20.toInt()))
                        binding.actionFlash.surfaceColor.background = GradientDrawable(GradientDrawable.Orientation.LEFT_RIGHT, intArrayOf(0XFFA67E20.toInt(),0XFFD3B55B.toInt(),0XFFFFEC95.toInt(),0XFFD3B55B.toInt(),0XFFA67E20.toInt()))
                        binding.actionItemColor =ContextCompat.getColor(requireContext(), R.color.colorPresidentIDText)
                        val colorStateList = ContextCompat.getColorStateList(requireContext(), R.color.colorOfficerIdCardBackground)
                        listOf(
                           cardLv,
                            cardRankRating,
                            cardPl,
                            cardOthers,
                            cardOthersCount,
                            cardStars
                        ).forEach { view ->
                            view.backgroundTintList = colorStateList
                        }

                    }
                }

                UserCitizenship.GOLDEN -> {
                    binding.apply {
                        binding.layoutPremiumRatingTribeBanner.background = ContextCompat.getDrawable(requireContext(), R.drawable.bg_tribe_count_golden)
                        binding.textViewPremiumRating.setTextColor(ContextCompat.getColor(requireContext(), R.color.white))
                        binding.textViewPremiumTribe.setTextColor(ContextCompat.getColor(requireContext(), R.color.white))
                        binding.viewSeparator.visibility = View.GONE
                        usertypeColor = ContextCompat.getColor(requireContext(), R.color.white)
                        idCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_golden)
                        idCardUserStrengthDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_golden)
                        textIdCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_golden)
                        textAboutDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_golden)
                        idColor = ContextCompat.getColor(requireContext(), R.color.white)
                        textColor = ContextCompat.getColor(requireContext(), R.color.colorFlashatGolden)
                        labelTextColor = ContextCompat.getColor(requireContext(), R.color.colorFlashatGolden)
                        userIconTint = ContextCompat.getColor(requireContext(), R.color.colorFlashatGolden)
                        iconTintColor = ContextCompat.getColor(requireContext(), R.color.white)
                        subTitleColor = ContextCompat.getColor(requireContext(), R.color.white)
                        isResident = false
                        binding.isVisitor = false
                        userIconTextTint = ContextCompat.getColor(requireContext(), R.color.colorAlwaysLightPrimary)
                        tribeDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_tribe_president_count)
                        haveTribe = true
                        isPresident = true
                        val layoutParams = textIdCard.layoutParams as ViewGroup.MarginLayoutParams
                        layoutParams.setMargins(16,30,0,0)
                        textIdCard.layoutParams = layoutParams
                        layoutHeader.setPadding(48, 32, 60, 40)
                        textCitizenship.setPadding(0, 0, 16, 0)
                        layoutUserStrength.setPadding(40, 32, 40, 32)
                        cardViewItems.radius = 0F

                        val elementSpacing = resources.getDimensionPixelOffset(R.dimen.element_spacing)
                        val layoutParamsTextViewStrength = textStrength.layoutParams as ViewGroup.MarginLayoutParams
                        layoutParamsTextViewStrength.marginStart = elementSpacing
                        textStrength.layoutParams = layoutParamsTextViewStrength


                        val tribeCount = profile.tribeParticipantsCount
                        val countText = getString(R.string.id_card_tribe_count, tribeCount.toString())
                        textViewPremiumTribe.text = countText.highlightOccurrences(tribeCount.toString()) {
                            ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.colorAlwaysLightSecondaryLightest))
                        }

//                        // Highlight the rating in pink
                        val rating = profile.flaxRatePercentage?.toInt()
                        val ratingText = getString(R.string.user_strength_rating_prefix, rating.toString())
                        textViewPremiumRating.text = ratingText.highlightOccurrences("$rating%") {
                            ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.colorAlwaysLightSecondaryLightest))
                        }
                        binding.actionHuddle.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorFlashatGolden))
                        binding.actionPostat.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorFlashatGolden))
                        binding.actionFlash.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorFlashatGolden))
                        binding.actionItemColor =ContextCompat.getColor(requireContext(), R.color.white)

                        val colorStateList = ContextCompat.getColorStateList(requireContext(), R.color.colorGoldenIdCardBackground)
                        listOf(
                            cardLv,
                          cardRankRating,
                            cardPl,
                           cardOthers,
                         cardOthersCount,
                        cardStars
                        ).forEach { view ->
                            view.backgroundTintList = colorStateList
                        }


                    }
                }

                UserCitizenship.RESIDENT -> {
                    binding.layoutPremiumRatingTribeBanner.backgroundTintList = ContextCompat.getColorStateList(requireContext(), R.color.resident_rating_text_color)
                    binding.textViewPremiumRating.setTextColor(ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary))
                    binding.textViewPremiumTribe.setTextColor(ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary))
                    binding.viewSeparator.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary))
                    binding.usertypeColor = ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary)
                    binding.idCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_resident)
                    binding.idCardUserStrengthDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_resident)
                    binding.textIdCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_resident_latest)
                    binding.textAboutDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_resident_latest)
                    binding.idColor = ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary)
                    binding.textColor = ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary)
                    binding.labelTextColor = binding.textColor
                    binding.userIconTint = ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary)
                    binding.iconTintColor = ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary)
                    binding.isResident=true
                    binding.isVisitor = false
                    binding.userIconTextTint =ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary)
                    binding.haveTribe=false
                    binding.actionHuddle.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary))
                    binding.actionPostat.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary))
                    binding.actionFlash.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary))
                    binding.actionItemColor =ContextCompat.getColor(requireContext(), R.color.white)
                    val colorStateList = ContextCompat.getColorStateList(requireContext(), R.color.colorAlwaysLightSurfaceSecondary)
                    listOf(
                        binding.cardLv,
                        binding.cardRankRating,
                        binding.cardPl,
                        binding.cardOthers,
                        binding.cardOthersCount,
                        binding.cardStars
                    ).forEach { view ->
                        view.backgroundTintList = colorStateList
                    }

                }
                UserCitizenship.VISITOR -> {
                    binding.layoutPremiumRatingTribeBanner.backgroundTintList = ContextCompat.getColorStateList(requireContext(), R.color.colorPodiumSpeakerResident)
                    binding.textViewPremiumRating.setTextColor(ContextCompat.getColor(requireContext(), R.color.textColorPrimary))
                    binding.textViewPremiumTribe.setTextColor(ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary))
                    binding.viewSeparator.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary))
                    binding.usertypeColor = ContextCompat.getColor(requireContext(), R.color.textColorAlwaysLightPrimary)
                    binding.idCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_visitor)
                    binding.idCardUserStrengthDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_visitor)
                    binding.textIdCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_visitor)
                    binding.textAboutDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_visitor)
                    binding.idColor = ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary)
                    binding.textColor = ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary)
                    binding.labelTextColor = binding.textColor
                    binding.userIconTint = ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary)
                    binding.iconTintColor = ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary)
                    binding.isResident=true
                    binding.isVisitor = true
                    binding.userIconTextTint =ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary)
                    binding.haveTribe=false
                    binding.actionHuddle.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorStateAffairVisitor))
                    binding.actionPostat.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorStateAffairVisitor))
                    binding.actionFlash.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorStateAffairVisitor))
                    binding.actionItemColor =ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary)

                    val colorStateList = ContextCompat.getColorStateList(requireContext(), R.color.colorAlwaysLightSurfaceSecondary)
                    listOf(
                        binding.cardLv,
                        binding.cardRankRating,
                        binding.cardPl,
                        binding.cardOthers,
                        binding.cardOthersCount,
                        binding.cardStars
                    ).forEach { view ->
                        view.backgroundTintList = colorStateList
                    }
                }

                else -> {
                    binding.layoutPremiumRatingTribeBanner.backgroundTintList = ContextCompat.getColorStateList(requireContext(), R.color.textColorOnPrimary)
                    binding.textViewPremiumRating.setTextColor(ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary))
                    binding.textViewPremiumTribe.setTextColor(ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary))
                    binding.viewSeparator.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary))
                    binding.usertypeColor = ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary)
                    binding.idColor = ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary)
                    binding.textColor = ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary)
                    binding.labelTextColor = binding.textColor
                    binding.userIconTint = ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary)
                    binding.iconTintColor = ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary)
                    binding.isResident=false
                    binding.userIconTextTint =ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary)
                    binding.haveTribe=false
                    binding.actionHuddle.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorAlwaysLightSecondary))
                    binding.actionPostat.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorAlwaysLightSecondary))
                    binding.actionFlash.surfaceColor.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorAlwaysLightSecondary))
                    binding.layoutLv.backgroundTintList = ContextCompat.getColorStateList(requireContext(), R.color.colorAlwaysLightSurfaceSecondary)
                    binding.cardRankRating.backgroundTintList = ContextCompat.getColorStateList(requireContext(), R.color.colorAlwaysLightSurfaceSecondary)
                    binding.cardPl.backgroundTintList = ContextCompat.getColorStateList(requireContext(), R.color.colorAlwaysLightSurfaceSecondary)
                    binding.cardOthers.backgroundTintList = ContextCompat.getColorStateList(requireContext(), R.color.colorAlwaysLightSurfaceSecondary)
                    binding.cardOthersCount.backgroundTintList = ContextCompat.getColorStateList(requireContext(), R.color.colorAlwaysLightSurfaceSecondary)
                    binding.cardStars.backgroundTintList = ContextCompat.getColorStateList(requireContext(), R.color.colorAlwaysLightSurfaceSecondary)
                }
            }

            viewModel.chatStatus.observe(viewLifecycleOwner) {
                Log.d(Constants.FLASHAT_TAG, "chatStatus: $it")
            }
            viewModel.nickNameOrName.observe(viewLifecycleOwner) {
                Log.d(Constants.FLASHAT_TAG, "usernameOrNickName: $it")
            }
            viewModel._countryList.observe(viewLifecycleOwner){
                Log.d("CountryFlag","countryFlag: ${profile?.countryCode}")
                viewModel.getFlag(it,profile?.countryCode)
                binding.idCardFlag.setImageResource(viewModel.countryFlag ?: 0)
            }
        }
    }

    private fun setup() {
        binding.btnIdCardPopup.setOnClickListener {

            val profile = viewModel.profile.value?: return@setOnClickListener
            val isMinisterCanBlockVisitor = (viewModel.user.citizenship==UserCitizenship.MINISTER && profile.citizenship==UserCitizenship.VISITOR)
            val notSelf =  viewModel.isCurrentUser.value == false
            val canTemporarilyBlockAnyUser = viewModel.user.userEmpowerment?.canTemporarilyBlockAnyUser==true
            val isPresidentUser = viewModel.user.citizenship==UserCitizenship.PRESIDENT || profile.citizenship==UserCitizenship.PRESIDENT
            val isGoldenUser =  profile.citizenship?.isGolden?: false

            val popup = PopupMenu(requireContext(), it)
            MenuCompat.setGroupDividerEnabled(popup.menu, true)
            popup.menuInflater.inflate(R.menu.menu_idcard, popup.menu)
            popup.setForceShowIcon(true)
            popup.menu.apply {
                findItem(R.id.id_card_send_gift).isVisible = notSelf && viewModel.user.canSendGifts().toBoolean && profile.citizenship.canReceiveGifts() && viewModel.user.blockedByAdmin==false && profile.blockedByAdmin==false
                findItem(R.id.id_card_send_flax).isVisible = notSelf && (profile.citizenship != UserCitizenship.VISITOR && profile.citizenship != UserCitizenship.GOLDEN) && (viewModel.user.citizenship != UserCitizenship.VISITOR)  && viewModel.user.blockedByAdmin==false && profile.blockedByAdmin==false
                findItem(R.id.id_card_follow).isVisible = profile.isSuperstar != true && (notSelf)
                findItem(R.id.id_card_follow).setTitle(if (profile.isStar) R.string.user_action_unfollow else R.string.user_action_follow)
                findItem(R.id.id_card_block).setTitle(if (viewModel.chatInfo.value?.chatBlocked == true) R.string.user_action_unblock_from_private_message else R.string.user_action_block_from_private_message)
                findItem(R.id.id_card_block).isVisible = args.context == UserProfileContext.PRIVATE_CHAT && viewModel.chatStatus.value != PublicUserProfileViewModel.PrivateChatStatus.RESTRICTED
                findItem(R.id.id_card_restrict).isVisible = args.context == UserProfileContext.PRIVATE_CHAT && viewModel.chatStatus.value == PublicUserProfileViewModel.PrivateChatStatus.ACTIVE
                findItem(R.id.id_card_manage_notifications).isVisible = args.context == UserProfileContext.STAR_BROADCAST
                findItem(R.id.id_card_send_private_message).isVisible =
                    (args.context != UserProfileContext.PRIVATE_CHAT && args.context != UserProfileContext.STAR_BROADCAST || args.context == UserProfileContext.HUDDLE_LIST) && (notSelf)
                findItem(R.id.id_card_remove_broadcast).isVisible = args.context == UserProfileContext.BROADCAST_LIST
                findItem(R.id.id_card_remove_broadcast).setTitle(if (profile.likersPrivacy == false) R.string.user_action_remove_from_broadcast else R.string.user_action_add_to_broadcast)
                findItem(R.id.id_card_ban).isVisible = (notSelf && (canTemporarilyBlockAnyUser || isMinisterCanBlockVisitor)) && !isPresidentUser && !isGoldenUser
                findItem(R.id.id_card_block_from_app).apply {
                    //Hiding block from flashat menu item for all users as per requirement
                    isVisible = false
                    setTitle(if (profile.blockedByLeader == false) R.string.user_action_block_from_app else R.string.user_action_unblock_from_temporarily)
                }
                findItem(R.id.id_card_edit_nick_name).isVisible = notSelf

                findItem(R.id.id_card_report).apply {
                    isVisible = notSelf && viewModel.user.canReport(profile)
                    setupMenuItemTextColor(color = R.color.colorError, context = requireContext(), isTextBold = true)
                }
                findItem(R.id.id_card_ban).apply {
                    isVisible = notSelf && viewModel.user.canBan(profile)
                    setupMenuItemTextColor(color = R.color.colorError, context = requireContext(), isTextBold = true)
                }
                findItem(R.id.id_card_restore_rating).isVisible = viewModel.profile.value?.flaxRatePercentage!=100.0 && profile.premiumUser && viewModel.user.premiumUser
            }
            popup.setOnMenuItemClickListener { item ->
                when (item?.itemId) {
                    R.id.id_card_send_gift -> findNavController().navigateSafe(PublicUserIDCardFragmentDirections.actionGlobalGiftFragment(args.useId, giftContext = GiftContext.GIFT_ID_CARD))
                    R.id.id_card_send_flax -> findNavController().navigateSafe(PublicUserIDCardFragmentDirections.actionGlobalFlaxSendFlax(args.useId))
                    R.id.id_card_follow -> viewModel.toggleFollow()
                    R.id.id_card_block -> if (viewModel.chatInfo.value?.chatBlocked == true) viewModel.unblockChat() else viewModel.blockChat()
                    R.id.id_card_restrict -> findNavController().navigateSafe(PublicUserIDCardFragmentDirections.actionGlobalPrivateMessageRestrictDialogFragment())
                    R.id.id_card_remove_broadcast -> if (profile.likersPrivacy == true) viewModel.hideLiker(false) else viewModel.hideLiker(true)
                    R.id.id_card_send_private_message -> viewModel.navigateToPrivateMessage()
                    R.id.id_card_block_from_app -> blockUserTemporarily()
                    R.id.id_card_edit_nick_name -> handleNickNameChange()
                    R.id.id_card_report -> {
                        ensureReportUserAllowed {
                            Log.e("RPF", "sending to report: ${ReportPackage.User(profile.asBasicUser(), ReportType.REPORT).serialize()}")
                            findNavController().navigateSafe(PublicUserIDCardFragmentDirections.actionGlobalReportFragment(ReportPackage.User(profile.asBasicUser(), ReportType.REPORT).serialize()))
                        }
                    }
                    R.id.id_card_ban -> {
                        ensureReportBanAllowed {
                            findNavController().navigateSafe(PublicUserIDCardFragmentDirections.actionGlobalReportFragment(ReportPackage.User(profile.asBasicUser(), ReportType.BAN).serialize()))
                        }
                    }
                    R.id.id_card_restore_rating ->{
                        findNavController().navigateSafe(PublicUserIDCardFragmentDirections.actionHomeBusinessFragmentToRestoreRatingFragment(false, RestoreType.FOR_OTHER, userName = viewModel.profile.value?.name?:""))
                    }
                }
                return@setOnMenuItemClickListener true
            }
            popup.show()
        }
        viewModel.dataLoading.observe(viewLifecycleOwner) {
            if (it) binding.multiStateView.viewState = MultiStateView.ViewState.LOADING
            else binding.multiStateView.viewState = MultiStateView.ViewState.CONTENT
        }

        binding.idCardDp.setOnClickListener {
            handleProfileDpClick()
        }
        binding.layoutPl.setOnClickListener{
            if (viewModel.isCurrentUser.value == true) {
                showIdCardInfo(header = getString(R.string.id_card_skill_title_self), body = getString(R.string.id_card_skill_desc), action = true)
            }else{
                showIdCardInfo(header = getString(R.string.id_card_skill_title_other,if(viewModel.profile.value?.showCrownForSkill==true) getString(R.string.max_user_levels) else viewModel.profile.value?.plModified), body = getString(R.string.id_card_skill_desc), action = true)

            }
        }

        binding.layoutRank.setOnClickListener {
                showPopularityInfoPopup(requireContext())
        }

        binding.layoutLv.setOnClickListener {
            if (viewModel.isCurrentUser.value == true) {
                showIdCardInfo(header = getString(R.string.id_card_generosity_title_self,viewModel.user.coinsNeededNextContributorLevel?.toInt().toString()), body = getString(R.string.id_card_generosity_desc), action = true)
            }else{
                showIdCardInfo(header = getString(R.string.id_card_generosity_title_other,if(viewModel.profile.value?.showCrownForGenerosity==true) getString(R.string.max_user_levels) else viewModel.profile.value?.lvModified), body = getString(R.string.id_card_generosity_desc), action = true)

            }
        }
        binding.layoutDears.setOnClickListener {
            if (viewModel.isCurrentUser.value == true) {
                findNavController().navigateSafe(PublicUserIDCardFragmentDirections.actionGlobalPublicBroadcastStandaloneFragment(BroadcastTab.TAB_DEARS.ordinal))
            }
            else{
                showIdCardInfo(body = getString(R.string.id_card_dears_desc))
            }
        }
        binding.layoutFans.setOnClickListener {
            if (viewModel.isCurrentUser.value == true) {
                findNavController().navigateSafe(PublicUserIDCardFragmentDirections.actionGlobalPublicBroadcastStandaloneFragment(BroadcastTab.TAB_FANS.ordinal))
            }
            else{
                showIdCardInfo(body = getString(R.string.id_card_fans_desc))
            }
        }
        binding.layoutLikers.setOnClickListener {
            if (viewModel.isCurrentUser.value == true) {
                findNavController().navigateSafe(PublicUserIDCardFragmentDirections.actionGlobalPublicBroadcastStandaloneFragment(BroadcastTab.TAB_LIKERS.ordinal))
            }
            else{
                showIdCardInfo(body = getString(R.string.id_card_likers_desc))
            }
        }
        binding.layoutStars.setOnClickListener {
            if (viewModel.isCurrentUser.value == true){
                findNavController().navigateSafe(PublicUserIDCardFragmentDirections.actionGlobalPublicStarsStandaloneFragment())
            }
            else{
                showIdCardInfo(body = getString(R.string.id_card_stars_desc))
            }
        }
        binding.layoutOthers.setOnClickListener {
            findNavController().navigateSafe(PublicUserIDCardFragmentDirections.actionPublicUserIdCardLatestToPublicTotalDetailsBottomSheetFragment(viewModel.userIdLiveData.value?:0))

        }
        binding.textCitizenship.setOnClickListener {
            val balloon = viewModel.profile.value?.citizenship?.let { citizenship -> createTooltipBalloon(requireContext(), citizenship) }
            balloon?.showAlignBottom(binding.textCitizenship)
        }

        binding.actionHuddle.root.setOnClickListener {
            findNavController().navigateSafe(PublicUserIDCardFragmentDirections.actionGlobalOthersHuddleList(userId = viewModel.profile.value?.id?:0, userName = viewModel.profile.value?.name?:""))
        }
        binding.actionPostat.root.setOnClickListener {
            if (viewModel.isCurrentUser.value == true){
                findNavController().navigateSafe(PublicUserIDCardFragmentDirections.actionPublicUserIdCardLatestToMyPostatFeedFragment())
            }else{
                findNavController().navigateSafe(PublicUserIDCardFragmentDirections.actionPublicUserIdCardLatestToUserPostatFeedFragment(viewModel.profile.value?.id?:0, podiumId = null))
            }
        }
        binding.actionFlash.root.setOnClickListener {
            findNavController().navigateSafe(PublicUserIDCardFragmentDirections.actionGlobalNavFlashUser(viewModel.profile.value?.id?:0))
        }

    }

    private fun handleProfileDpClick() {
        viewModel.profile.value?.thumbnail?.let { media ->
            StfalconImageViewer.Builder(context, listOf(media)) { imageView, image ->
                Glide.with(requireContext()).load(image).into(imageView)
            }.withTransitionFrom(binding.idCardDp).withHiddenStatusBar(false).build().show()
        }
    }

    private fun handleNickNameChange() {
        viewModel.showNickNameEdit()
        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutNicknameIdCardEditBinding>(layoutInflater, R.layout.layout_nickname_id_card_edit, null, false)
            view.viewModel = viewModel
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(false)
            view.actionSave.setOnClickListener {
                hideKeyboard()
                viewModel.updateUserNickName()
                dismiss()
            }
            view.actionCancel.setOnClickListener {
                hideKeyboard()
                viewModel.cancelNickNameEdit()
                dismiss()
            }
        }
    }

    private fun showIdCardInfo(header: String?=null,body:String?=null,action:Boolean = false) {
        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutIdCardToolTipInformationBinding>(layoutInflater, R.layout.layout_id_card_tool_tip_information, null, false)
            view.textHeader = header
            view.textBodys = body
            view.actionVisibility = action
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(true)
            view.actionClose.setOnClickListener {
                dismiss()
            }
            view.actionCloseOthers.setOnClickListener {
                dismiss()
            }
        }
    }

    private fun createTooltipBalloon(context: Context, citizenship: UserCitizenship): Balloon {
        return createBalloon(context) {
            setHeight(BalloonSizeSpec.WRAP)
            setLayout(R.layout.layout_id_card_citizenship_tool_tip)
            setArrowPositionRules(ArrowPositionRules.ALIGN_ANCHOR)
            setCornerRadius(8f)
            setWidth(BalloonSizeSpec.WRAP)
            setMargin(6)
            setBackgroundColorResource(R.color.colorAlwaysLightSurface)
            setBalloonAnimation(BalloonAnimation.ELASTIC)
            build()
        }.apply {
            getContentView().findViewById<AppCompatTextView>(R.id.message).text = if (viewModel.isCurrentUser.value == true) {
                when (citizenship) {
                    UserCitizenship.VISITOR -> getString(R.string.idCard_citizenship_action_own_visitor) + getString(R.string.idCard_citizenship_action_other_resident)
                    UserCitizenship.RESIDENT -> getString(R.string.idCard_citizenship_action_own_resident) + getString(R.string.idCard_citizenship_action_other_citizen)
                    UserCitizenship.CITIZEN ->  getString(R.string.idCard_citizenship_action_other_officer)
                    UserCitizenship.OFFICER ->  getString(R.string.idCard_citizenship_action_other_ambassador)
                    UserCitizenship.AMBASSADOR -> getString(R.string.idCard_citizenship_action_other_minister)
                    UserCitizenship.MINISTER -> getString(R.string.idCard_citizenship_action_own_minister)
                    UserCitizenship.PRESIDENT ->  getString(R.string.idCard_citizenship_action_other_president) /*getString(R.string.idCard_citizenship_action_own_president)*/
                    else -> ""
                }
            } else when (citizenship) {
                UserCitizenship.VISITOR -> getString(
                    R.string.idCard_citizenship_action_other_visitor
                )
                UserCitizenship.RESIDENT -> getString(R.string.idCard_citizenship_action_other_resident)
                UserCitizenship.CITIZEN -> getString(R.string.idCard_citizenship_action_other_citizen)
                UserCitizenship.OFFICER -> getString(R.string.idCard_citizenship_action_other_officer)
                UserCitizenship.AMBASSADOR -> getString(R.string.idCard_citizenship_action_other_ambassador)
                UserCitizenship.MINISTER -> getString(R.string.idCard_citizenship_action_other_minister)
                UserCitizenship.PRESIDENT -> getString(R.string.idCard_citizenship_action_other_president)
                UserCitizenship.GOLDEN -> getString(R.string.idCard_citizenship_action_other_president)
            }
        }

    }
    private fun showPopularityInfoPopup(context: Context) {
        val builder = MaterialAlertDialogBuilder(context)
        val inflater = LayoutInflater.from(context)
        val binding: LayoutIdCardPopularityInfoBinding = DataBindingUtil.inflate(
            inflater,
            R.layout.layout_id_card_popularity_info,
            null,
            false
        )

        builder.setView(binding.root)
        val dialog = builder.create()

        binding.actionMoreDetails.isVisible = viewModel.isCurrentUser.value == true

        binding.actionMoreDetails.setOnClickListener {
            findNavController().navigateSafe(PublicUserIDCardFragmentDirections.actionPublicUserIdCardLatestToPopularityDetailsBottomSheetDialogFragment())
            dialog.dismiss()
        }

        binding.actionClose.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }
}