package com.app.messej.ui.home.publictab.podiums.live

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.appcompat.widget.PopupMenu
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.enums.PodiumEnforcementDialogType
import com.app.messej.data.model.enums.PodiumTab
import com.app.messej.data.model.enums.TheaterCharge
import com.app.messej.data.model.enums.TheaterJoinType
import com.app.messej.databinding.FragmentPodiumLiveTheaterBinding
import com.app.messej.databinding.ItemPodiumLikesContainerBinding
import com.app.messej.databinding.ItemPodiumSpeakerBinding
import com.app.messej.databinding.ItemPodiumSpeakerHeaderBinding
import com.app.messej.databinding.ItemPodiumSpeakerMainBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensurePodiumCreateAllowed
import com.app.messej.ui.home.promobar.CommonPromoBarUtil.setupPodiumPromoBoard
import com.app.messej.ui.home.promobar.FadedMarqueeText
import com.app.messej.ui.home.promobar.toAnnotatedString
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.showPodiumCoinInfoDialog
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.showPodiumLikeInfoDialog
import com.app.messej.ui.home.publictab.podiums.model.PodiumLiveChatUIModel
import com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel.ActiveSpeakerUIModel
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.showInsufficientBalanceAlert
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.button.MaterialButton
import com.kennyc.view.MultiStateView
import com.scwang.smart.refresh.layout.SmartRefreshLayout

class PodiumLiveTheaterFragment : PodiumLiveAbstractFragment() {

    override val viewModel: PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)

    val args: PodiumLiveTheaterFragmentArgs by navArgs()

    override val podiumIdArg: String
        get() = args.podiumId

    override val enableScrollForTab: PodiumTab?
        get() = PodiumTab.fromInt(args.enableScrollForTab)

    private var mSpeakerAdapter: PodiumLiveSpeakersAdapter? = null

    override lateinit var binding: FragmentPodiumLiveTheaterBinding

    override val liveBindingElements = object: LiveBindingElements {

        override val liveChat: RecyclerView?
            get() = null

        override fun showLocalVideoSurface(show: Boolean) {
            binding.mainScreen.showVideo = show
        }

        override fun onPodiumLoading(loading: Boolean) {
            binding.speakerListMultiStateView.viewState = if (loading) MultiStateView.ViewState.LOADING else MultiStateView.ViewState.CONTENT
            binding.header.podiumHeaderMultiStateView.viewState = if (loading) MultiStateView.ViewState.LOADING else MultiStateView.ViewState.CONTENT
        }

        override val likesContainer: ItemPodiumLikesContainerBinding
            get() = binding.likesContainer

        override val chatSend: MaterialButton
            get() = binding.chatSendButton

        override val actionPaidLike: MaterialButton
            get() = binding.actionLike

        override val actionShare: MaterialButton?
            get() = null

        override val actionDecorHolderTop: LinearLayoutCompat
            get() = binding.actionDecorHolderTop

        override val liveCounter: ViewGroup
            get() = binding.header.liveCounter

        override val scrollerLayout: SmartRefreshLayout
            get() = binding.refreshLayout

        override val anthemOverlay: ComposeView?
            get() = binding.anthemOverlay

    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_live_theater, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initAdapter()
        setup()
        addObservers()
    }

    override fun ItemPodiumSpeakerMainBinding.onMainScreenSetup() {
        showCameraToggle = true
    }

    private fun setup() {

        binding.header.likeCounter.setOnClickListener {
            if (viewModel.canShowTheaterCharges()) {
                ensureAnthemNotPlaying {
                    findNavController().navigateSafe(PodiumLiveTheaterFragmentDirections.actionPodiumLiveTheaterFragmentToPodiumTheaterLikesBottomSheetFragment(args.podiumId))
                }
            }else{
                showPodiumLikeInfoDialog()
            }
        }

        binding.header.coinCounter.setOnClickListener {
            if (viewModel.canShowTheaterCharges()) {
                ensureAnthemNotPlaying {
                    viewModel.podium.value?.kind?.let {
                        findNavController().navigateSafe(PodiumLiveTheaterFragmentDirections.actionGlobalPodiumChargesBottomSheetFragment(podiumId = args.podiumId, chargeType = TheaterCharge.SPEAKER, podiumKind = it))
                    }
                }
            }else{
                showPodiumCoinInfoDialog()
            }
        }

        binding.header.podiumDp.setOnClickListener {
            ensureAnthemNotPlaying {
                showMoreMenu(it)
            }
        }
//        binding.header.exitButton.setOnClickListener {
//            onExit()
//        }
        binding.mainScreen.clickTarget.setOnClickListener {
            val item = viewModel.mainScreenSpeaker.value ?: return@setOnClickListener
            onSpeakerItemClick(item)
        }
        binding.mainScreen.placeholderPlusButton.setOnClickListener {
            requestToMainScreen()
        }
        binding.mainScreenTwo.clickTarget.setOnClickListener {
            val item = viewModel.secondMainScreenSpeaker.value ?: return@setOnClickListener
            onSpeakerItemClick(item)
        }

        binding.mainScreenTwo.placeholderPlusButton.setOnClickListener {
            ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                if (!checkUserCanSpeakByRating()) return@ensurePodiumCreateAllowed
                requestToStage()
            }
        }

        binding.mainScreenTwo.mainScreen.setOnClickListener {
            val id = viewModel.secondMainScreenSpeakerId.value?: return@setOnClickListener
            val action = PodiumLiveTheaterFragmentDirections.actionPodiumLiveTheaterFragmentToPodiumSpeakerActionsBottomSheetFragment(id)
            findNavController().navigateSafe(action)
        }

        fun ItemPodiumSpeakerMainBinding.customize() {
            placeholderImage.setImageResource(R.drawable.ic_podium_theater_stage_placeholder)
            placeholderText.isVisible = true
            placeholderText.setText(R.string.podium_theater_stage_prompt)

            showLikes = true
        }

        binding.mainScreen.customize()
        binding.mainScreenTwo.customize()

        binding.addChatButton.setOnClickListener {
            viewModel.toggleTheaterChatInput(true)
        }

        binding.welcomeHolder.setContent {
            val messageState = viewModel.theaterLastMessage.observeAsState()
            val message = messageState.value

            val text = if(message==null) ""
            else when(message) {
                is PodiumLiveChatUIModel.ChatMessage -> message.message.lines().joinToString(" ")
                is PodiumLiveChatUIModel.UserJoined -> getString(R.string.podium_theater_chat_ticker_new_user,message.senderDetails.name)
                is PodiumLiveChatUIModel.PaidLike -> getString(R.string.podium_theater_chat_ticker_heart,message.senderDetails.name)
                else -> ""
            }

            FadedMarqueeText(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight(),
                text = text.toAnnotatedString(),
                style = TextStyle(
                    color = colorResource(R.color.colorPrimaryDarker),
                    fontSize = 16.sp
                ),
                spacerWidthDp = 24.dp
            )
        }

        setupPodiumPromoBoard(binding.ticker)

        binding.chatCancelButton.setOnClickListener {
            viewModel.toggleTheaterChatInput(false)
        }
    }

    private fun onSpeakerItemClick(item: ActiveSpeakerUIModel) {
        ensureAnthemNotPlaying {
            val action = PodiumLiveTheaterFragmentDirections.actionPodiumLiveTheaterFragmentToPodiumSpeakerActionsBottomSheetFragment(item.speaker.id)
            findNavController().navigateSafe(action)
        }
    }

    private fun initAdapter() {
        Log.d("PLF", "initAdapter: create new adapter")

        mSpeakerAdapter = PodiumLiveSpeakersAdapter(layoutInflater, mutableListOf(), object : PodiumLiveSpeakersAdapter.SpeakerListener {
            override fun onEmptySpeakZoneClick() {
                ensureAnthemNotPlaying {
                    ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                        if (!checkUserCanSpeakByRating()) return@ensurePodiumCreateAllowed
                        requestToAudience()
                    }
                }
            }

            override fun decorateSpeakerTile(item: ActiveSpeakerUIModel, binding: ItemPodiumSpeakerBinding) {
                binding.showLikes = true
            }

            override fun onActiveSpeakerClick(activeSpeaker: ActiveSpeakerUIModel, view: View) {
                onSpeakerItemClick(activeSpeaker)
            }

            override fun setSpeakerTitle(speakerHeader: ItemPodiumSpeakerHeaderBinding, item: ActiveSpeakerUIModel) {
                speakerHeader.setSpeakerTitle(item)
            }

            override fun showAudioControls(speakerId: Int): Boolean {
                return viewModel.user.id == speakerId
            }

            override fun toggleMic(speakerId: Int, isMuted: Boolean) {
                viewModel.muteToggleSelf()
            }
        })

        binding.speakerList.apply {
            layoutManager = GridLayoutManager(context, 4).apply {}
            setHasFixedSize(true)
            adapter = mSpeakerAdapter
        }
    }

    private fun addObservers() {

        viewModel.iAmElevated.observe(viewLifecycleOwner) {
            binding.mainScreen.placeholderPlusButton.isClickable = it==true
        }
        viewModel.mainScreenSpeaker.observe(viewLifecycleOwner) {
            Log.w("PLVM", "observe: mainScreenSpeaker $it")
            setMainScreenSpeaker(binding.mainScreen,it)
        }

        viewModel.secondMainScreenSpeaker.observe(viewLifecycleOwner) {
            Log.w("PLVM", "observe: mainScreenSpeakerTwo $it")
            setMainScreenSpeaker(binding.mainScreenTwo,it)
        }
        viewModel.mainScreenSpeakerId.observe(viewLifecycleOwner) {
            it?: return@observe
            binding.mainScreen.setupMainScreen(it)
        }
        viewModel.secondMainScreenSpeakerId.observe(viewLifecycleOwner) {
            Log.w("PLVM", "secondMainScreenSpeakerId: $it")
            it?: return@observe
            binding.mainScreenTwo.setupMainScreen(it)
        }
        viewModel.myVideoIsTurnedOn.observe(viewLifecycleOwner) {
            it?: return@observe
            val screen = if (viewModel.mainScreenSpeakerId.value == viewModel.user.id) binding.mainScreen
            else if(viewModel.secondMainScreenSpeakerId.value == viewModel.user.id) binding.mainScreenTwo
            else return@observe
            screen.setupMainScreen(viewModel.user.id)
        }
        viewModel.showSecondVideoSurface.observe(viewLifecycleOwner) {
            Log.w("PLVM", "observe: showSecondVideoSurface $it")
            binding.mainScreenTwo.showVideo = it
        }

        viewModel.speakersWithPlaceholders.observe(viewLifecycleOwner) {
            Log.w("PLVM", "observe: speakersFullList ${it.size}")
            mSpeakerAdapter?.updateData(it)
        }

        viewModel.onTheaterSpeakInsufficientBalance.observe(viewLifecycleOwner) {
            showInsufficientBalanceAlert(getString(R.string.podium_theater_insufficient_balance,it.toString()))
        }
        viewModel.onTheaterSpeakError.observe(viewLifecycleOwner) {
            showToast(it)
        }
        viewModel.isLikeLoading.observe(viewLifecycleOwner){
            Log.d("LIKE_ENABLED", "observe: $it")
        }
    }

    override fun onSecondResume() {
        viewModel.mainScreenSpeakerId.value?.let { mss ->
            binding.mainScreen.setupMainScreen(mss)
        }
        viewModel.secondMainScreenSpeakerId.value?.let { mss ->
            binding.mainScreenTwo.setupMainScreen(mss)
        }
    }

    override fun showMoreMenu(v: View): PopupMenu {
        return super.showMoreMenu(v).apply {
            menu.apply {
                findItem(R.id.action_pause_resume_mic).isVisible = false
                findItem(R.id.action_pause_resume_comments).isVisible = false
                findItem(R.id.action_close).isVisible = viewModel.iAmManager.value == true  ||viewModel.iHavePowerToEndPodium || (viewModel.isAdmin(viewModel.user.id) && !viewModel.isManagerAvailable())

                findItem(R.id.action_restricted).title = getString(R.string.podium_blocked_users_label)

//                findItem(R.id.action_edit_speaker_fees).isVisible = viewModel.iAmManager.value == true
                findItem(R.id.action_step_down).isVisible = viewModel.iAmSpeaker.value==true && viewModel.iAmManager.value!=true
            }
        }
    }

    override fun onMenuClick(menuItem: MenuItem): Boolean {
        return when (menuItem.itemId) {
//            R.id.action_edit_speaker_fees -> {
//                val action = PodiumLiveTheaterFragmentDirections.actionPodiumLiveTheaterFragmentToEditTheaterFeeFragment(podiumId = args.podiumId)
//                findNavController().navigateSafe(action)
//                false
//            }
            R.id.action_step_down -> {
                confirmAction(
                    message = R.string.podium_theater_step_down_confirm
                ) {
                    viewModel.endMySpeakingSession()
                }
                false
            }
            else -> super.onMenuClick(menuItem)
        }
    }

    private val iAmOnStage: Boolean
        get() = viewModel.secondMainScreenSpeakerId.value == viewModel.user.id

    private val iAmOnFirstMainScreen: Boolean
        get() = viewModel.mainScreenSpeakerId.value == viewModel.user.id

    private val iAmInAudienceTile: Boolean
        get() = viewModel.iAmSpeaker.value == true && !viewModel.iAmOnMainScreen()

    private fun requestToMainScreen() {
        if (viewModel.iAmElevated.value!=true) return
        if (iAmInAudienceTile) {
            confirmAction(
                message = R.string.podium_theater_audience_to_stage_message, positiveTitle = R.string.podium_theater_step_down
            ) {
                viewModel.endMySpeakingSession()
            }
        }
        if (viewModel.iAmSpeaker.value == true) return
        confirmAction(
            message = R.string.podium_theater_speak_confirmation_free,
        ) {
            viewModel.requestToSpeak(TheaterJoinType.MAIN_SCREEN)
        }
    }

    private fun requestToStage() {
        if (viewModel.iAmManager.value==true) return
        if (iAmInAudienceTile) {
            confirmAction(
                message = R.string.podium_theater_audience_to_stage_message, positiveTitle = R.string.podium_theater_step_down
            ) {
                viewModel.endMySpeakingSession()
            }
        }
        if (viewModel.iAmSpeaker.value == true) return
        else {
            val fee = (viewModel.podium.value ?: return).stageFee ?: 0
            val haveEmpowermentToSpeak = viewModel.user.userEmpowerment?.allowJoinSpeakPodiumForFree == true
            confirmAction(
                message = if (fee > 0 && viewModel.podium.value?.isAdmin != true && viewModel.podium.value?.stageFeePaid != true && !haveEmpowermentToSpeak) getString(R.string.podium_theater_speak_confirmation, fee.toString()) else getString(R.string.podium_theater_speak_confirmation_free),
            ) {
                viewModel.requestToSpeak(TheaterJoinType.STAGE)
            }
        }
    }

    private fun requestToAudience() {
        if (viewModel.iAmManager.value==true) return
        if (iAmOnStage) {
            confirmAction(
                message = R.string.podium_theater_stage_to_audience_message, positiveTitle = R.string.podium_theater_step_down
            ) {
                viewModel.endMySpeakingSession()
            }
        }
        if (viewModel.iAmSpeaker.value == true) return
        else {
            val fee = (viewModel.podium.value ?: return).audienceFee ?: 0
            val haveEmpowermentToSpeak = viewModel.user.userEmpowerment?.allowJoinSpeakPodiumForFree == true
            confirmAction(
                message = if (fee > 0 && viewModel.podium.value?.isAdmin != true && !haveEmpowermentToSpeak && viewModel.podium.value?.audienceFeePaid != true) getString(R.string.podium_theater_speak_confirmation, fee.toString()) else getString(R.string.podium_theater_speak_confirmation_free),
            ) {
                viewModel.requestToSpeak(TheaterJoinType.AUDIENCE)
            }
        }
    }

    override fun onAboutToSwitchToAnotherPodium(proceed: (Boolean) -> Unit) {
        if (viewModel.iAmSpeaker.value==true) {
            confirmAction(
                message = R.string.podium_theater_leave_confirm
            ) {
                proceed.invoke(true)
            }
        } else proceed.invoke(false)
    }

    override fun executeExtraStepsToShowAnthem(show: Boolean) {
        binding.anthemOverlayHolder.isVisible = show
    }

    override fun toLiveChatActions(userId: Int) {
        // NA
    }

    override fun toAdminList() {
        val action = PodiumLiveTheaterFragmentDirections.actionPodiumLiveTheaterFragmentToPodiumAdminsBottomSheetFragment()
        findNavController().navigateSafe(action)
    }

    override fun toBuyCamera(buy: Boolean) {
        val action = PodiumLiveTheaterFragmentDirections.actionPodiumLiveTheaterFragmentToPodiumBuyCameraBottomSheetFragment(buy)
        findNavController().navigateSafe(action)
    }

    override fun toLiveUsersList() {
        val action = PodiumLiveTheaterFragmentDirections.actionPodiumLiveTheaterFragmentToPodiumLiveUsersListBottomSheetFragment()
        findNavController().navigateSafe(action)
    }

    override fun toInvitations() {
        val action = PodiumLiveTheaterFragmentDirections.actionPodiumLiveTheaterFragmentToPodiumInviteBottomSheetFragment(podiumId = podiumIdArg)
        findNavController().navigateSafe(action)
    }

    override fun toRestrictedUsers() {
        val action = PodiumLiveTheaterFragmentDirections.actionPodiumLiveTheaterFragmentToPodiumBlockedUsersBottomSheetFragment()
        findNavController().navigateSafe(action)
    }
}