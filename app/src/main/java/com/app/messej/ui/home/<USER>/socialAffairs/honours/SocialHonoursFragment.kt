package com.app.messej.ui.home.publictab.socialAffairs.honours

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentSocialHonoursBinding
import com.app.messej.ui.home.promobar.CommonPromoBarUtil.setupPromoBoard
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class SocialHonoursFragment : Fragment() {

    private lateinit var binding: FragmentSocialHonoursBinding
    private val viewModel : SocialHonoursViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_social_honours, container,false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).apply {
            binding.customActionBar.toolbar.apply {
                setupActionBar(toolBar = this, customNavIcon = R.drawable.ic_social_back_button)
                setNavigationOnClickListener { findNavController().navigateUp() }
            }
        }
        binding.customActionBar.toolBarTitle.text = getString(R.string.social_affair_honours)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        addAsMenuHost()
        observe()
    }

    private fun setup() {
        setupPromoBoard(binding.promoBar)
        binding.composeView.setContent {
            HonoursComposeScreen(
                viewModel = viewModel,
                onUserDPClick = { userId ->
                    findNavController().navigateSafe(
                        direction = SocialHonoursFragmentDirections.actionGlobalPublicUserProfileFragment(id = userId)
                    )
                }
            )
        }
    }

    private fun observe() {

    }
}