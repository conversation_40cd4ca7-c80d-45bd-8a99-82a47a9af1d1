package com.app.messej.ui.premium

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.UserLevelUpgradeDetails
import com.app.messej.data.model.api.subscription.FlixSubscriptionDetails
import com.app.messej.data.model.api.subscription.Subscription
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.utils.DateFormatHelper
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Currency
import java.util.Locale

class AlreadySubscribedViewModel(application: Application):AndroidViewModel(application) {

    private val _price = MutableLiveData<String?>(null)
    val price: LiveData<String?> = _price

    private val _period = MutableLiveData<String?>(null)
    val period: LiveData<String?> = _period
    private val _subscriptionLoading = MutableLiveData<Boolean?>(null)
    val verifyOTPLoading: LiveData<Boolean?> = _subscriptionLoading

    private val _status = MutableLiveData<String?>(null)
    val status: LiveData<String?> = _status

    private val _nextBillDate = MutableLiveData<String?>(null)
    val nextBillDate: LiveData<String?> = _nextBillDate

    private val _expiry= MutableLiveData<String?>(null)
    val expiry: LiveData<String?> = _expiry

    private val _isActive = MutableLiveData<Boolean?>(null)
    val isActive: LiveData<Boolean?> = _isActive

    private val _isShowWelcomeMessage = MutableLiveData<Boolean?>(null)
    val isShowWelcomeMessage: LiveData<Boolean?> = _isShowWelcomeMessage

    val repository=ProfileRepository(application)
    private val accountRepo = AccountRepository(getApplication())
    private val businessRepository = BusinessRepository(application)

    val user: CurrentUser get() = accountRepo.user

    val currency =MutableLiveData<String?>(null)

    private val _userLevelUpgradeDetails = MutableLiveData<UserLevelUpgradeDetails?>()
    val userLevelUpgradeDetails: LiveData<UserLevelUpgradeDetails?> = _userLevelUpgradeDetails

     fun getSubsctriptionDetails() {
        _subscriptionLoading.postValue(true)

        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<Subscription> =
                repository.getSubscriptionDetails()) {
                is ResultOf.Success -> {
                    currency.postValue(result.value.subscriptionDetails?.get(0)?.currency)
                    _price.postValue( result.value.subscriptionDetails?.get(0)?.currency?.let { (if(it =="FLiX") (result.value.subscriptionDetails?.get(0)?.paymentAmount.toString()+" FLiX")  else (getCurrencySymbol(it)+result.value.subscriptionDetails?.get(0)?.paymentAmount.toString()))})
                    _nextBillDate.postValue(result.value.expirationDate?.let { DateFormatHelper.getConvertedDate(it) })
                    _expiry.postValue(result.value.expirationDate)
                    setStatus(result.value.status)
                }
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {
                }
            }
            _subscriptionLoading.postValue(false)
        }
    }

    private fun getCurrencySymbol(currencyCode: String): String {
        val currency = Currency.getInstance(currencyCode.uppercase())
        return currency.getSymbol(Locale.US)
    }

    fun setShowWelcomePopUp(showWelcomePopup: Boolean) {
        _isShowWelcomeMessage.postValue(showWelcomePopup)
    }

    private fun setStatus(status: String?) {
      if(status.equals("active")){
          _isActive.postValue(true)
      }else{
          _isActive.postValue(false)
      }
    }

    fun handleCountryFlagUpdate(checked: Boolean) {
        setSubscriptionToggle(checked)
    }

    val onSubscriptionToggleChanged = LiveEvent<Boolean>()

     fun setSubscriptionToggle(checked: Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<String> = repository.setSubscriptionToggle(FlixSubscriptionDetails(toggle = checked))) {
                is ResultOf.Success -> {
                    onSubscriptionToggleChanged.postValue(checked)
                    withContext(Dispatchers.Main) {
                        accountRepo.updateUser(
                            user.copy(isFlixSubscriptionRenewed = checked))
                    }
                }

                is ResultOf.APIError -> {
                }
                is ResultOf.Error -> {
                }
            }
        }
    }

    fun fetchUserLevelUpgradeDetails() {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = businessRepository.getUserLevelUpgrade()) {
                is com.app.messej.data.utils.ResultOf.Success -> {
                    _userLevelUpgradeDetails.postValue(result.value.userLevelUpgradeDetails)
                }
                else -> {
                    _userLevelUpgradeDetails.postValue(null)
                }
            }
        }
    }
}