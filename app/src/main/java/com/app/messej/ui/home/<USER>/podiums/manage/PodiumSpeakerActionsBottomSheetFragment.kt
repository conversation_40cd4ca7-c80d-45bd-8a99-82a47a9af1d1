package com.app.messej.ui.home.publictab.podiums.manage

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.ReportPackage
import com.app.messej.data.model.enums.BlockUnblockAction
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.data.model.enums.PodiumBlockFrom
import com.app.messej.data.model.enums.PodiumEnforcementDialogType
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.data.model.enums.ReportType
import com.app.messej.databinding.FragmentPodiumSpeakerActionsBottomSheetBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensurePodiumCreateAllowed
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureReportBanAllowed
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureReportUserAllowed
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.setCitizenshipWithFlixRate
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class PodiumSpeakerActionsBottomSheetFragment : BasePodiumActionsBottomSheetFragment() {

    private lateinit var binding: FragmentPodiumSpeakerActionsBottomSheetBinding

    private val args: PodiumSpeakerActionsBottomSheetFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_speaker_actions_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observer()
    }

    private fun observer() {
        viewModel.onUserMuteToggled.observe(viewLifecycleOwner) { msg: String? ->
            msg?.let {
                showToast(it)
            }
            findNavController().popBackStack()
        }
        viewModel.onUserBlockToggled.observe(viewLifecycleOwner) { msg: String? ->
            msg?.let {
                showToast(it)
            }
            findNavController().popBackStack()
        }
        userStatsViewModel.followStatus.observe(viewLifecycleOwner) {
            binding.isFollowed = it
        }
        viewModel.onSpeakingSessionEnd.observe(viewLifecycleOwner) { msg: String? ->
            msg?.let {
                showToast(it)
            }
            findNavController().popBackStack()
        }
        viewModel.onAdminActionFinished.observe(viewLifecycleOwner) {
            findNavController().popBackStack()
        }
        userStatsViewModel.userStats.observe(viewLifecycleOwner) {
            binding.userInfo.stats = it
            it?: return@observe
            setCitizenshipWithFlixRate(binding.userInfo.userFlixRate,it,viewModel.isManager(args.userId))
        }
        viewModel.onShowInMainScreen.observe(viewLifecycleOwner) {
            findNavController().popBackStack()
        }
        viewModel.onFollowedUser.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(), getString(R.string.public_star_following_text, it), Toast.LENGTH_SHORT).show()
            userStatsViewModel.setFollowed(true)
            findNavController().popBackStack()
        }

        viewModel.onUnfollowedUser.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(), getString(R.string.public_star_unfollowing_text, it), Toast.LENGTH_SHORT).show()
            userStatsViewModel.setFollowed(false)
            findNavController().popBackStack()
        }
    }

    private fun setup() {
        viewModel.podiumId.value?.let {
            userStatsViewModel.setUserAndPodium(args.userId, it)
        }
        viewModel.getSpeaker(args.userId)?.also { speaker ->
            val isSelf = speaker.id == viewModel.user.id
            binding.speaker = speaker
            binding.isSelf = isSelf

            binding.userInfo.sendGift.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    viewModel.podium.value?.let {
                        val action = PodiumSpeakerActionsBottomSheetFragmentDirections.actionGlobalGiftFragment(args.userId, giftContext =  GiftContext.GIFT_PODIUM, managerId = it.managerId, giftContextId = it.id)
                        findNavController().navigateSafe(action,navOptions = NavOptions.Builder()
                            .setPopUpTo(R.id.podiumSpeakerActionsBottomSheetFragment, inclusive = true)
                            .build())
                    }
                }
            }
            binding.actionInfo.setOnClickListener {
                val action = PodiumSpeakerActionsBottomSheetFragmentDirections.actionGlobalPublicUserProfileFragment(args.userId, false, isSelf = isSelf)
                findNavController().navigateSafe(action,navOptions = NavOptions.Builder()
                    .setPopUpTo(R.id.podiumSpeakerActionsBottomSheetFragment, inclusive = true)
                    .build())
            }

            binding.actionMute.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    if (isSelf) {
                        viewModel.muteToggle(speaker)
                    } else {
                        confirmAction(
                            title = null, message = if(!speaker.muted) R.string.podium_action_mute_confirm_message else R.string.podium_action_unmute_confirm_message
                        ) {
                            viewModel.muteToggle(speaker)
                        }
                    }
                }
            }

            binding.actionEnd.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    confirmAction(
                        title = null, message = if(isSelf) R.string.podium_action_end_self_confirm_message else R.string.podium_action_end_confirm_message
                    ) {
                        viewModel.endSpeakingSession(speaker)
                    }
                }
            }

            binding.actionPrivateMessage.setOnClickListener{
                viewModel.navigateToPrivateMessage(speaker.id)
            }

            binding.actionExtendSpeakingTime.setOnClickListener{
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    viewModel.extendSpeakingTime(args.userId)
                }
            }

            val isTheater = viewModel.podiumKind.value==PodiumKind.THEATER

            val text = if(isTheater) R.string.podium_theater_block else R.string.common_block
            binding.textBlock.setText(text)
            binding.actionBlock.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    if (isTheater) {
                        val hasFee = if (speaker.showOnStage==true) (viewModel.podium.value?.stageFee?:0)>0
                        else (viewModel.podium.value?.audienceFee?:0)>0
                        confirmAction(
                            title = R.string.podium_theater_block,
                            message = if (hasFee) R.string.podium_theater_block_confirm_refund else R.string.podium_theater_block_confirm
                        ) {
                            viewModel.toggleUserBlock(args.userId, action = BlockUnblockAction.BLOCK)
                        }
                    } else {
                        confirmAction(
                            title = null, message = R.string.podium_action_block_user_confirm_message
                        ) {
                            viewModel.toggleUserBlock(args.userId, action = BlockUnblockAction.BLOCK)
                        }
                    }
                }
            }

            binding.actionBlockTheaterStg.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    if (speaker.blockedFromStage==true) {
                        confirmAction(
                            title = R.string.podium_theater_unblock_stage,
                            message = R.string.podium_theater_unblock_stage_confirm
                        ) {
                            viewModel.toggleUserBlock(args.userId,BlockUnblockAction.UNBLOCK,PodiumBlockFrom.STAGE)
                        }
                    } else {
                        val hasFee = speaker.showOnStage==true && (viewModel.podium.value?.stageFee?:0)>0
                        confirmAction(
                            title = R.string.podium_theater_block_stage,
                            message = if (hasFee) R.string.podium_theater_block_stage_confirm_refund else R.string.podium_theater_block_stage_confirm
                        ) {
                            viewModel.toggleUserBlock(args.userId,BlockUnblockAction.BLOCK,PodiumBlockFrom.STAGE)
                        }
                    }
                }
            }

            binding.actionBlockTheaterAud.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    if (speaker.blockedFromAudience==true) {
                        confirmAction(
                            title = R.string.podium_theater_unblock_audience,
                            message = R.string.podium_theater_unblock_audience_confirm
                        ) {
                            viewModel.toggleUserBlock(args.userId, BlockUnblockAction.UNBLOCK, PodiumBlockFrom.AUDIENCE)
                        }
                    } else {
                        val hasFee = speaker.showOnStage!=true && (viewModel.podium.value?.audienceFee?:0)>0
                        confirmAction(
                            title = R.string.podium_theater_block_audience,
                            message = if (hasFee) R.string.podium_theater_block_audience_confirm_refund else R.string.podium_theater_block_audience_confirm
                        ) {
                            viewModel.toggleUserBlock(args.userId, BlockUnblockAction.BLOCK, PodiumBlockFrom.AUDIENCE)
                        }
                    }
                }
            }

            binding.actionAppointAdmin.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    appointAdmin(speaker)
                }
            }

            binding.actionMainScreen.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    viewModel.showInMainScreen(args.userId)
                }
//                confirmAction(
//                    title = null, message = getString(R.string.podium_show_main_screen_confirmation_message, speaker.name)
//                ) {
//                }
            }

            binding.actionDismissAdmin.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    dismissAdmin(speaker)
                }
            }

            binding.actionCancelAdminInvite.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    cancelAdminInvite(speaker)
                }
            }
//            binding.actionSendFlax.setOnClickListener{
//                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
//                    val action = PodiumSpeakerActionsBottomSheetFragmentDirections.actionGlobalFlaxTransfer(args.userId)
//                    findNavController().navigateSafe(action,navOptions = NavOptions.Builder()
//                        .setPopUpTo(R.id.podiumSpeakerActionsBottomSheetFragment, inclusive = true)
//                        .build())
//                }
//            }

            binding.actionWithdrawAdmin.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    withdrawAdmin(speaker)
                }
            }

            binding.actionPauseGift.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    pauseUserGift(speaker.id)
                }
            }

            binding.actionReportUser.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    ensureReportUserAllowed {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.User(speaker.asBasicUser(), reportType = ReportType.REPORT).serialize()))
                    }
                }
            }

            binding.actionBanUser.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    ensureReportBanAllowed {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.User(speaker.asBasicUser(), reportType = ReportType.BAN).serialize()))
                    }
                }
            }

        }?: run {
            findNavController().popBackStack()
        }

        binding.actionFollow.setOnClickListener {
            val speaker = viewModel.getSpeaker(args.userId)
            speaker ?: return@setOnClickListener
            viewModel.toggleFollow(speaker.id, speaker.name, userStatsViewModel.followStatus.value == true)
        }
    }
}