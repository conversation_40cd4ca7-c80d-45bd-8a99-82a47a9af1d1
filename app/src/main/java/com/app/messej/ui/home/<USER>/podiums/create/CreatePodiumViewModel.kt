package com.app.messej.ui.home.publictab.podiums.create

import android.app.Application
import android.net.Uri
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.huddles.CreatePodiumRequest.PodiumTypeEntry
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.PodiumEntry
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.data.model.enums.PodiumWhoCanJoin
import com.app.messej.data.model.enums.SpeakingJoiningFee
import com.app.messej.data.model.enums.TheaterAudienceFee
import com.app.messej.data.model.enums.TheaterStageFee
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.PodiumRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.home.publictab.common.BaseProfilePicAttachViewModel
import com.github.f4b6a3.uuid.UuidCreator
import com.hadilq.liveevent.LiveEvent
import com.kennyc.view.MultiStateView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.UUID

class CreatePodiumViewModel(application: Application) : BaseProfilePicAttachViewModel(application) {
    private val podiumRepo = PodiumRepository(getApplication())
    private val accountRepo = AccountRepository(getApplication())

    companion object {
        enum class NameError {
            NONE, LT_MIN, GT_MAX
        }

        private const val NAME_MAX_LENGTH = 20
        private const val ABOUT_MAX_LENGTH = 400
        private const val ABOUT_MAX_LENGTH_THEATER = 30
    }

    val name = MutableLiveData<String>()
    val about = MutableLiveData<String>()
    val categoryId = MutableLiveData<Int>(null)
    val podiumEntryType = MutableLiveData<PodiumTypeEntry>()
    val pId = MutableLiveData<String>()
    val tempId = MutableLiveData<String>()
    val editMode = MutableLiveData<Boolean>(false)
    val kind = MutableLiveData<PodiumKind?>(null)

    val user: CurrentUser get() = accountRepo.user

    fun setMode(podiumId: String) {
        if (podiumId != "-1") {
            editMode.postValue(true)
            pId.postValue(podiumId)
            getPodiumDetails(podiumId)
        } else {
            finalImagePath.value ?: useProfilePhoto()
            editMode.postValue(false)
        }
        Log.d("TYPE_EDIT", "" + editMode.value)
    }

    val podiumToEdit = MutableLiveData<Podium?>(null)

    val canEditPodiumType = podiumToEdit.map {
        Log.w("CPVM", "canEditPodiumType: $it", )
        it?: return@map true
        return@map it.kind!=PodiumKind.ALONE
    }

    val canEditPodiumKind = podiumToEdit.map {
        it?: return@map true
        return@map !it.isLive
    }

    val canDeletePodium = podiumToEdit.map {
        it?: return@map false
        return@map !it.isLive
    }

    private val didEnterName = MutableLiveData<Boolean>(false)

    private val ruleCharacterMin = name.map { it.isNotEmpty() }
    private val ruleCharacterMax = name.map { it.length <= NAME_MAX_LENGTH }
    private val ruleAboutCharacterMin = about.map { it.isNotEmpty() }

    val aboutMaxLength = kind.map {
        if (it == PodiumKind.THEATER) ABOUT_MAX_LENGTH_THEATER else ABOUT_MAX_LENGTH
    }
    private val ruleAboutCharacterMax = about.map { it.length <= (aboutMaxLength.value ?: ABOUT_MAX_LENGTH) }

    private val _createPodiumLoading = MutableLiveData(false)
    val createPodiumLoading: LiveData<Boolean> = _createPodiumLoading

    val onPodiumCreated = LiveEvent<Podium>()

    private val _podiumNameValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun checkHuddleName() {
            _podiumNameValid.postValue(
                name.value.orEmpty().isNotBlank() && ruleCharacterMax.value == true && ruleCharacterMin.value == true
            )
        }
        med.addSource(ruleCharacterMin) { checkHuddleName() }
        med.addSource(ruleCharacterMax) { checkHuddleName() }
        med
    }
    val podiumNameValid: LiveData<Boolean> = _podiumNameValid

    private val _podiumAboutValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun checkHuddleAbout() {
            _podiumAboutValid.postValue(
                about.value.orEmpty().isNotBlank() && ruleAboutCharacterMin.value == true && ruleAboutCharacterMax.value == true
            )
        }
        med.addSource(ruleAboutCharacterMin) { checkHuddleAbout() }
        med.addSource(ruleAboutCharacterMax) { checkHuddleAbout() }
        med
    }

    val podiumAboutValid: LiveData<Boolean> = _podiumAboutValid

    private val _nameError: MediatorLiveData<NameError> by lazy {
        val med: MediatorLiveData<NameError> = MediatorLiveData(NameError.NONE)
        fun check() {
            if (didEnterName.value == false) {
                med.postValue(NameError.NONE)
            } else if (ruleCharacterMin.value == false) {
                med.postValue(NameError.LT_MIN)
            } else if (ruleCharacterMax.value == false) {
                med.postValue(NameError.GT_MAX)
            } else med.postValue(NameError.NONE)

        }
        med.addSource(ruleCharacterMin) { check() }
        med.addSource(ruleCharacterMax) { check() }
        med.addSource(didEnterName) { check() }
        med
    }
    val nameError: LiveData<NameError> = _nameError

    val createButtonEnable: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun check() {
            var additionalFieldsValid = true

            when(kind.value) {
                PodiumKind.THEATER -> {
                    additionalFieldsValid = isAudienceFeeValid.value == true &&
                            isStageFeeValid.value == true && isWhoCanSpeakValid.value == true && isJoiningFeeValid.value == true
                }
                PodiumKind.LECTURE -> {
                    additionalFieldsValid = isWhoCanSpeakValid.value == true
                            && isWhoCanCommentValid.value == true
                            && isJoiningFeeValid.value == true
                            && isSpeakingFeeValid.value == true
                }
                PodiumKind.ASSEMBLY -> {
                    additionalFieldsValid = isWhoCanSpeakValid.value == true
                            && isWhoCanCommentValid.value == true
                            && isJoiningFeeValid.value == true
                            && isSpeakingFeeValid.value == true
                }
                PodiumKind.BIRTHDAY -> {
                    additionalFieldsValid = isSpeakingFeeValid.value == true
                }
                else -> {}
            }
            med.postValue(_createPodiumLoading.value == false &&
                                  _podiumNameValid.value == true &&
                                  _podiumAboutValid.value == true &&
                                  categoryId.value != null &&
                                  podiumEntryType.value != null &&
                                  about.value != null &&
                                  kind.value != null &&
                                  isWhoCanJoinValid.value == true &&
                                    additionalFieldsValid

            )
        }
        med.addSource(_createPodiumLoading) { check() }
        med.addSource(_podiumNameValid) { check() }
        med.addSource(_podiumAboutValid) { check() }
        med.addSource(categoryId) { check() }
        med.addSource(podiumEntryType) { check() }
        med.addSource(about) { check() }
        med.addSource(kind) { check() }
        med.addSource(isAudienceFeeValid) { check() }
        med.addSource(isStageFeeValid) { check() }
        med.addSource(isWhoCanJoinValid) { check() }
        med.addSource(isWhoCanCommentValid) { check() }
        med.addSource(isJoiningFeeValid) { check() }
        med.addSource(isSpeakingFeeValid) { check() }
        med.addSource(isWhoCanSpeakValid) { check() }
        med
    }

    override fun addCroppedImage(uri: Uri) {
        super.addCroppedImage(uri)
        _useProfilePhoto.postValue(false)
    }


    private val _useProfilePhoto = MutableLiveData<Boolean>(false)
    fun useProfilePhoto() {
        _useProfilePhoto.postValue(true)
        clearImage()
    }

    val imageToDisplay: LiveData<String?> by lazy {
        val med = MediatorLiveData<String?>(null)
        fun update() {
            if (finalImagePath.value!=null) med.postValue(finalImagePath.value)
            else if (_useProfilePhoto.value==true) med.postValue(user.thumbnail)
            else med.postValue(podiumToEdit.value?.managerProfileThumbnail)
        }
        med.addSource(_useProfilePhoto) { update() }
        med.addSource(finalImagePath) { update() }
        med.addSource(podiumToEdit) { update() }
        med.distinctUntilChanged()
    }

    private val podiumUniqueKey: UUID = UuidCreator.getTimeBased()

    val onCreatePodiumError = LiveEvent<String>()

    fun createPodium(isLive: Boolean) {
        _createPodiumLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            val compressed = finalImage?.let { podiumRepo.compressImage(it) }
            val kind = kind.value ?: return@launch

            val joiningFeeUpdated = when(kind) {
                PodiumKind.THEATER, PodiumKind.LECTURE, PodiumKind.ASSEMBLY -> if (joiningFee.value == SpeakingJoiningFee.FREE) "0" else joiningFeeString.value
                else -> "0"
            }

            when (val result = podiumEntryType.value?.let {
                podiumRepo.createPodium(
                    file = compressed, name = name.value.orEmpty(), about = about.value, category = categoryId.value, type = it, goLive = isLive, uuid = podiumUniqueKey.toString(), useProfilePhoto = _useProfilePhoto.value, kind = kind, audienceFee = audienceFeeString.value?.toIntOrNull(),
                    stageFee = stageFeeString.value?.toIntOrNull(),
                    requiredRating = if (whoCanJoinVisible.value == true) { whoCanJoin.value } else "0",
                    requiredRatingToComment = if (canCommentViewVisible.value == true) { whoCanComment.value?.canJoin } else "0",
                    requiredRatingToSpeak = if (canSpeakViewVisible.value == true) whoCanSpeak.value?.canJoin else "0",
                    joiningFee = joiningFeeUpdated,
                    speakingFee = if (speakingFeeVisible.value == true) { if (speakingFee.value == SpeakingJoiningFee.FREE) 0 else speakingFeeString.value?.toIntOrNull() } else null,
                )
            }) {
                is ResultOf.Success -> {
                    result.value.let {
                        onPodiumCreated.postValue(it)
                    }
                }

                is ResultOf.APIError -> {
                    if (result.code==403) {
                        onCreatePodiumError.postValue(result.error.message)
                    }
                }
                else -> {}
            }
            _createPodiumLoading.postValue(false)
        }
    }

    fun editPodium(goLive: Boolean) {
        _createPodiumLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            val compressed = finalImage?.let { podiumRepo.compressImage(it) }
            val kind = kind.value ?: return@launch

            val entry = podiumEntryType.value?: return@launch
            val tId = tempId.value?: return@launch
            val podId = pId.value?: return@launch

            val joiningFeeUpdated = when(kind) {
                PodiumKind.THEATER, PodiumKind.LECTURE, PodiumKind.ASSEMBLY -> if (joiningFee.value == SpeakingJoiningFee.FREE) "0" else joiningFeeString.value
                else -> "0"
            }

            when (val result = podiumRepo.updatePodium(
                file = compressed,
                name = name.value.orEmpty(),
                about = about.value,
                category = categoryId.value,
                type = entry,
                uuid = podiumUniqueKey.toString(),
                podiumId = podId,
                goLive = goLive,
                tempId = tId,
                useProfilePhoto = _useProfilePhoto.value,
                kind = kind,
                audienceFee = audienceFeeString.value?.toIntOrNull(),
                stageFee = stageFeeString.value?.toIntOrNull(),
                requiredRating = whoCanJoin.value,
                requiredRatingToComment = whoCanComment.value?.canJoin,
                requiredRatingToSpeak = whoCanSpeak.value?.canJoin,
                joiningFee = joiningFeeUpdated,
                speakingFee = if (speakingFeeVisible.value == true) { if (speakingFee.value == SpeakingJoiningFee.FREE) 0 else speakingFeeString.value?.toIntOrNull() } else 0,

            )) {
                is ResultOf.Success -> {
                    result.value.let {
                        onPodiumCreated.postValue(it)
                    }
                }
                is ResultOf.APIError -> {
                    if (result.code==403) {
                        onCreatePodiumError.postValue(result.error.message)
                    }
                }
                else -> {}
            }
            _createPodiumLoading.postValue(false)
        }
    }



    private fun getPodiumCategoriesList() {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = podiumRepo.getPodiumCategories()) {
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
                is ResultOf.Success -> {
                    categoryId.postValue(result.value.podiumCategories[0].categoryId)
                }
            }
        }
    }

    private val _podiumDetailMultiStateView = MutableLiveData<MultiStateView.ViewState>()
    val podiumDetailMultiStateView : LiveData<MultiStateView.ViewState> = _podiumDetailMultiStateView

    private fun getPodiumDetails(id: String) {
        viewModelScope.launch(Dispatchers.IO) {
            _podiumDetailMultiStateView.postValue(MultiStateView.ViewState.LOADING)

            when (val result = podiumRepo.getPodiumDetails(id)) {
                is ResultOf.APIError -> {
                    _podiumDetailMultiStateView.postValue(MultiStateView.ViewState.ERROR)
                }

                is ResultOf.Error -> {
                    _podiumDetailMultiStateView.postValue(MultiStateView.ViewState.ERROR)
                }

                is ResultOf.Success -> {
                    _podiumDetailMultiStateView.postValue(MultiStateView.ViewState.CONTENT)
                    podiumToEdit.postValue(result.value)

                    name.postValue(result.value.name)
                    about.postValue(result.value.bio.orEmpty())
                    finalImagePath.postValue(if(result.value.thumbnail=="")result.value.managerProfileThumbnail else result.value.thumbnail)
                    result.value.kind?.let {
                        kind.postValue(it)
                    }
                    categoryId.postValue(result.value.category.categoryId)
                    val entryType = when(result.value.type) {
                        Podium.PodiumType.PUBLIC -> when(result.value.entry) {
                            PodiumEntry.GENERAL -> PodiumTypeEntry.PUBLIC_GENERAL
                            PodiumEntry.MEN_ONLY -> PodiumTypeEntry.PUBLIC_MEN_ONLY
                            PodiumEntry.WOMEN_ONLY -> PodiumTypeEntry.PUBLIC_WOMEN_ONLY
                            null -> PodiumTypeEntry.PUBLIC_GENERAL
                        }
                        Podium.PodiumType.PRIVATE -> when(result.value.entry) {
                            PodiumEntry.GENERAL -> PodiumTypeEntry.PRIVATE_GENERAL
                            PodiumEntry.MEN_ONLY -> PodiumTypeEntry.PRIVATE_MEN_ONLY
                            PodiumEntry.WOMEN_ONLY -> PodiumTypeEntry.PRIVATE_WOMEN_ONLY
                            null -> PodiumTypeEntry.PRIVATE_GENERAL
                        }
                    }
                    podiumEntryType.postValue(entryType)
                    tempId.postValue(result.value.tempId)
                    audienceFeeString.postValue(result.value.audienceFee.toString())
                    stageFeeString.postValue(result.value.stageFee.toString())
                    whoCanJoin.postValue(result.value.requiredUserRating.toString())

                    when (result.value.audienceFee) {
                        0 -> {
                            setAudienceFee(TheaterAudienceFee.FREE)
                            setSpeakingFee(SpeakingJoiningFee.FREE)
                        }
                        TheaterAudienceFee.COINS_FIVE.amount.toInt() -> setAudienceFee(TheaterAudienceFee.COINS_FIVE)
                        TheaterAudienceFee.COINS_TEN.amount.toInt() -> setAudienceFee(TheaterAudienceFee.COINS_TEN)
                        else -> {
                            setAudienceFee(TheaterAudienceFee.CUSTOM)
                            setSpeakingFee(SpeakingJoiningFee.CUSTOM)
                            speakingFeeString.postValue(result.value.audienceFee.toString())
                            audienceFeeString.postValue(result.value.audienceFee.toString())
                        }
                    }
                    when (result.value.stageFee) {
                        TheaterStageFee.COINS_HUNDRED.amount.toInt() -> setStageFee(TheaterStageFee.COINS_HUNDRED)
                        TheaterStageFee.COINS_ZERO.amount.toInt() -> setStageFee(TheaterStageFee.COINS_ZERO)
                        else -> {
                            setStageFee(TheaterStageFee.CUSTOM)
                            stageFeeString.postValue(result.value.stageFee.toString())
                        }
                    }
                    when(result.value.requiredUserRating){
                        PodiumWhoCanJoin.ANY_ONE_CAN.canJoin.toInt() -> setWhoCanJoin(PodiumWhoCanJoin.ANY_ONE_CAN)
                        PodiumWhoCanJoin.RATING_ABOVE_NINETY.canJoin.toInt() -> setWhoCanJoin(PodiumWhoCanJoin.RATING_ABOVE_NINETY)
                        PodiumWhoCanJoin.RATING_ONLY_HUNDRED.canJoin.toInt() -> setWhoCanJoin(PodiumWhoCanJoin.RATING_ONLY_HUNDRED)
                    }
                    when(result.value.requiredRatingToSpeak) {
                        PodiumWhoCanJoin.ANY_ONE_CAN.canJoin.toInt() -> setWhoCanSpeak(PodiumWhoCanJoin.ANY_ONE_CAN)
                        PodiumWhoCanJoin.RATING_ABOVE_NINETY.canJoin.toInt() -> setWhoCanSpeak(PodiumWhoCanJoin.RATING_ABOVE_NINETY)
                        PodiumWhoCanJoin.RATING_ONLY_HUNDRED.canJoin.toInt() -> setWhoCanSpeak(PodiumWhoCanJoin.RATING_ONLY_HUNDRED)
                    }
                    when(result.value.requiredRatingToComment) {
                        PodiumWhoCanJoin.ANY_ONE_CAN.canJoin.toInt() -> setWhoCanComment(PodiumWhoCanJoin.ANY_ONE_CAN)
                        PodiumWhoCanJoin.RATING_ABOVE_NINETY.canJoin.toInt() -> setWhoCanComment(PodiumWhoCanJoin.RATING_ABOVE_NINETY)
                        PodiumWhoCanJoin.RATING_ONLY_HUNDRED.canJoin.toInt() -> setWhoCanComment(PodiumWhoCanJoin.RATING_ONLY_HUNDRED)
                    }
                    when(result.value.joiningFee) {
                        null, 0 -> setJoiningFee(value = SpeakingJoiningFee.FREE)
                        else -> {
                            setJoiningFee(value = SpeakingJoiningFee.CUSTOM)
                            joiningFeeString.postValue(result.value.joiningFee.toString())
                        }
                    }
                }
            }
        }
    }

    val onPodiumDelete = LiveEvent<String>()
    val onPodiumDeleteError = LiveEvent<String>()

    fun deletePodium() {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepo.deletePodium(pId.value.toString())) {
                    is ResultOf.Success -> {
                        onPodiumDelete.postValue(result.value)
                    }

                    is ResultOf.APIError -> {
                        onPodiumDeleteError.postValue(result.error.message)
                    }
                    is ResultOf.Error -> {}
                }

            } catch (e: Exception) {
                Log.d("PublicPodiumLVM", "deletePodium: ${e.message}")
            }
        }
    }

    private val _whoCanJoinType = MutableLiveData<PodiumWhoCanJoin>()
    val whoCanJoinType: LiveData<PodiumWhoCanJoin> = _whoCanJoinType

    val whoCanJoin = MutableLiveData<String>()

    //Who can Join
    val whoCanJoinVisible = kind.map {
        when(it) {
            PodiumKind.BIRTHDAY, PodiumKind.MAIDAN -> false
            else -> true
        }
    }


    fun setWhoCanJoin(type: PodiumWhoCanJoin) {
        when (type) {
            PodiumWhoCanJoin.ANY_ONE_CAN -> whoCanJoin.postValue("0")
            PodiumWhoCanJoin.RATING_ABOVE_NINETY -> whoCanJoin.postValue("90")
            PodiumWhoCanJoin.RATING_ONLY_HUNDRED ->whoCanJoin.postValue("100")
            else -> whoCanJoin.postValue("")
        }
        _whoCanJoinType.postValue(type)
    }

    val isWhoCanJoinValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            med.postValue(
                whoCanJoin.value?.isNotBlank()
            )
        }
        med.addSource(_whoCanJoinType) { update() }
        med
    }

    //Who can Comment
    val canCommentViewVisible = kind.map {
        when(it) {
            PodiumKind.LECTURE, PodiumKind.ASSEMBLY -> true
            else -> false
        }
    }

    private val _whoCanComment = MutableLiveData<PodiumWhoCanJoin?>()
    val whoCanComment : LiveData<PodiumWhoCanJoin?> = _whoCanComment

    fun setWhoCanComment(type: PodiumWhoCanJoin) {
        _whoCanComment.postValue(type)
    }

    val isWhoCanCommentValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            med.postValue(_whoCanComment.value != null)
        }
        med.addSource(_whoCanComment) { update() }
        med
    }

    //Who can Speak
    val canSpeakViewVisible = kind.map {
        when(it) {
            PodiumKind.LECTURE, PodiumKind.ASSEMBLY, PodiumKind.THEATER -> true
            else -> false
        }
    }

    private val _whoCanSpeak = MutableLiveData<PodiumWhoCanJoin?>()
    val whoCanSpeak : LiveData<PodiumWhoCanJoin?> = _whoCanSpeak

    fun setWhoCanSpeak(type: PodiumWhoCanJoin) {
        _whoCanSpeak.postValue(type)
    }

    val isWhoCanSpeakValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            med.postValue(_whoCanSpeak.value != null)
        }
        med.addSource(_whoCanSpeak) { update() }
        med
    }

    // Speaking Fee
    private val _speakingFee = MutableLiveData<SpeakingJoiningFee?>()
    val speakingFee: LiveData<SpeakingJoiningFee?> = _speakingFee
    val speakingFeeString = MutableLiveData<String>()
    val isSpeakingFeeEmpty = speakingFeeString.map { it.isNullOrEmpty() }

    val speakingFeeVisible = kind.map {
        when(it) {
            PodiumKind.LECTURE, PodiumKind.ASSEMBLY, PodiumKind.BIRTHDAY -> true
            else -> false
        }
    }

    fun setSpeakingFee(value: SpeakingJoiningFee) {
        if (_speakingFee.value == value) return
        _speakingFee.postValue(value)
        if (value == SpeakingJoiningFee.CUSTOM) speakingFeeString.postValue("")
    }

    val isSpeakingFeeValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            val priceString = speakingFeeString.value.orEmpty().trim()
            val price = priceString.toIntOrNull() ?: 0

            val valid = when (_speakingFee.value) {
                SpeakingJoiningFee.CUSTOM -> price > SpeakingJoiningFee.MINIMUM_CUSTOM_FEE
                else -> true
            }
            med.postValue(valid)
        }
        med.addSource(_speakingFee) { update() }
        med.addSource(speakingFeeString) { update() }
        med
    }

    // Joining Fee
    private val _joiningFee = MutableLiveData<SpeakingJoiningFee?>()
    val joiningFee: LiveData<SpeakingJoiningFee?> = _joiningFee
    val joiningFeeString = MutableLiveData<String>()
    val isJoiningFeeEmpty = joiningFeeString.map { it.isNullOrEmpty() }

    val joiningFeeVisible = kind.map {
        when(it) {
            PodiumKind.LECTURE, PodiumKind.ASSEMBLY, PodiumKind.THEATER -> true
            else -> false
        }
    }

    fun setJoiningFee(value: SpeakingJoiningFee) {
        if (value == _joiningFee.value) return
        _joiningFee.postValue(value)
        if (value == SpeakingJoiningFee.FREE) joiningFeeString.postValue("")
    }

    val isJoiningFeeValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            val priceString = joiningFeeString.value.orEmpty().trim()
            val price = priceString.toIntOrNull() ?: 0

            val valid = when (_joiningFee.value) {
                SpeakingJoiningFee.CUSTOM -> price > SpeakingJoiningFee.MINIMUM_CUSTOM_FEE
                else -> true
            }
            med.postValue(valid)
        }
        med.addSource(_joiningFee) { update() }
        med.addSource(joiningFeeString) { update() }
        med
    }

    private val _audienceFeeType = MutableLiveData<TheaterAudienceFee>()
    val audienceFeeType: LiveData<TheaterAudienceFee> = _audienceFeeType

    val audienceFeeString = MutableLiveData<String>()
    val isAudienceFeeEmpty = audienceFeeString.map { it.isNullOrEmpty() }

    fun setAudienceFee(fee: TheaterAudienceFee) {
        when (fee) {
            TheaterAudienceFee.FREE -> audienceFeeString.postValue("0")
            TheaterAudienceFee.CUSTOM -> audienceFeeString.postValue("")
            else -> audienceFeeString.postValue(fee.amount)
        }
        _audienceFeeType.postValue(fee)
    }

    val isAudienceFeeValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            val priceString = audienceFeeString.value.orEmpty().trim()
            val price = priceString.toIntOrNull() ?: 0

            val valid = when (_audienceFeeType.value) {
                TheaterAudienceFee.CUSTOM -> price > TheaterAudienceFee.MINIMUM_CUSTOM_FEE
                else -> true
            }
            med.postValue(valid)
        }
        med.addSource(_audienceFeeType) { update() }
        med.addSource(audienceFeeString) { update() }
        med
    }

    private val _stageFeeType = MutableLiveData<TheaterStageFee>()
    val stageFeeType: LiveData<TheaterStageFee> = _stageFeeType

    val stageFeeString = MutableLiveData<String>()
    val isStageFeeEmpty = stageFeeString.map { it.isNullOrEmpty() }

    fun setStageFee(fee: TheaterStageFee) {
        when (fee) {
            TheaterStageFee.COINS_HUNDRED -> stageFeeString.postValue("100")
            TheaterStageFee.COINS_ZERO -> stageFeeString.postValue("0")
            TheaterStageFee.CUSTOM -> stageFeeString.postValue("")
        }
        _stageFeeType.postValue(fee)
    }

    val isStageFeeValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            val priceString = stageFeeString.value.orEmpty().trim()
            val price = priceString.toIntOrNull() ?: 0
            val valid = when (_stageFeeType.value) {
                TheaterStageFee.CUSTOM -> price > TheaterStageFee.MINIMUM_CUSTOM_FEE
                else -> true
            }
            med.postValue(valid)
        }
        med.addSource(_stageFeeType) { update() }
        med.addSource(stageFeeString) { update() }
        med
    }

    val advancedSettingsUpdateButtonVisible: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun check() {
            var additionalFieldsValid = true

            when(kind.value) {
                PodiumKind.THEATER -> {
                    additionalFieldsValid = isAudienceFeeValid.value == true &&
                            isStageFeeValid.value == true && isWhoCanSpeakValid.value == true && isJoiningFeeValid.value == true
                }
                PodiumKind.LECTURE -> {
                    additionalFieldsValid = isWhoCanSpeakValid.value == true
                            && isWhoCanCommentValid.value == true
                            && isJoiningFeeValid.value == true
                            && isSpeakingFeeValid.value == true
                }
                PodiumKind.ASSEMBLY -> {
                    additionalFieldsValid = isWhoCanSpeakValid.value == true
                            && isWhoCanCommentValid.value == true
                            && isJoiningFeeValid.value == true
                            && isSpeakingFeeValid.value == true
                }
                else -> {}
            }
            med.postValue(isWhoCanJoinValid.value == true && additionalFieldsValid)
        }
        med.addSource(isAudienceFeeValid) { check() }
        med.addSource(isStageFeeValid) { check() }
        med.addSource(isWhoCanJoinValid) { check() }
        med.addSource(isWhoCanCommentValid) { check() }
        med.addSource(isJoiningFeeValid) { check() }
        med.addSource(isSpeakingFeeValid) { check() }
        med.addSource(isWhoCanSpeakValid) { check() }
        med
    }

    init {
        setWhoCanJoin(type = PodiumWhoCanJoin.ANY_ONE_CAN)
        setWhoCanComment(type = PodiumWhoCanJoin.ANY_ONE_CAN)
        setWhoCanSpeak(type = PodiumWhoCanJoin.ANY_ONE_CAN)
        setJoiningFee(value = SpeakingJoiningFee.FREE)
        setSpeakingFee(value = SpeakingJoiningFee.FREE)
        setAudienceFee(fee = TheaterAudienceFee.FREE)
        setStageFee(fee = TheaterStageFee.COINS_ZERO)
        getPodiumCategoriesList()
    }

    val editSpeakerFeesEnable: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun check() {
            med.postValue(isAudienceFeeValid.value == true &&
                                  isStageFeeValid.value == true)
        }
        med.addSource(isAudienceFeeValid) { check() }
        med.addSource(isStageFeeValid) { check() }
        med
    }
}