package com.app.messej.ui.home.publictab.podiums.manage

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.ReportPackage
import com.app.messej.data.model.enums.BlockUnblockAction
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.data.model.enums.PodiumEnforcementDialogType
import com.app.messej.data.model.enums.ReportType
import com.app.messej.databinding.FragmentPodiumLiveChatActionsBottomSheetBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensurePodiumCreateAllowed
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureReportBanAllowed
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureReportUserAllowed
import com.app.messej.ui.enforcements.EnforcementExtensions.showPodiumEnforcementDialog
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.setCitizenshipWithFlixRate
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class PodiumLiveChatActionsBottomSheetFragment : BasePodiumActionsBottomSheetFragment() {

    private lateinit var binding: FragmentPodiumLiveChatActionsBottomSheetBinding

    private val args: PodiumWaitListActionsBottomSheetFragmentArgs by navArgs()

//    override fun onCreate(savedInstanceState: Bundle?) {
//        super.onCreate(savedInstanceState)
//        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_FlashCommentsBottomSheet)
//    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_live_chat_actions_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observer()
    }

    private fun observer() {
        userStatsViewModel.followStatus.observe(viewLifecycleOwner) {
            binding.isFollowed = it
        }
        userStatsViewModel.userStats.observe(viewLifecycleOwner) {
            binding.userInfo.stats = it
            it?: return@observe
            setCitizenshipWithFlixRate(binding.userInfo.userFlixRate,it,viewModel.isManager(args.userId))
        }
        viewModel.onUserAllowedToSpeak.observe(viewLifecycleOwner) { msg: String? ->
            msg?.let {
                Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
            }
            findNavController().popBackStack()
        }
        viewModel.onUserFreezeToggled.observe(viewLifecycleOwner) { msg: String? ->
            msg?.let {
                Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
            }
            findNavController().popBackStack()
        }
        viewModel.onUserBlockToggled.observe(viewLifecycleOwner) { msg: String? ->
            msg?.let {
                Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
            }
            findNavController().popBackStack()
        }
        viewModel.onAdminActionFinished.observe(viewLifecycleOwner) {
            findNavController().popBackStack()
        }
        viewModel.onRequestToSpeakDeclined.observe(viewLifecycleOwner) {
            if (it) findNavController().popBackStack()
        }

        viewModel.onFollowedUser.observe(viewLifecycleOwner) {
           Toast.makeText(requireContext(), getString(R.string.public_star_following_text, it), Toast.LENGTH_SHORT).show()
            userStatsViewModel.setFollowed(true)
            findNavController().popBackStack()
        }

        viewModel.onUnfollowedUser.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(), getString(R.string.public_star_unfollowing_text, it), Toast.LENGTH_SHORT).show()
            userStatsViewModel.setFollowed(false)
            findNavController().popBackStack()
        }

        viewModel.onInviteToSpeakRequested.observe(viewLifecycleOwner) {
            findNavController().popBackStack()
        }

        viewModel.onInviteToSpeakError.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
        }

        viewModel.podiumKind.observe(viewLifecycleOwner) {
            Log.d("PLCABS", "observer: $it")
        }

    }

    private fun setup() {
        viewModel.getSpeaker(args.userId)
        viewModel.podiumId.value?.let {
            userStatsViewModel.setUserAndPodium(args.userId, it)
        }
        viewModel.findLiveChatUser(args.userId)?.also { liveChat ->
            binding.user = liveChat.userWithRating
            val isSelf = liveChat.userId == viewModel.user.id
            binding.isSelf = isSelf

            binding.actionInfo.setOnClickListener {
                val action = PodiumLiveChatActionsBottomSheetFragmentDirections.actionGlobalPublicUserProfileFragment(args.userId, false)
                findNavController().navigateSafe(action,navOptions = NavOptions.Builder()
                    .setPopUpTo(R.id.podiumLiveChatActionsBottomSheetFragment, inclusive = true)
                    .build())
            }

            binding.actionFreeze.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    confirmAction(
                        message = R.string.podium_action_freeze_confirm_message
                    ) {
                        viewModel.freezeToggle(liveChat.senderDetails.id,liveChat.senderDetails.name,true)
                    }
                }
            }

            binding.actionUnfreeze.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    confirmAction(
                        message = R.string.podium_action_unfreeze_user_confirm_message
                    ) {
                        viewModel.freezeToggle(liveChat.senderDetails.id,liveChat.senderDetails.name,false)
                    }
                }
            }

            binding.userInfo.sendGift.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    if (liveChat.reportPayFine == true) {
                        showPodiumEnforcementDialog(dialogType = PodiumEnforcementDialogType.SendGiftToFinedUser)
                        return@ensurePodiumCreateAllowed
                    }
                    viewModel.podium.value?.let {
                        val action = PodiumLiveChatActionsBottomSheetFragmentDirections.actionGlobalGiftFragment(args.userId, giftContext =  GiftContext.GIFT_PODIUM, managerId = it.managerId, giftContextId = it.id)
                        findNavController().navigateSafe(action,navOptions = NavOptions.Builder()
                            .setPopUpTo(R.id.podiumSpeakerActionsBottomSheetFragment, inclusive = true)
                            .build())
                    }
                }
            }

            binding.actionBlock.setOnClickListener {
                confirmAction(
                    title = null, message = R.string.podium_action_block_user_confirm_message
                ) {
                    viewModel.toggleUserBlock(args.userId, BlockUnblockAction.BLOCK)
                }
            }

            binding.actionAppointAdmin.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    if (liveChat.reportPayFine == true) {
                        showPodiumEnforcementDialog(dialogType = PodiumEnforcementDialogType.InviteFinedUserToAdmin)
                        return@ensurePodiumCreateAllowed
                    }
                    appointAdmin(liveChat.senderDetails)
                }
            }

            binding.actionDismissAdmin.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    dismissAdmin(liveChat.senderDetails)
                }
            }

            binding.actionCancelAdminInvite.setOnClickListener {
                cancelAdminInvite(liveChat.senderDetails)
            }

            binding.actionFollow.setOnClickListener {
                viewModel.toggleFollow(liveChat.senderDetails.id, liveChat.senderDetails.name, userStatsViewModel.followStatus.value == true)
            }

            binding.actionInviteToSpeak.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    if (liveChat.reportPayFine == true) {
                        showPodiumEnforcementDialog(dialogType = PodiumEnforcementDialogType.InviteFinedUser)
                        return@ensurePodiumCreateAllowed
                    }

                    if (viewModel.hasSpaceForNewSpeaker.value == true) {
                        val haveEmpowermentToSpeak = userStatsViewModel.userStats.value?.haveEmpowermentToSpeak == true
                        if (((viewModel.podium.value?.audienceFee ?: 0) != 0) && !haveEmpowermentToSpeak) {
                            showInviteToSpeakWithFeeAndFreeAlert(
                                onInviteFree = {
                                    viewModel.inviteToSpeak(liveChat.senderDetails.id, invitedForFree = true)
                                },
                                onInviteWIthFee = {
                                    viewModel.inviteToSpeak(liveChat.senderDetails.id, invitedForFree = false)
                                }
                            )
                        } else {
                            viewModel.inviteToSpeak(liveChat.senderDetails.id, invitedForFree = false)
                        }

                    } else {
                        showToast(getString(R.string.podium_max_speakers_toast,viewModel.podiumKind.value?.maxSpeakers?.toString().orEmpty()))
                    }
//                    if (viewModel.hasSpaceForNewSpeaker.value == true) {
//                        confirmAction(
//                            message = getString(R.string.podium_invite_speaker_confirm_message), positiveTitle = R.string.common_confirm, negativeTitle = R.string.common_cancel
//                        ) {
//                            viewModel.inviteToSpeak(liveChat.senderDetails.id)
//                        }
//                    } else {
//                        showToast(getString(R.string.podium_max_speakers_toast,viewModel.podiumKind.value?.maxSpeakers?.toString().orEmpty()))
//                    }
                }
            }

            binding.actionSendPrivateMessage.setOnClickListener {
                viewModel.navigateToPrivateMessage(liveChat.senderDetails.id)
            }

//            binding.actionSendFlax.setOnClickListener{
//                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
//                    if (liveChat.reportPayFine == true) {
//                        showPodiumEnforcementDialog(dialogType = PodiumEnforcementDialogType.SendFlixToFinedUser)
//                        return@ensurePodiumCreateAllowed
//                    }
//                    val action = PodiumSpeakerActionsBottomSheetFragmentDirections.actionGlobalFlaxTransfer(args.userId)
//                    findNavController().navigateSafe(action,navOptions = NavOptions.Builder()
//                        .setPopUpTo(R.id.podiumSpeakerActionsBottomSheetFragment, inclusive = true)
//                        .build()
//                    )
//                }
//            }

            binding.actionPauseGift.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    pauseUserGift(liveChat.senderDetails.id)
                }
            }

            binding.actionReportUser.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    ensureReportUserAllowed {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.User(liveChat.senderDetails.asBasicUser(), reportType = ReportType.REPORT).serialize()))
                    }
                }
            }

            binding.actionBanUser.setOnClickListener {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    ensureReportBanAllowed {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.User(liveChat.senderDetails.asBasicUser(), reportType = ReportType.BAN).serialize()))
                    }
                }
            }
        }?: run {
            findNavController().popBackStack()
        }
    }

    private fun showInviteToSpeakWithFeeAndFreeAlert(
        onInviteFree: () -> Unit,
        onInviteWIthFee: () -> Unit
    ) {
        showFlashatDialog {
            setMessage(getString(R.string.podium_invite_speaker_free_text))
            setConfirmButtonVisible(true)
            setConfirmButton(R.string.podium_theater_invite_with_fee, R.drawable.ic_coin_fee, false) {
                onInviteWIthFee()
                true
            }
            setNeutralButtonVisible(true)
            setNeutralButton(getString(R.string.podium_theater_invite_free), R.drawable.ic_coin_free, false) {
                onInviteFree()
                true
            }
        }
    }

}