package com.app.messej.ui.home.publictab.authorities

import android.content.Context
import android.content.res.Resources
import android.view.View
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.legal.AbstractCaseDetails
import com.app.messej.data.model.enums.ReportCaseStatus
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.databinding.LayoutListStateErrorBinding
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.kennyc.view.MultiStateView

object AuthoritiesUtils {
    fun setupListEmptyView(
        multiStateView : MultiStateView,
        @StringRes message: Int = R.string.legal_affairs_no_active_cases,
        @DrawableRes image: Int = R.drawable.ic_no_active_cases_empty_view
    ) {
        val emptyView: View = multiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        emptyViewBinding.prepare(
            message = message,
            image = image
        )
    }

    fun setupListErrorView(
        multiStateView : MultiStateView,
        onRetryButtonClick: () -> Unit
    ) {
        val errorView: View = multiStateView.getView(MultiStateView.ViewState.ERROR) ?: return
        val errorViewBinding = LayoutListStateErrorBinding.bind(errorView)
        errorViewBinding.prepare(
            message = R.string.default_eds_error_message,
            action = R.string.common_retry
        ) {
            onRetryButtonClick()
        }
    }

    fun AbstractCaseDetails.getStatusText(r: Resources, currentUser: Int) : String? {
        with(r) {
            return when (caseStatus) {
                ReportCaseStatus.INVESTIGATION_BUREAU -> getString(R.string.legal_affairs_investigation_bureau)
                ReportCaseStatus.APPEAL -> getString(R.string.legal_affairs_appeal)
                ReportCaseStatus.ADVOCATES_UNION-> getString(R.string.legal_affairs_advocates)
                ReportCaseStatus.JURY -> getString(R.string.legal_affairs_jury)
                ReportCaseStatus.VERDICT -> {
                    if (actionTaken?.hasDefendantFine==true && userId == currentUser) {
                        return getString(R.string.legal_affairs_fined)
                    }
                    else if (actionTaken?.hasPlaintiffFine == true && reporterId == currentUser) {
                        return getString(R.string.legal_affairs_fined)
                    }
                    else if (actionTaken?.hasDefendantCompensation == true && userId == currentUser) {
                        return getString(R.string.legal_affairs_compensation_filed)
                    }
                    return verdict?.displayText(r)
                }
                ReportCaseStatus.CLOSED -> getString(R.string.legal_affairs_closed)
                else -> null
            }
        }
    }

    fun AbstractCaseDetails.getStatusTextColor(context: Context) : Int {
        val color =  when (caseStatus) {
            ReportCaseStatus.INVESTIGATION_BUREAU -> R.color.chatMessageTickColorPurple
            ReportCaseStatus.APPEAL -> R.color.colorLegalAppeal
            ReportCaseStatus.ADVOCATES_UNION-> R.color.colorLegalAppeal
            ReportCaseStatus.JURY -> R.color.chatMessageTickColorPurple
            ReportCaseStatus.VERDICT -> if (guilty==true) R.color.colorError else R.color.colorPass
            ReportCaseStatus.CLOSED -> R.color.textColorSecondaryLight
            else -> R.color.chatMessageTickColorPurple
        }
        return ContextCompat.getColor(context, color)
    }


    fun AbstractCaseDetails.VoteResult.displayText(r: Resources): String {
        return when(this) {
            AbstractCaseDetails.VoteResult.GUILTY -> r.getString(R.string.legal_affairs_guilty)
            AbstractCaseDetails.VoteResult.NOT_GUILTY -> r.getString(R.string.legal_affairs_not_guilty)
        }
    }

    fun Fragment.showSocialWelfareFreeUserAlert() {
        showFlashatDialog {
            setMessage(message = R.string.social_free_user_permission_alert)
            setCloseButton(title = R.string.common_cancel)
            setConfirmButton(title = R.string.common_upgrade, icon = R.drawable.ic_promo_upgrade, tint = true) {
                findNavController().navigateSafe(direction = NavGraphHomeDirections.actionGlobalUpgradePremiumFragment())
                true
            }
        }
    }
}