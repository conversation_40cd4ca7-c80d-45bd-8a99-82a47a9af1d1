package com.app.messej.ui.home.publictab.postat.mypostat

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.entity.Postat
import com.app.messej.databinding.FragmentPostatMineFeedBinding
import com.app.messej.ui.home.publictab.postat.PostatFeedBaseFragment

class MyPostatFeedFragment: PostatFeedBaseFragment() {

    private lateinit var outerBinding: FragmentPostatMineFeedBinding

    override val viewModel: MyPostatViewModel by activityViewModels()

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(outerBinding.appbar.toolbar)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        outerBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_postat_mine_feed, container, false)
        outerBinding.lifecycleOwner = viewLifecycleOwner
        innerBinding = outerBinding.feedLayout
        innerBinding.viewModel = viewModel
        return outerBinding.root
    }
    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        //prevent menu of base
    }

    override fun onUserClick(item: Postat) {
        //prevent looping
    }
}
