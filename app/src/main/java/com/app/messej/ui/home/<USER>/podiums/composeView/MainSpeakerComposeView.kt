package com.app.messej.ui.home.publictab.podiums.composeView

import android.content.res.Configuration
import android.view.SurfaceView
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ripple
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.ui.composeComponents.ComposeShimmerLayout
import com.app.messej.ui.composeComponents.FlashatComposeTypography
import com.app.messej.ui.composeComponents.UserBadge
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomHorizontalSpacer
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomVerticalSpacer
import com.app.messej.ui.home.publictab.podiums.model.PodiumComposeSpeakerModel
import com.app.messej.ui.home.publictab.podiums.model.PodiumComposeSpeakerModel.Companion.testPodiumSpeakerModel
import com.app.messej.ui.utils.EnumUtils.displayText
import java.util.Locale.getDefault

interface MainSpeakerActions {
    fun onToggleMic()
    fun onToggleVideo()
    fun onSwitchCamera()
    fun onMainSpeakerClick()
    fun onDetachMainScreenPreview()
    fun setupMainScreenPreview(surfaceView: SurfaceView)
    fun setupRemoteMainScreenPreview(surfaceView: SurfaceView)
}

@Composable
fun PodiumMainSpeakerComposeView(
    modifier: Modifier = Modifier,
    mainSpeaker: State<PodiumComposeSpeakerModel?>,
    podiumKind: PodiumKind?,
    isHostTagNeededWithSpeakerName: Boolean,
    showLocalVideoSurface: State<Boolean>,
    isCurrentUser: Boolean,
    myVideoOn: State<Boolean>,
    isLoading: State<Boolean>,
    listener: MainSpeakerActions
) {
    //Added for button press effect
    val source = remember { MutableInteractionSource() }
    val indication = ripple()

    Box(
        modifier = modifier
            .border(width = 1.dp, color = colorResource(id = if (mainSpeaker.value?.currentlySpeaking == true) R.color.colorSecondary  else R.color.colorSurface))
            .clickable(interactionSource = source, indication = indication) { listener.onMainSpeakerClick() }
    ) {
        if (isLoading.value) {
            PodiumSpeakerShimmerView(
                isMainSpeaker = true
            )
            return@Box
        }
        //For Previewing Agora Video
        if (showLocalVideoSurface.value) {
            SetAgoraMainScreen(
                modifier = modifier,
                isCurrentUser = isCurrentUser,
                myVideoOn = myVideoOn,
                onDetachMainScreenPreview = listener::onDetachMainScreenPreview,
                setupMainScreenPreview = listener::setupMainScreenPreview,
                setupRemoteMainScreenPreview = listener::setupRemoteMainScreenPreview,
            )
        } else {
            //Show User Image in Background if video is off.
            SetPodiumUserImageBackgroundView(
                modifier = modifier,
                imageUrl = mainSpeaker.value?.speaker?.thumbnail
            )
        }

        //User Citizenship And Received / Sent Gift Value, Visible at the top
        PodiumSpeakerTitleAndCountryFlagView(
            modifier = Modifier.align(alignment = Alignment.TopCenter),
            item = mainSpeaker.value,
            showLocalVideo = showLocalVideoSurface,
            isHostTagNeededWithSpeakerName = isHostTagNeededWithSpeakerName,
            podiumKind = podiumKind,
            isMainSpeakerView = true
        )

        //User Image And Badge Visible at the centre
        if (!showLocalVideoSurface.value) {
            PodiumUserImageHolder(
                modifier = Modifier
                    .size(size = 80.dp)
                    .align(alignment = Alignment.Center),
                item = mainSpeaker.value
            )
        }

        //Visible at the bottom of the screen
        Column(
            modifier = Modifier
                .align(alignment = Alignment.BottomCenter)
                .fillMaxWidth()
        ) {
            //Flip Camera, Turn off/on camera, mute/ unmute buttons
            SelfActionHolder(
                showVideoToggle = true,
                item = mainSpeaker.value,
                isCurrentUser = isCurrentUser,
                showLocalVideo = showLocalVideoSurface,
                onToggleMic = listener::onToggleMic,
                onToggleVideo = listener::onToggleVideo,
                onSwitchCamera = listener::onSwitchCamera
            )
            CustomVerticalSpacer(
                space = dimensionResource(id = R.dimen.line_spacing)
            )
            //Speaker name. Visible at the bottom of the screen
            PodiumSpeakerFooterView(
                item = mainSpeaker.value,
                showControls = isCurrentUser
            )
        }
    }
}

@Composable
private fun PodiumMainSpeakerComposePreview() {
    Column (modifier = Modifier.fillMaxSize()) {
        Text(
            text = "Main Speaker Own User View",
            color = colorResource(id = R.color.textColorPrimary)
        )
        PodiumMainSpeakerComposeView(
            modifier = Modifier.size(size = 400.dp),
            mainSpeaker = remember { mutableStateOf(value = testPodiumSpeakerModel) },
            podiumKind = PodiumKind.LECTURE,
            isHostTagNeededWithSpeakerName = true,
            showLocalVideoSurface = remember { mutableStateOf(value = false) },
            isCurrentUser = true,
            myVideoOn = remember { mutableStateOf(value = false) },
            isLoading = remember { mutableStateOf(value = false) },
            listener = object :MainSpeakerActions {
                override fun onToggleMic() {
                }
                override fun onToggleVideo() {
                }
                override fun onSwitchCamera() {
                }
                override fun onMainSpeakerClick() {
                }
                override fun onDetachMainScreenPreview() {
                }
                override fun setupMainScreenPreview(surfaceView: SurfaceView) {
                }
                override fun setupRemoteMainScreenPreview(surfaceView: SurfaceView) {
                }
            }
        )
        CustomVerticalSpacer(
            space = dimensionResource(id = R.dimen.activity_margin)
        )
        Text(
            text = "Main Speaker Other's View",
            color = colorResource(id = R.color.textColorPrimary)
        )
        PodiumMainSpeakerComposeView(
            modifier = Modifier.size(size = 400.dp),
            mainSpeaker = remember { mutableStateOf(value = testPodiumSpeakerModel) },
            podiumKind = PodiumKind.BIRTHDAY,
            isHostTagNeededWithSpeakerName = true,
            showLocalVideoSurface = remember { mutableStateOf(value = false) },
            isCurrentUser = false,
            myVideoOn = remember { mutableStateOf(value = false) },
            isLoading = remember { mutableStateOf(value = false) },
            listener = object :MainSpeakerActions {
                override fun onToggleMic() {
                }
                override fun onToggleVideo() {
                }
                override fun onSwitchCamera() {
                }
                override fun onMainSpeakerClick() {
                }
                override fun onDetachMainScreenPreview() {
                }
                override fun setupMainScreenPreview(surfaceView: SurfaceView) {
                }
                override fun setupRemoteMainScreenPreview(surfaceView: SurfaceView) {
                }
            }
        )
    }
}

@Preview(showBackground = true, uiMode = Configuration.UI_MODE_NIGHT_YES)
@Composable
private fun PodiumScreenNightModePreview() {
    PodiumMainSpeakerComposePreview()
}

@Preview(showBackground = true, uiMode = Configuration.UI_MODE_NIGHT_NO)
@Composable
private fun PodiumScreenLightModePreview() {
    PodiumMainSpeakerComposePreview()
}

@Composable
fun SetPodiumUserImageBackgroundView(
    modifier: Modifier = Modifier,
    imageUrl: String?
) {
    AsyncImage(
        model = ImageRequest
            .Builder(context = LocalContext.current)
            .crossfade(enable = true)
            .data(data = imageUrl)
            .build(),
        contentDescription = null,
        placeholder = painterResource(id = R.drawable.im_user_placeholder_square_always_dark),
        error = painterResource(id = R.drawable.im_user_placeholder_square_always_dark),
        contentScale = ContentScale.Crop,
        modifier = modifier.alpha(alpha = 0.3F).fillMaxSize()
    )
}

@Composable
private fun SetAgoraMainScreen(
    modifier: Modifier = Modifier,
    isCurrentUser: Boolean,
    myVideoOn: State<Boolean>,
    onDetachMainScreenPreview: () -> Unit,
    setupMainScreenPreview: (surfaceView: SurfaceView) -> Unit,
    setupRemoteMainScreenPreview: (surfaceView: SurfaceView) -> Unit
) {
    val context = LocalContext.current

    if (isCurrentUser) {
        if (myVideoOn.value) {
            AndroidView(
                modifier = modifier,
                factory = {
                    SurfaceView(context).apply {
                        setupMainScreenPreview(this)
                    }
                }, onRelease = {
                    onDetachMainScreenPreview()
                }
            )
        }
    } else {
        AndroidView(
            modifier = modifier,
            factory = {
                SurfaceView(context).apply {
                    setupRemoteMainScreenPreview(this)
                }
            }, onRelease = {
                onDetachMainScreenPreview()
            }
        )
    }
}

@Composable
fun PodiumUserImageHolder(
    modifier: Modifier = Modifier,
    item: PodiumComposeSpeakerModel?
) {
    //Modifier.size() is mandatory while using this function.
    BoxWithConstraints (modifier = modifier, contentAlignment = Alignment.Center) {

        val context = LocalContext.current
        val maxWidth = maxWidth
        val animateSpeakingBorder = animateDpAsState(targetValue = if (item?.currentlySpeaking == true) 2.dp else 0.dp)
        val animateBackground = animateDpAsState(targetValue = if (item?.currentlySpeaking == true) maxWidth else 0.dp)

        Box(
            modifier = Modifier
                .align(alignment = Alignment.Center)
                .size(size = animateBackground.value)
                .clip(shape = CircleShape)
                .background(color = colorResource(id = R.color.colorSecondary).copy(alpha = 0.2F))
        )
        Box {
            AsyncImage(
                model = ImageRequest
                    .Builder(context = context)
                    .crossfade(enable = true)
                    .data(data = item?.speaker?.thumbnail)
                    .build(),
                placeholder = painterResource(id = R.drawable.im_user_placeholder_square),
                error = painterResource(id = R.drawable.im_user_placeholder_square),
                contentDescription = null,
                contentScale = ContentScale.Crop,
                modifier = Modifier
                    .size(size = (maxWidth / 1.2.dp).dp)
                    .clip(CircleShape)
                    .border(
                        width = animateSpeakingBorder.value,
                        shape = CircleShape,
                        color = colorResource(id = if (item?.currentlySpeaking == true) R.color.colorSecondary else R.color.transparent)
                    )
            )
            // User Badge View
            UserBadge(modifier = Modifier
                .align(alignment = Alignment.TopStart),
                userType = item?.speaker?.userBadge,
                size = maxWidth / 4
            )
        }
    }
}

@Composable
fun PodiumSpeakerTitleAndCountryFlagView(
    modifier: Modifier = Modifier,
    item: PodiumComposeSpeakerModel?,
    showLocalVideo: State<Boolean>?,
    isHostTagNeededWithSpeakerName: Boolean,
    isMainSpeakerView: Boolean,
    podiumKind: PodiumKind?
) {
    val context = LocalContext.current

    val notchText = if (item?.speaker?.inactive == true) {
        stringResource(id = R.string.user_citizenship_inactive)
    } else {
        stringResource(id = item?.speaker?.citizenship?.displayText() ?: return)
    }
    val updatedNotchText = if (isHostTagNeededWithSpeakerName) {
        stringResource(id = R.string.podium_speaker_prefix_manager, notchText)
    } else notchText

    val notchTextColor = podiumSpeakerTitleTextColor(speaker = item.speaker)

    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .setPodiumSpeakerTitleBackground(speaker = item.speaker)
                .padding(vertical = 2.dp, horizontal = dimensionResource(id = R.dimen.line_spacing))
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            //Notch text
            Text(
                text = updatedNotchText,
                modifier = Modifier.fillMaxWidth().weight(weight = 1F),
                color = colorResource(id = notchTextColor),
                style = FlashatComposeTypography.overLineTiny
            )
            //Rating / coins count
            Text(
                text = if (item.speaker.inactive) stringResource(id = R.string.common_percentage_value, item.speaker.userRatingPercent)
                else if (podiumKind?.isBirthDayPodium == true) {
                    if (item.speaker.isManager) stringResource(id = R.string.podium_my_birthday_title).uppercase(locale = getDefault())
                    else item.coinsSentToDisplay
                } else item.coinsToDisplay,
                color = colorResource(id = notchTextColor),
                style = FlashatComposeTypography.overLineTiny
            )
            CustomHorizontalSpacer(
                space = 2.dp
            )
            //Admin purple dot view
            AnimatedVisibility (visible = item.admin) {
                Box(
                    modifier = Modifier
                        .clip(shape = CircleShape)
                        .background(color = colorResource(id = R.color.colorPodiumSpeakerAdmin))
                        .size(size = 9.dp)
                )
            }
        }

        //Country Flag
        AnimatedVisibility(
            visible = !(showLocalVideo?.value ?: false),
            modifier = Modifier.align(alignment = Alignment.End)
        ) {
            item.countryFlag?.let {
                AsyncImage(
                    model = ImageRequest
                        .Builder(context = context)
                        .crossfade(enable = true)
                        .data(data = item.countryFlag)
                        .build(),
                    placeholder = painterResource(id = R.drawable.im_user_placeholder_square),
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .padding(all = dimensionResource(id = R.dimen.line_spacing))
//                        .clip(shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.line_spacing)))
                        .size(width = if (isMainSpeakerView) 20.dp else 15.dp, height = if (isMainSpeakerView) 13.dp else 10.dp)
                )
            }
        }
    }
}

@Composable
private fun SelfActionHolder(
    item: PodiumComposeSpeakerModel?,
    showVideoToggle: Boolean,
    isCurrentUser: Boolean,
    showLocalVideo:State<Boolean>,
    onSwitchCamera: () -> Unit,
    onToggleVideo: () -> Unit,
    onToggleMic: () -> Unit
) {
    if (!isCurrentUser) return
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        AnimatedVisibility (visible = showLocalVideo.value) {
            SelfActionIcon(
                icon = R.drawable.ic_flash_record_flip,
                onClick = onSwitchCamera
            )
        }
        if (showVideoToggle) {
            CustomHorizontalSpacer(
                space = dimensionResource(id = R.dimen.line_spacing)
            )
            SelfActionIcon(
                icon = if (showLocalVideo.value) R.drawable.ic_podium_video_on else R.drawable.ic_podium_video_off,
                onClick = onToggleVideo
            )
        }
        CustomHorizontalSpacer(
            space = dimensionResource(id = R.dimen.line_spacing)
        )
        SelfActionIcon(
            icon = if (item?.muted == true) R.drawable.ic_podium_mic_speaker_off else R.drawable.ic_podium_mic_speaker_on,
            onClick = onToggleMic
        )
    }
}

@Composable
private fun SelfActionIcon(
    @DrawableRes icon: Int,
    onClick: () -> Unit
) {
    IconButton(
        onClick = onClick,
        modifier = Modifier.size(size = 32.dp),
        colors = IconButtonDefaults.iconButtonColors(
            containerColor = Color(0x55000000),
            contentColor = colorResource(id = R.color.textColorOnPrimary)
        )
    ) {
        Icon(
            painter = painterResource(id = icon),
            modifier = Modifier.padding(all = 7.dp),
            contentDescription = null
        )
    }
}

@Composable
fun PodiumSpeakerFooterView(
    modifier: Modifier = Modifier,
    item: PodiumComposeSpeakerModel?,
    showControls: Boolean,
    isMainSpeakerTile: Boolean = true
) {
    Row(
        modifier = modifier
            .background(brush = Brush.verticalGradient(
                colors = listOf(colorResource(id = R.color.transparent), Color(color = 0x55000000)))
            ).padding(all = dimensionResource(id = R.dimen.line_spacing))
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = item?.speaker?.name ?: "",
            modifier = Modifier
                .fillMaxWidth()
                .weight(weight = 1F)
                .padding(horizontal = dimensionResource(id = R.dimen.line_spacing)),
            color = colorResource(id = R.color.textColorOnPrimary),
            maxLines = 1,
            style = if (isMainSpeakerTile) FlashatComposeTypography.defaultType.subtitle2 else FlashatComposeTypography.overLineTiny,
            overflow = TextOverflow.Ellipsis
        )
        if (!showControls) {
            Icon(
                modifier = Modifier.size(size = if (isMainSpeakerTile) 20.dp else 15.dp),
                painter = painterResource(id =
                    if (item?.online == true) { if (item.muted) R.drawable.ic_podium_mic_off else R.drawable.ic_podium_mic_on }
                    else R.drawable.ic_podium_offline),
                tint = Color.Unspecified,
                contentDescription = null
            )
        }
    }
}

@Composable
private fun Modifier.setPodiumSpeakerTitleBackground(speaker: PodiumSpeaker?): Modifier {
    val colors: List<Color> = if (speaker?.inactive == true) {
            List(size = 2) { colorResource(id = R.color.colorAlwaysLightSurfaceSecondary) }
        } else when(speaker?.citizenship) {
        UserCitizenship.VISITOR -> List(size = 2) { colorResource(id = R.color.colorAlwaysLightSurfaceSecondaryDarker) }
        UserCitizenship.RESIDENT -> List(size = 2) { colorResource(id = R.color.colorPodiumSpeakerResident) }
        UserCitizenship.CITIZEN -> List(size = 2) { colorResource(id = R.color.colorPodiumSpeakerCitizen) }
        UserCitizenship.OFFICER -> List(size = 2) { colorResource(id = R.color.colorPodiumSpeakerOfficer) }
        UserCitizenship.AMBASSADOR -> List(size = 2) { colorResource(id = R.color.colorPodiumSpeakerAmbassador) }
        UserCitizenship.MINISTER -> List(size = 2) { colorResource(id = R.color.colorPrimary) }
        UserCitizenship.GOLDEN -> listOf(Color(color = 0xFF6D1B95), Color(color = 0xFFFFD473))
        UserCitizenship.PRESIDENT -> listOf(Color(color = 0xFFA37A1E), Color(color = 0xFFFFEC95), Color(color = 0xFFB58F3E))
        null -> List(size = 2) { colorResource(id = R.color.colorAlwaysLightSurfaceSecondaryDarker) }
    }
    return this.background(
        brush = Brush.linearGradient(colors = colors)
    )
}

@ColorRes
private fun podiumSpeakerTitleTextColor(speaker: PodiumSpeaker?) : Int {
    return if (speaker?.inactive == true) {
        R.color.colorError
    } else when (speaker?.citizenship) {
        UserCitizenship.VISITOR -> R.color.textColorOnSecondary
        UserCitizenship.RESIDENT -> R.color.textColorOnPrimary
        UserCitizenship.CITIZEN -> R.color.colorPrimaryColorDarkest1
        UserCitizenship.OFFICER -> R.color.textColorAlwaysLightPrimary
        UserCitizenship.AMBASSADOR -> R.color.colorPrimaryColorDarkest1
        UserCitizenship.MINISTER -> R.color.textColorOnPrimary
        UserCitizenship.GOLDEN -> R.color.white
        UserCitizenship.PRESIDENT -> R.color.colorPrimary
        null -> R.color.textColorOnSecondary
    }
}

@Composable
fun PodiumSpeakerShimmerView(
    isMainSpeaker: Boolean
) {
    ComposeShimmerLayout { brush ->
        Column(modifier = Modifier
            .background(brush = brush)
            .fillMaxSize()
        ) {
            Box(
                modifier = Modifier
                    .height(height = 12.dp)
                    .background(color = colorResource(id = R.color.colorSurfaceSecondaryDarkest))
                    .fillMaxWidth()
            )
            PodiumUserImageHolder(
                modifier = Modifier
                    .size(size = if (isMainSpeaker) 80.dp else 40.dp)
                    .align(alignment = Alignment.CenterHorizontally)
                    .weight(weight = 1F),
                item = null
            )
            Box(
                modifier = Modifier
                    .height(height = 12.dp)
                    .background(color = colorResource(id = R.color.colorSurfaceSecondaryDarkest))
                    .fillMaxWidth()
            )
        }
    }
}