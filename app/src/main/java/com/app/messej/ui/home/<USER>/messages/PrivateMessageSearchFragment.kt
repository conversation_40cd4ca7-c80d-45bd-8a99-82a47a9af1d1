package com.app.messej.ui.home.privatetab.messages

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.api.huddles.PrivateMessagesSuggestionResponse
import com.app.messej.databinding.FragmentPrivateMessageSearchBinding
import com.app.messej.ui.home.privatetab.HomePrivateFragmentDirections
import com.app.messej.ui.utils.FragmentExtensions.showKeyboard
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.google.android.material.button.MaterialButton
import com.kennyc.view.MultiStateView

class PrivateMessageSearchFragment : Fragment() {

    private lateinit var binding: FragmentPrivateMessageSearchBinding

    private var mAdapter: PrivateMessageSearchListAdapter? = null

    private val viewModel: PrivateMessageSearchViewModel by viewModels()

    private var itemClicked = true

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_private_message_search, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.apply {
            binding.customActionBar.toolbar.apply {
                setupActionBar(this)
                setNavigationIcon(R.drawable.ic_close)
                if(!viewModel.user.premiumUser)
                    setNavigationIconTint(getColor(R.color.textColorAlwaysLightSecondary))
            }
        }
    }


    private fun observe() {
        viewModel.messageSearchSuggestionList.observe(viewLifecycleOwner) {
            it?: return@observe
            mAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }

        viewModel.onNavigateToPrivateMessage.observe(viewLifecycleOwner) {
            findNavController().navigateSafe(HomePrivateFragmentDirections.actionGlobalNavigationChatPrivate(it.first, it.second))
        }
    }

    private fun setup() {
        initAdapter()

        binding.customActionBar.apply {
            keyword = viewModel.searchKeyword
            showKeyboard(searchBox)
        }
    }

    override fun onResume() {
        super.onResume()
        itemClicked = false
    }

    private fun initAdapter() {
        mAdapter = PrivateMessageSearchListAdapter(layoutInflater, viewModel.user.id, object: PrivateMessageSearchListAdapter.ItemListener {
            override fun onItemClick(item: PrivateMessagesSuggestionResponse.User) {
                itemClicked = true
                viewModel.navigateToPrivateMessage(receiver = item.id)
            }

        })

        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatImageView>(R.id.eds_empty_image).apply {
                visibility = View.GONE
            }
            findViewById<AppCompatTextView>(R.id.eds_empty_message).text = resources.getString(R.string.huddle_participants_no_result_found_text)
            findViewById<MaterialButton>(R.id.eds_empty_action).apply {
                visibility = View.GONE
            }
        }

        val layoutMan = LinearLayoutManager(context)

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)
                viewModel.dataLoadingMore.postValue(loadState.append is LoadState.Loading)
            }
            registerAdapterDataObserver(object: RecyclerView.AdapterDataObserver() {
                override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
                    super.onItemRangeMoved(fromPosition, toPosition, itemCount)
                    if ((fromPosition == 0 || toPosition == 0) && layoutMan.findFirstCompletelyVisibleItemPosition()==0) {
                        layoutMan.scrollToPosition(0)
                    }
                }
            })
        }

        binding.messageList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

    }
}