package com.app.messej.ui.utils

import android.text.style.ForegroundColorSpan
import android.util.Log
import androidx.appcompat.widget.AppCompatTextView
import androidx.compose.ui.res.stringResource
import androidx.core.content.ContextCompat
import androidx.viewbinding.ViewBinding
import com.app.messej.R
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.databinding.LayoutSettingsBottomSheetHeaderFreeBinding
import com.app.messej.databinding.LayoutSettingsBottomSheetHeaderPremiumBinding
import com.app.messej.ui.utils.EnumUtils.displayText
import com.app.messej.ui.utils.TextFormatUtils.highlightOccurrences

sealed class DrawerHeaderHolder {
    abstract val binding: ViewBinding
    abstract var profile: CurrentUser.Profile?
    abstract var account: AccountDetailsResponse?

    class FreeHeader(override val binding: LayoutSettingsBottomSheetHeaderFreeBinding, private val listener: ClickListener, private val citizenship: UserCitizenship?,user: CurrentUser?): DrawerHeaderHolder() {

        interface ClickListener {
            fun toProfile()
            fun toUpgrade()
            fun dismissDialog()
            fun toFollowers()
            fun toFollowing()
            fun toFlaxBalance()
//            fun toDears()
//            fun toFans()
//            fun toLikers()
//            fun toStars()
        }

        init {
            binding.profileHolder.setOnClickListener {
                listener.toProfile()
            }
            binding.upgradeButton.setOnClickListener {
                listener.toUpgrade()
            }
            binding.closeButton.setOnClickListener {
                listener.dismissDialog()
            }
            binding.followersButton.setOnClickListener {
                listener.toFollowers()
            }
            binding.followingButton.setOnClickListener {
                listener.toFollowing()
            }
            binding.layoutFlixBalance.cardView.setOnClickListener {
                listener.toFlaxBalance()
            }
            binding.layoutSocialBalance.cardView.setOnClickListener {
                listener.toFlaxBalance()
            }

            citizenship?.setSettingsWheelCitizenshipTextAndBackground(textView = binding.textCitizenship)


            binding.followersButton.apply {
                val count = " ("+user?.followersCount+") "
                val textFollowers = context.getString(R.string.home_private_tab_followers) + count
                text = textFollowers.highlightOccurrences(count) {
                    ForegroundColorSpan(ContextCompat.getColor(context, R.color.colorAlwaysDarkSurfaceSecondary))
                }
            }
            binding.followingButton.apply {
                val count = " ("+user?.followingCount+") "
                val textFollowing = context.getString(R.string.home_private_tab_following) + count

                text = textFollowing.highlightOccurrences(count) {
                    ForegroundColorSpan(ContextCompat.getColor(context, R.color.colorAlwaysDarkSurfaceSecondary))
                }
            }

//            binding.headerDearsHolder.setOnClickListener {
//                listener.toDears()
//            }
//            binding.headerFansHolder.setOnClickListener {
//                listener.toFans()
//            }
//            binding.headerLikersHolder.setOnClickListener {
//                listener.toLikers()
//            }
//            binding.headerStarsHolder.setOnClickListener {
//                listener.toStars()
//            }
        }

        override var profile: CurrentUser.Profile?
            get() = binding.profile
            set(value) {
                binding.profile = value
            }

        override var account: AccountDetailsResponse?
            get() = binding.account
            set(value) {
                binding.account = value
            }
    }

    class PremiumHeader(override val binding: LayoutSettingsBottomSheetHeaderPremiumBinding, private val listener: ClickListener, private val citizenship: UserCitizenship?,user: CurrentUser?): DrawerHeaderHolder() {


        interface ClickListener {
            fun toMyETribe()
            fun toProfile()
            fun dismissDialog()
            fun toFollowers()
            fun toFollowing()
            fun toStatements()
        }

        init {
            binding.eTribeButton.setOnClickListener {
                listener.toMyETribe()
            }
            binding.profileHolder.setOnClickListener {
                listener.toProfile()
            }
            binding.closeButton.setOnClickListener {
                listener.dismissDialog()
            }
            binding.followersButton.setOnClickListener {
                listener.toFollowers()
            }
            binding.followingButton.setOnClickListener {
                listener.toFollowing()
            }
            binding.layoutFlixBalance.cardView.setOnClickListener {
                listener.toStatements()
            }
            binding.layoutSocialBalance.cardView.setOnClickListener {
                listener.toStatements()
            }
            citizenship?.setSettingsWheelCitizenshipTextAndBackground(textView = binding.textCitizenship)
            binding.followersButton.apply {
                val count = " ("+user?.followersCount+") "
                val textFollowers = context.getString(R.string.home_private_tab_followers) + count
                text = textFollowers.highlightOccurrences(count) {
                    ForegroundColorSpan(ContextCompat.getColor(context, R.color.colorPrimary))
                }
            }
            binding.followingButton.apply {
                val count = " ("+user?.followingCount+") "
                val textFollowing = context.getString(R.string.home_private_tab_following) + count

                text = textFollowing.highlightOccurrences(count) {
                    ForegroundColorSpan(ContextCompat.getColor(context, R.color.colorPrimary))
                }
            }
        }

        override var profile: CurrentUser.Profile?
            get() = binding.profile
            set(value) {
                binding.profile = value
                Log.d("PremiumHeader", "Profile set to: $value")
            }

        override var account: AccountDetailsResponse?
            get() = binding.account
            set(value) {
                binding.account = value
            }
    }
}

private fun UserCitizenship.setSettingsWheelCitizenshipTextAndBackground(textView: AppCompatTextView) {
    val (backgroundColor, textColor) = when(this) {
        UserCitizenship.VISITOR -> Pair(R.color.colorAlwaysLightSurfaceSecondaryDarker, R.color.colorAlwaysDarkSurfaceSecondaryDark)
        UserCitizenship.RESIDENT -> Pair(R.color.colorPodiumSpeakerResident, R.color.colorAlwaysDarkSurfaceSecondaryDark)
        UserCitizenship.CITIZEN -> Pair(R.color.colorPodiumSpeakerCitizen, R.color.colorPrimary)
        UserCitizenship.OFFICER -> Pair(R.color.colorPodiumSpeakerOfficer, R.color.colorPrimary)
        UserCitizenship.AMBASSADOR -> Pair(R.color.colorPodiumSpeakerAmbassador, R.color.colorPrimary)
        UserCitizenship.MINISTER-> Pair(R.color.colorAlwaysLightPrimaryDarkest, R.color.white)
        UserCitizenship.PRESIDENT-> Pair(R.color.colorSecondaryDark, R.color.colorPrimary)
        UserCitizenship.GOLDEN-> Pair(R.color.colorSecondaryDark, R.color.white)

    }
    textView.apply {
        text = context.getString(displayText())
        setTextColor(context.getColor(textColor))
        if (this@setSettingsWheelCitizenshipTextAndBackground == UserCitizenship.PRESIDENT) {
            background = ContextCompat.getDrawable(context, R.drawable.bg_citizenship_president_right)
        }else if(this@setSettingsWheelCitizenshipTextAndBackground == UserCitizenship.GOLDEN) {
            background = ContextCompat.getDrawable(context, R.drawable.bg_citizenship_golden_right)
        }else {
            background.setTint(ContextCompat.getColor(context, backgroundColor))
        }
    }
}
