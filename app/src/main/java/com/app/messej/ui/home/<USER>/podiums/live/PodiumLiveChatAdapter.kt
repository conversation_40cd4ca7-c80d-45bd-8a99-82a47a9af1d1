package com.app.messej.ui.home.publictab.podiums.live

import android.content.Context
import android.graphics.Typeface
import android.os.Build
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.text.style.ImageSpan
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.socket.PodiumMaidanScoreUpdatePayload
import com.app.messej.databinding.ItemPodiumLiveChatAboutBinding
import com.app.messej.databinding.ItemPodiumLiveChatBinding
import com.app.messej.databinding.ItemPodiumLiveChatMaidanBinding
import com.app.messej.databinding.ItemPodiumLiveChatPaidLikeBinding
import com.app.messej.databinding.ItemPodiumLiveChatUserJoinedBinding
import com.app.messej.ui.home.publictab.podiums.model.PodiumLiveChatUIModel
import com.app.messej.ui.utils.TextFormatUtils.highlightOccurrences
import kotlin.math.roundToInt

class PodiumLiveChatAdapter(
    private val context: Context,
    private val inflater: LayoutInflater,
    private var liveChat: MutableList<PodiumLiveChatUIModel>,
    private var mListener: LiveChatListener
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        const val ITEM_ABOUT = 39
        const val ITEM_CHAT_MESSAGE = 40
        const val ITEM_USER_JOINED = 41
        const val ITEM_PAID_LIKE = 42
        const val ITEM_MAIDAN_CONTRIBUTION = 43
    }

    interface LiveChatListener {
        fun isDisplayedOnDayNightSurface(): Boolean
        fun getCountryFlag(countryCode: String): Int?
        fun onClick(chat: PodiumLiveChatUIModel, view: View)
        fun onLongClick(chat: PodiumLiveChatUIModel, view: View): Boolean
    }


    override fun getItemViewType(position: Int): Int {
        return when (liveChat[position]) {
            is PodiumLiveChatUIModel.About -> ITEM_ABOUT
            is PodiumLiveChatUIModel.ChatMessage -> ITEM_CHAT_MESSAGE
            is PodiumLiveChatUIModel.UserJoined -> ITEM_USER_JOINED
            is PodiumLiveChatUIModel.PaidLike -> ITEM_PAID_LIKE
            is PodiumLiveChatUIModel.MaidanContribution -> ITEM_MAIDAN_CONTRIBUTION
        }
    }

    override fun getItemCount(): Int {
        return liveChat.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            ITEM_ABOUT -> AboutViewHolder(ItemPodiumLiveChatAboutBinding.inflate(inflater, parent, false))
            ITEM_CHAT_MESSAGE -> ChatMessageViewHolder(ItemPodiumLiveChatBinding.inflate(inflater, parent, false))
            ITEM_USER_JOINED -> UserJoinViewHolder(ItemPodiumLiveChatUserJoinedBinding.inflate(inflater, parent, false))
            ITEM_PAID_LIKE -> PaidLikeViewHolder(ItemPodiumLiveChatPaidLikeBinding.inflate(inflater, parent, false))
            ITEM_MAIDAN_CONTRIBUTION -> MaidanContributionViewHolder(ItemPodiumLiveChatMaidanBinding.inflate(inflater, parent, false))
            else -> throw IllegalStateException("Unknown view type")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is AboutViewHolder -> holder.bind(liveChat[position] as PodiumLiveChatUIModel.About)
            is ChatMessageViewHolder -> holder.bind(liveChat[position] as PodiumLiveChatUIModel.ChatMessage)
            is UserJoinViewHolder -> holder.bind(liveChat[position] as PodiumLiveChatUIModel.UserJoined)
            is PaidLikeViewHolder -> holder.bind(liveChat[position] as PodiumLiveChatUIModel.PaidLike)
            is MaidanContributionViewHolder -> holder.bind(liveChat[position] as PodiumLiveChatUIModel.MaidanContribution)
        }
    }

    private fun getFormattedName(user: AbstractUser): CharSequence {
//        val color = if (viewModel.isManager(id)) R.color.colorPrimaryDark
//        else if (viewModel.isAdmin(id)) R.color.colorPodiumChatAdmin
        val color = when (user.citizenship) {
            UserCitizenship.MINISTER -> R.color.colorPrimaryColorDarkest1
            UserCitizenship.AMBASSADOR -> R.color.colorPodiumSpeakerAmbassador
            UserCitizenship.CITIZEN -> R.color.colorPodiumSpeakerCitizen
            UserCitizenship.OFFICER -> R.color.colorPodiumSpeakerOfficer
            UserCitizenship.VISITOR -> R.color.textColorPrimary
            UserCitizenship.RESIDENT -> R.color.textColorSecondaryLight
            UserCitizenship.PRESIDENT -> R.color.colorPodiumSpeakerPresident
            UserCitizenship.GOLDEN -> R.color.colorPodiumSpeakerPresident
            else -> R.color.textColorSecondaryLight
        }

        val nameSpan = SpannableString(user.name).apply {
            highlightOccurrences(this.toString()) {
                StyleSpan(Typeface.BOLD)
            }
            highlightOccurrences(this.toString()) {
                ForegroundColorSpan(ContextCompat.getColor(context, color))
            }
        }
        return nameSpan
    }

    inner class AboutViewHolder(val binding: ItemPodiumLiveChatAboutBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PodiumLiveChatUIModel.About) {
            binding.apply {
                about = item.about
                hostName = item.name
            }
        }
    }

    inner class ChatMessageViewHolder(val binding: ItemPodiumLiveChatBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PodiumLiveChatUIModel.ChatMessage) {
            binding.apply {
                chatModel = item
                dayNight = mListener.isDisplayedOnDayNightSurface()
                username.apply {
                    text = getFormattedName(item.senderDetails)
                    setTextColor(ContextCompat.getColor(context,if (mListener.isDisplayedOnDayNightSurface()) R.color.textColorSecondary else R.color.textColorOnPrimaryLight))
                }
                message.text = item.message
                item.countryCode?.let { cc ->
                    mListener.getCountryFlag(cc)?.let { flagRes ->
                        flag.setImageResource(flagRes)
                    }
                }
                root.setOnClickListener {
                    mListener.onClick(item, it)
                }
                root.setOnLongClickListener {
                    mListener.onLongClick(item, it)
                }
            }
        }

        //Used for single line chats
//        protected fun AppCompatTextView.formatChatText(id: Int, user: AbstractUser, country: String?, message: String?) {
//            val nameSpan = highlightChatName(id,user)
//
//            val strBuilder = SpannableStringBuilder().append(nameSpan)
//
//            val shouldPrepend = if(Locale.getDefault().layoutDirection == LayoutDirection.RTL) {
//                nameSpan.isLeftToRight() && message.orEmpty().isLeftToRight()
//            } else {
//                !nameSpan.isLeftToRight() || !message.orEmpty().isLeftToRight()
//            }
//
//            fun SpannableStringBuilder.smartAppend(text: CharSequence): SpannableStringBuilder {
//                return if (shouldPrepend) insert(0,text) else append(text)
//            }
//
//            country?.let { cc ->
//                viewModel.getCountryFlag(cc)?.let { flagRes ->
//                    val flag = ContextCompat.getDrawable(requireContext(), flagRes) ?: return@let
//                    val lineHeight: Int = lineHeight
//                    val scaleFactor = 0.6f
//                    val aspect = flag.intrinsicWidth.toFloat() / flag.intrinsicHeight
//                    flag.setBounds(0, 0, (lineHeight * aspect * scaleFactor).roundToInt(), (lineHeight * scaleFactor).roundToInt())
//                    Log.w("PLF", "chatFlag: aspect: $aspect | lh: $lineHeight")
//                    val ccText = SpannableString(cc).apply {
//                        highlightOccurrences(cc) {
//                            ImageSpan(flag, if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) ImageSpan.ALIGN_CENTER else ImageSpan.ALIGN_BOTTOM)
//                        }
//                    }
//                    strBuilder.smartAppend(" ").smartAppend(ccText)
//                }
//            }
//            message?.let {
//                strBuilder.smartAppend(" ").smartAppend(message)
//            }
//            this.text = strBuilder
//        }
    }

    inner class UserJoinViewHolder(val binding: ItemPodiumLiveChatUserJoinedBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PodiumLiveChatUIModel.UserJoined) {
            binding.apply {
                chatModel = item
                dayNight = mListener.isDisplayedOnDayNightSurface()
                username.apply {
                    text = getFormattedName(item.senderDetails).let { name ->
                        val fullTextTemplate = context.getString(R.string.podium_live_chat_new_user, "%s")

                        val formattedText = SpannableStringBuilder().apply {
                            append(fullTextTemplate.substringBefore("%s"))
                            append(name)
                            append(fullTextTemplate.substringAfter("%s"))  /** this will add  citizenship color to welcome message **/
                        }
                        formattedText
                    }
                    setTextColor(ContextCompat.getColor(context,if (mListener.isDisplayedOnDayNightSurface()) R.color.colorLiveChatNewUser else R.color.colorLiveChatNewUserAlwaysDark))
                }
                item.userStats?.let { userStats ->
                    val baseText = context.resources.getString(R.string.podium_newuser_stats_format, userStats.rating?.toString() ?: "0", userStats.generosity)

                    message.text = SpannableString(baseText).apply {
                        highlightOccurrences(this.toString()) {
                            ForegroundColorSpan(ContextCompat.getColor(context, R.color.colorPrimaryDark))
                        }
                        val drawable = ContextCompat.getDrawable(context, R.drawable.bg_max_crown)?.apply {
                            val lineHeight: Int = message.lineHeight
                            val scaleFactor = 0.8f
                            val aspect = intrinsicWidth.toFloat() / intrinsicHeight
                            setBounds(0, 0, (lineHeight * aspect * scaleFactor).roundToInt(), (lineHeight * scaleFactor).roundToInt())
                        }
                        drawable?: return@apply
                        if (userStats.showCrownForGenerosity) {
                            highlightOccurrences(userStats.generosity) {
                                ImageSpan(drawable, if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) ImageSpan.ALIGN_CENTER else ImageSpan.ALIGN_BOTTOM)
                            }
                        }
                        if (userStats.showCrownForSkills) {
                            highlightOccurrences(userStats.skills) {
                                ImageSpan(drawable, if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) ImageSpan.ALIGN_CENTER else ImageSpan.ALIGN_BOTTOM)
                            }
                        }
                    }
                }
                item.countryCode?.let { cc ->
                    mListener.getCountryFlag(cc)?.let { flagRes ->
                        flag.setImageResource(flagRes)
                    }
                }
                root.setOnClickListener {
                    mListener.onClick(item, it)
                }
            }
        }
    }

    inner class PaidLikeViewHolder(val binding: ItemPodiumLiveChatPaidLikeBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PodiumLiveChatUIModel.PaidLike) {
            binding.apply {
                chatModel = item
                dayNight = mListener.isDisplayedOnDayNightSurface()
                username.apply {
                    text = getFormattedName(item.senderDetails)
                    setTextColor(ContextCompat.getColor(context,if (mListener.isDisplayedOnDayNightSurface()) R.color.textColorSecondary else R.color.textColorOnPrimaryLight))

                }
                item.countryCode?.let { cc ->
                    mListener.getCountryFlag(cc)?.let { flagRes ->
                        flag.setImageResource(flagRes)
                    }
                }
                root.setOnClickListener {
                    mListener.onClick(item, it)
                }
            }
        }
    }

    inner class MaidanContributionViewHolder(val binding: ItemPodiumLiveChatMaidanBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PodiumLiveChatUIModel.MaidanContribution) {
            binding.apply {
                chat = item
                dayNight = mListener.isDisplayedOnDayNightSurface()
                username.apply {
                    text = getFormattedName(item.senderDetails)
                    setTextColor(ContextCompat.getColor(context,if (mListener.isDisplayedOnDayNightSurface()) R.color.textColorSecondary else R.color.textColorOnPrimaryLight))
                }
                message.text = root.resources.getString(R.string.podium_maidan_chat_score,item.coins.toString(),item.totalCoins.toString())
                item.countryCode?.let { cc ->
                    mListener.getCountryFlag(cc)?.let { flagRes ->
                        flag.setImageResource(flagRes)
                    }
                }
                val horizontalMargin = if (item.specialGift) context.resources.getDimensionPixelSize(R.dimen.element_spacing)
                else context.resources.getDimensionPixelSize(R.dimen.activity_margin)

                (card.layoutParams as? ViewGroup.MarginLayoutParams)?.let { params ->
                    params.setMargins(horizontalMargin, params.topMargin, horizontalMargin, params.bottomMargin)
                    card.layoutParams = params
                }
                likeIcon.setImageResource(when(item.type) {
                    PodiumMaidanScoreUpdatePayload.MaidanContributionType.LIKE -> R.drawable.ic_podium_like
                    PodiumMaidanScoreUpdatePayload.MaidanContributionType.GIFT -> R.drawable.ic_gift_square
                })

                card.setOnClickListener {
                    mListener.onClick(item, it)
                }
            }
        }
    }

    fun updateData(newChatList: List<PodiumLiveChatUIModel>) {
        val diffResult = DiffUtil.calculateDiff(LiveChatDiffCallback(liveChat, newChatList))

        liveChat.clear()
        liveChat.addAll(newChatList)

        diffResult.dispatchUpdatesTo(this)
    }

    class LiveChatDiffCallback(
        private val oldList: List<PodiumLiveChatUIModel>,
        private val newList: List<PodiumLiveChatUIModel>
    ) : DiffUtil.Callback() {

        override fun getOldListSize(): Int {
            return oldList.size
        }

        override fun getNewListSize(): Int {
            return newList.size
        }

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldChat = oldList[oldItemPosition]
            val newChat = newList[newItemPosition]

            return if(oldChat is PodiumLiveChatUIModel.ChatWithData && newChat is PodiumLiveChatUIModel.ChatWithData) oldChat.chatId == newChat.chatId
            else oldChat==newChat
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition] == newList[newItemPosition]
        }
    }
}
