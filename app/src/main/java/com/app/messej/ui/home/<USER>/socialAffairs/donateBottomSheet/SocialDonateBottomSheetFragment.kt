package com.app.messej.ui.home.publictab.socialAffairs.donateBottomSheet

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.R
import com.app.messej.databinding.FragmentSocialDonateBottomSheetBinding
import com.app.messej.ui.home.publictab.socialAffairs.SocialAffairUtils.showSocialDonateConfirmationAlert
import com.app.messej.ui.home.publictab.socialAffairs.SocialAffairUtils.showSocialErrorAlert
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class SocialDonateBottomSheetFragment : BottomSheetDialogFragment(), SocialDonateBottomSheetListener {

    private lateinit var binding: FragmentSocialDonateBottomSheetBinding
    private val viewModel : SocialDonateViewModel by viewModels()
    private val args: SocialDonateBottomSheetFragmentArgs by navArgs()

    companion object {
        const val SOCIAL_DONATE_UPDATE_REQUEST = "social_donate_update_request"
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_social_donate_bottom_sheet, container,false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog =  super.onCreateDialog(savedInstanceState)
        if (dialog is BottomSheetDialog) {
            dialog.behavior.apply {
                isCancelable=false
            }
        }
        return dialog
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_CaseDetailsBottomSheet)
    }

    override fun getTheme(): Int {
        return R.style.Widget_Flashat_CaseDetailsBottomSheet
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        viewModel.parseCase(json = args.socialCase)
        binding.composeView.setContent {
            SocialDonateBottomSheetComposeScreen(
                viewModel = viewModel,
                isFromCaseInfo = args.isFromCaseInfo,
                listener = this
            )
        }
    }

    private fun observe() {
        viewModel.onSuccess.observe(viewLifecycleOwner) {
            requireActivity().supportFragmentManager.setFragmentResult(SOCIAL_DONATE_UPDATE_REQUEST, bundleOf())
            if (it) findNavController().navigateUp()
        }

        viewModel.onDonateError.observe(viewLifecycleOwner) {
            it ?: return@observe
            showSocialErrorAlert(error = it)
        }

        viewModel.onError.observe(viewLifecycleOwner) { msg->
            msg?.let { showToast(message = it) }
        }
    }

    override fun onUserDPClick(userId: Int) {

    }

    override fun onViewDetails() {
        val caseDetail = viewModel.caseDetail.value ?: return
        val caseId = caseDetail.id ?: return
        findNavController().navigateSafe(
            direction = SocialDonateBottomSheetFragmentDirections.actionGlobalSocialCaseInfoFragment(id = caseId, isUpgradeSupport = caseDetail.isUpgradeRequest)
        )
    }

    override fun onCancel() {
        findNavController().navigateUp()
    }

    override fun onSubmit() {
        showSocialDonateConfirmationAlert(
            coins = viewModel.donateTextFieldState.text,
            onConfirm = viewModel::donateCoins
        )
    }

}