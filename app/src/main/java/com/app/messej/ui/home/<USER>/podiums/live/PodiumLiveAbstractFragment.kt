package com.app.messej.ui.home.publictab.podiums.live

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.content.Intent
import android.graphics.Path
import android.graphics.RectF
import android.os.Bundle
import android.text.style.BackgroundColorSpan
import android.util.Log
import android.view.MenuItem
import android.view.SurfaceView
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.annotation.CallSuper
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.appcompat.widget.PopupMenu
import androidx.compose.ui.platform.ComposeView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.os.BundleCompat
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.paging.PagingDataPresenter
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.afollestad.assent.Permission
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.NavLivePodiumDirections
import com.app.messej.R
import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.ReportPackage
import com.app.messej.data.model.api.podium.PodiumFriend
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.AcceptDecline
import com.app.messej.data.model.enums.ChallengeType
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.data.model.enums.PodiumActionType
import com.app.messej.data.model.enums.PodiumClosedBy
import com.app.messej.data.model.enums.PodiumEnforcementDialogType
import com.app.messej.data.model.enums.PodiumEntry
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.data.model.enums.PodiumPauseGift
import com.app.messej.data.model.enums.PodiumSwipAlertType
import com.app.messej.data.model.enums.PodiumTab
import com.app.messej.data.model.enums.ReportContentType
import com.app.messej.data.model.enums.TheaterJoinType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.socket.SentGiftPayload
import com.app.messej.data.utils.BeepUtils.playTone
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.databinding.ItemPodiumLikesContainerBinding
import com.app.messej.databinding.ItemPodiumSpeakerHeaderBinding
import com.app.messej.databinding.ItemPodiumSpeakerMainBinding
import com.app.messej.databinding.LayoutPodiumAdminInviteCollapsedBinding
import com.app.messej.databinding.LayoutPodiumAdminInviteExpandedBinding
import com.app.messej.databinding.LayoutPodiumCameraDisabledBinding
import com.app.messej.databinding.LayoutPodiumInvitationInvitedAdminsDialogBinding
import com.app.messej.databinding.LayoutPodiumLiveFriendsActionBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensurePodiumCreateAllowed
import com.app.messej.ui.home.CommonChallengeViewModel
import com.app.messej.ui.home.gift.GiftListBottomSheetFragment
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.findIndexOf
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.findIndexOfFriend
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.getPagingDataPresenter
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.showUserJoinByRatingError
import com.app.messej.ui.home.publictab.podiums.PublicPodiumViewModel
import com.app.messej.ui.home.publictab.podiums.live.PodiumScrollPreviewView.PodiumPreview
import com.app.messej.ui.home.publictab.podiums.model.PodiumLiveChatUIModel
import com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel.ActiveSpeakerUIModel
import com.app.messej.ui.legal.report.ReportFragment.Companion.setReportListener
import com.app.messej.ui.utils.DataFormatHelper.DpToPx
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.confirmPaidLikeAction
import com.app.messej.ui.utils.FragmentExtensions.doubleClickPrevent
import com.app.messej.ui.utils.FragmentExtensions.downloadAndShowGift
import com.app.messej.ui.utils.FragmentExtensions.ifStillAttached
import com.app.messej.ui.utils.FragmentExtensions.setAsDestructiveMenuItem
import com.app.messej.ui.utils.FragmentExtensions.setupMenuItemTextColor
import com.app.messej.ui.utils.FragmentExtensions.showInsufficientBalanceAlert
import com.app.messej.ui.utils.FragmentExtensions.showLoader
import com.app.messej.ui.utils.FragmentExtensions.showSnackbar
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.PermissionsHelper.checkAudioPermission
import com.app.messej.ui.utils.PermissionsHelper.checkCameraPermission
import com.app.messej.ui.utils.PermissionsHelper.checkPermission
import com.app.messej.ui.utils.Synchronize
import com.app.messej.ui.utils.TextFormatUtils.highlightOccurrences
import com.google.android.material.button.MaterialButton
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.snackbar.Snackbar
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import java.time.Duration
import java.time.ZonedDateTime
import kotlin.coroutines.cancellation.CancellationException
import kotlin.random.Random


abstract class PodiumLiveAbstractFragment : Fragment() {
    internal abstract val viewModel: PodiumLiveViewModel

    private val listViewModel: PublicPodiumViewModel by activityViewModels()

    private val challengeViewModel: CommonChallengeViewModel by activityViewModels()

    private val livePodiumPagingDataPresenter: PagingDataPresenter<Podium> by lazy {
        getPagingDataPresenter(listViewModel.livePodiumsList) {
            if (enableScrollForTab == PodiumTab.LIVE_PODIUM) {
                refreshPodiumScroller()
            }
        }
    }
    private val liveFriendsPagingDataPresenter: PagingDataPresenter<PodiumFriend> by lazy {
        getPagingDataPresenter(listViewModel.liveFriendsList) {
            if (enableScrollForTab == PodiumTab.LIVE_FRIENDS) {
                refreshPodiumScroller()
            }
        }
    }

    protected abstract val binding: ViewBinding

    interface LiveBindingElements {
        val liveChat: RecyclerView?
        val likesContainer: ItemPodiumLikesContainerBinding?

        fun onPodiumLoading(loading: Boolean)
        fun showLocalVideoSurface(show: Boolean)

        val actionDecorHolderTop: LinearLayoutCompat?

        val chatSend: MaterialButton?
        val actionPaidLike: MaterialButton?
        val actionShare: MaterialButton?
        val liveCounter: ViewGroup?
        val scrollerLayout: SmartRefreshLayout

        val anthemOverlay: ComposeView?
    }

    protected abstract val podiumIdArg: String
    protected open val enableScrollForTab: PodiumTab? = null

    protected abstract val liveBindingElements: LiveBindingElements

    protected var mLiveChatAdapter: PodiumLiveChatAdapter? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.d("PLULDS", "set Podium ID $podiumIdArg")
        viewModel.setPodiumId(podiumIdArg)
        initAdapter()
        setup()
        addObservers()
    }

    override fun onStart() {
        super.onStart()
        activity?.window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    private var isFirstResume = true

    override fun onResume() {
        super.onResume()
        viewModel.resetKicks()
        viewModel.checkIfStillLive()
        if (isFirstResume) {
            isFirstResume = false
        } else {
            onSecondResume()
        }
    }

    abstract fun onSecondResume()

    override fun onStop() {
        super.onStop()
        activity?.window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        viewModel.currentActiveSpeaker = null
        hideLoader()
        popup?.apply {
            dismiss()
            popup = null
        }
    }

    private fun ConstraintLayout.getNewVideoSurface(): SurfaceView {
        val view = layoutInflater.inflate(R.layout.item_podium_main_screen_video_surface,this,false)
        val params = ConstraintLayout.LayoutParams(
            ConstraintLayout.LayoutParams.MATCH_PARENT,
            ConstraintLayout.LayoutParams.MATCH_PARENT
        )
        view.layoutParams = params
        this.apply {
            removeAllViews()
            addView(view)
        }
        return view.findViewById(R.id.main_screen_surface)!!
    }

    @CallSuper
    protected fun ItemPodiumSpeakerMainBinding.setupMainScreen(mss: Int, retry: Boolean = true) {
        videoHolder.setupMainScreen(mss, retry)

        micButton.setOnClickListener {
            viewModel.muteToggleSelf()
        }

        cameraToggleButton.setOnClickListener {
            toggleCamera()
        }

        cameraFlipButton.setOnClickListener {
            toggleCameraFacing()
        }

        onMainScreenSetup()
    }

    protected open fun ItemPodiumSpeakerMainBinding.onMainScreenSetup() {

    }

    protected fun ConstraintLayout.setupMainScreen(mss: Int, retry: Boolean = true) {
        Log.w("AGREGS", "setupMainScreen $mss, $retry")
        if (mss == viewModel.user.id) {
            Log.w("AGREGS", "myVideoIsTurnedOn ${viewModel.myVideoIsTurnedOn.value}")
            if (viewModel.myVideoIsTurnedOn.value==true) {
                viewModel.setupMainScreenPreview(getNewVideoSurface())
            } else {
                removeAllViews()
                viewModel.detachMainScreenPreview()
            }
        } else {
            viewModel.setupRemoteMainScreenPreview(getNewVideoSurface(),mss)

        }
        if (retry) {
            lifecycleScope.launch {
                delay(1000)
                setupMainScreen(mss, false)
            }
        }
    }

    protected fun setComposeMainScreen() {

    }

    private fun setUpExitWatcher() {
        Log.w("NAVC", "onBackPressedDispatcher.addCallback: PodiumLiveFragment")
        (activity as AppCompatActivity).onBackPressedDispatcher.addCallback(viewLifecycleOwner, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                Log.d("NAVC", "handleOnBackPressed: PodiumLiveFragment")
                onExit()
            }
        })
    }

    protected open fun onExit() {
        if (viewModel.flashatAnthem.value?.playing==true) {
            showToast(R.string.podium_anthem_exit_blocked_message)
            return
        }
//        if (viewModel.iAmManager.value == true && !viewModel.hasOtherElevatedSpeakers) {
        if (viewModel.iAmManager.value == true) {
            confirmClose {
                viewModel.close()
            }
        } else {
            val wasInvited = viewModel.userHasInvitation()

            if(wasInvited){
              confirmExitForAdminsAndInvitees(
                  onExitOnly ={
                      viewModel.leave()
                  },
                  onCancelInviteExit = {
                      viewModel.exit()
                  }
              )
            }else {
                confirmExit {
                    viewModel.leave()
                }
            }
        }
    }

    private fun setup() {
        lifecycleScope.launch {
            delay(500)
            ifStillAttached {
                setUpExitWatcher()
            }
        }
        ActivePodiumTracker.registerActiveScreen(ActivePodiumTracker.ActivePodiumScreen.PodiumRoom(podiumIdArg), viewLifecycleOwner)

        setFragmentResultListener(GiftListBottomSheetFragment.GIFT_REQUEST_KEY) { _, bundle ->
            val payload = BundleCompat.getParcelable(bundle, GiftListBottomSheetFragment.GIFT_REQUEST_PAYLOAD, SentGiftPayload::class.java)
            payload?: return@setFragmentResultListener
            viewModel.sendPodiumGift(payload)
        }

        liveBindingElements.chatSend?.setOnClickListener {
            ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                when (viewModel.podium.value?.kind) {
                    PodiumKind.LECTURE, PodiumKind.ASSEMBLY -> {
                        if (!checkUserCanCommentByRating()) return@ensurePodiumCreateAllowed
                    }
                    else -> {}
                }
                viewModel.sendChatMessage()
            }
        }

        liveBindingElements.actionPaidLike?.setOnClickListener {
            if(doubleClickPrevent()) {
                ensureAnthemNotPlaying {
                    ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                        if (viewModel.podium.value?.likesDisabled == false) {
                            confirmPaidLikeAction {
                                viewModel.sendPaidLike()
                            }
                        }
                    }
                }
            }
        }

        liveBindingElements.actionShare?.setOnClickListener {
            sharePodium()
        }
//        liveBindingElements.actionVideoToggle?.setOnClickListener {
//            Log.d("CAM_ACTION", "main Screen  " + viewModel.iAmOnMainScreen())
//            toggleCamera()
//        }

        liveBindingElements.liveCounter?.setOnClickListener {
            ensureAnthemNotPlaying {
                if (!viewModel.user.premium) return@ensureAnthemNotPlaying
                else if (viewModel.podiumHideLiveUsers.value == true) {
                    // Exit early if the user is not premium or live users are hidden
                    if (viewModel.iAmPresident) {
                        toLiveUsersList()
                    } else {
                        return@ensureAnthemNotPlaying
                    }
                } else {
                    toLiveUsersList()
                }
            }
        }
        setupPodiumScroller()

        setReportListener { type, success ->
            if (type!= ReportContentType.PODIUM || !success) return@setReportListener
            Toast.makeText(requireContext(), getString(R.string.postat_report_successfull), Toast.LENGTH_SHORT).show()
        }
    }
    private fun confirmExitForAdminsAndInvitees(onExitOnly: ()->Unit, onCancelInviteExit: ()->Unit) {
        if (!canExit()) return
        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutPodiumInvitationInvitedAdminsDialogBinding>(layoutInflater, R.layout.layout_podium_invitation_invited_admins_dialog, null, false)
            view.textHeader = getString(R.string.podium_action_invited_exit_confirm_message)
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(true)

            view.actionExitOnly.setOnClickListener {
                onExitOnly.invoke()
                dismiss()
            }
            view.actionCancelInviteExit.setOnClickListener {
                onCancelInviteExit.invoke()
                dismiss()
            }

            view.actionCancel.setOnClickListener {
                dismiss()
            }
        }

    }

    protected fun checkCameraAccess(speaker: PodiumSpeaker, onAllowed: () -> Unit) {
        Log.d("CAM_ACTION", "violation ${speaker.violation}")
        Log.d("CAM_ACTION", "allowVideo ${speaker.allowVideoInPodiums}")
        if (!speaker.violation) {
            if (speaker.allowVideoInPodiums == true) {
                onAllowed.invoke()
            } else {
                Log.d("CAM_ACTION", "citizenship ${speaker.citizenship}")
                when (speaker.citizenship) {
                    UserCitizenship.AMBASSADOR, UserCitizenship.OFFICER, UserCitizenship.MINISTER -> {
                        cameraDisabledAlert(header = resources.getString(R.string.podium_camera_disabled), false)
                    }

                    UserCitizenship.CITIZEN -> {
                        cameraDisabledAlert(header = getString(R.string.podium_buy_camera_citizen_alert), true)
                    }

                    UserCitizenship.RESIDENT -> {
                        cameraDisabledAlert(header = getString(R.string.podium_buy_camera_resident_alert), true)
                    }

                    UserCitizenship.VISITOR -> {
                        if (viewModel.podiumKind.value == PodiumKind.INTERVIEW) {
                            cameraDisabledAlert(header = getString(R.string.podium_interview_buy_camera_visitor_alert), true)
                        } else {
                            cameraDisabledAlert(header = getString(R.string.podium_buy_camera_visitor_alert), false)
                        }
                    }

                    else -> {}
                }
            }
        } else {
            cameraDisabledAlert(header = resources.getString(R.string.podium_camera_disabled), false)
        }
    }

    protected abstract fun toLiveUsersList()

    protected fun toggleCamera() {
        if (!viewModel.iAmOnMainScreen()) {
            cameraDisabledAlert(header = resources.getString(R.string.podium_main_screen_camera_alert), false)
            return
        }
        val me = viewModel.meAsSpeaker.value?.speaker?: return
        checkCameraAccess(me) {
            if (viewModel.myVideoIsTurnedOn.value != true) {
                confirmAction(null, R.string.podium_main_screen_video_confirm, R.string.common_proceed, R.string.common_cancel) {
                    checkCameraPermission(binding.root) {
                        viewModel.enableVideo(true)
                    }
                }
            } else viewModel.enableVideo(false)
        }
    }

    protected fun checkGiftEligibility(receiver: PodiumSpeaker, onEligible:() -> Unit) {
        when(viewModel.canSendGifts()) {
            PodiumLiveViewModel.PodiumGiftSend.ELIGIBLE -> {
                when (viewModel.canReceiveGifts(receiver)) {
                    PodiumLiveViewModel.PodiumGiftSend.ELIGIBLE -> {
                        onEligible()
                    }

                    else -> showToast(R.string.podium_challenge_send_gift_inactive)
                }
            }
            PodiumLiveViewModel.PodiumGiftSend.UPGRADE -> {
                val builder = MaterialAlertDialogBuilder(requireContext()).apply {
                    setMessage(R.string.podium_challenge_send_gift_upgrade_prompt)
                    setPositiveButton(getString(R.string.common_upgrade)) { dialog, _ ->
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalUpgradePremiumFragment())
                        dialog.dismiss()
                    }
                    setNegativeButton(R.string.common_cancel) { dialog, _ ->
                        dialog.dismiss()
                    }
                }
                builder.show()
            }
            else -> {
                //TODO show some toast
            }
        }
    }

    protected fun cameraDisabledAlert(header: String, canBuy: Boolean, body: String? = null) {
        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutPodiumCameraDisabledBinding>(layoutInflater, R.layout.layout_podium_camera_disabled, null, false)
            view.textHeader = header
            view.text = body
            view.canUpgrade = canBuy
            view.canBuy = if (header == resources.getString(R.string.podium_interview_buy_camera_visitor_alert)) {
                false
            } else {
                canBuy
            }

            if (header == resources.getString(R.string.podium_interview_buy_camera_visitor_alert)) view.actionClose.text = getString(R.string.common_later)
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(true)
            view.actionClose.setOnClickListener {
                dismiss()
            }
//            view.actionLearn.setOnClickListener {
//                toBuyCamera(false)
//                dismiss()
//            }

            view.actionUpgrade.setOnClickListener {
                dismiss()
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalUpgradePremiumFragment())
            }

            view.actionBuy.setOnClickListener {
                toBuyCamera(true)
                dismiss()
            }
        }
    }

    protected abstract fun toBuyCamera(buy: Boolean)

    private fun deleteChatMessage(podiumId: String, chatId: String) {
//        MaterialDialog(requireContext()).show {
//            val view = DataBindingUtil.inflate<LayoutPodiumLiveFriendsActionBinding>(layoutInflater, R.layout.layout_podium_live_friends_action, null, false)
//            view.textHeader = getString(R.string.postat_delete_comment)
//            view.actionClose.text=getString(R.string.common_cancel)
//            view.actionFriendUnfollow.text =getString(R.string.common_delete)
//            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
//            cancelable(true)
//            view.actionClose.setOnClickListener {
//                dismiss()
//            }
//            view.actionFriendUnfollow.setOnClickListener {
//                viewModel.deleteChatMessage(podiumId = podiumId, chatId = chatId)
//                dismiss()
//            }
//        }
        showFlashatDialog {
            setMessage(message = R.string.postat_delete_comment)
            setCloseButton(title = R.string.common_cancel)
            setConfirmButton(title = R.string.common_delete, icon = R.drawable.ic_trash_outline, tint = true) {
                viewModel.deleteChatMessage(podiumId = podiumId, chatId = chatId)
                true
            }
        }
    }

    protected fun sharePodium() {
        viewModel.podium.value?.let {
            if (it.type == Podium.PodiumType.PRIVATE) {
                val message = getString(R.string.podium_share_text, viewModel.user.name, it.shareLink ?: "")
//                    findNavController().navigateSafe(PodiumLiveFragmentDirections.actionGlobalInternalShareFragment(message,ForwardSource.PODIUMS))
                sharePodium(message)
            }else{
                val message = getString(R.string.podium_share_text_public, it.name, it.shareLink ?: "")
                sharePodium(message)
            }
        }
    }

    private fun sharePodium(message: String?) {
        val sendIntent: Intent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(
                Intent.EXTRA_TEXT, message
            )
            type = "text/plain"
        }
        val shareIntent = Intent.createChooser(sendIntent, null)
        startActivity(shareIntent)
    }

    protected open val isDisplayedOnDayNightSurface: Boolean = true

    @CallSuper
    private fun initAdapter() {
        val liveChat = liveBindingElements.liveChat?: return

        mLiveChatAdapter = PodiumLiveChatAdapter(requireContext(),layoutInflater,mutableListOf(), object: PodiumLiveChatAdapter.LiveChatListener {
            override fun isDisplayedOnDayNightSurface() = isDisplayedOnDayNightSurface
            override fun getCountryFlag(countryCode: String) = viewModel.getCountryFlag(countryCode)
            override fun onClick(chat: PodiumLiveChatUIModel, view: View) {
                ensureAnthemNotPlaying {
                    if (chat is PodiumLiveChatUIModel.ChatWithData) {
                        toLiveChatActions(chat.userId)
                    }
                }
            }

            override fun onLongClick(chat: PodiumLiveChatUIModel, view: View): Boolean {
                if (chat !is PodiumLiveChatUIModel.ChatMessage) return false
                if (viewModel.iAmElevated.value == true|| viewModel.user.userEmpowerment?.allowDeleteCommentsFromPodium == true) {
                    if (!(viewModel.isManager(chat.userId) && viewModel.podium.value?.isAdmin == true)) {
                        deleteChatMessage(chat.podiumId, chat.chatId)
                        return true
                    }
                }
                return false
            }
        })
        liveChat.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, true)
            setHasFixedSize(true)
            adapter = mLiveChatAdapter
        }
        mLiveChatAdapter?.apply {
            registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
                override fun onItemRangeInserted(positionStart: Int, itemCount: Int) {
                    super.onItemRangeInserted(positionStart, itemCount)
                    if ((liveChat.layoutManager as LinearLayoutManager).findFirstVisibleItemPosition() < 2) {
                        liveChat.scrollToPosition(0)
                    }
                }
            })
        }
    }

    protected abstract fun toLiveChatActions(userId: Int)

    protected open fun toAdminList() {}

    protected open fun toRestrictedUsers() {}

    protected open fun toInvitations() {}


    private var didPopBackstack: Boolean by Synchronize(false)

    private fun popSafely() {
        Log.w("PLF", "popSafely: didPopBackstack: $didPopBackstack")
        if (didPopBackstack) return
        didPopBackstack = true
//        Log.w("PLF", "popSafely: popping from ${findNavController().currentBackStack.value.joinToString(" -> ") { it.destination.displayName.split("/").last() }}...")
        findNavController().popBackStack(R.id.nav_live_podium, true)
    }

    private var frozenToast: Snackbar? = null

    private fun addObservers() {
        viewModel.speakers.observe(viewLifecycleOwner) {
            if (!viewModel.iAmOnMainScreen() && viewModel.myVideoIsTurnedOn.value == true) {
                viewModel.enableVideo(false)
            }
        }
        viewModel.iAmManager.observe(viewLifecycleOwner) {
            it?: return@observe
        }
        viewModel.iAmElevated.observe(viewLifecycleOwner) {
            it ?: return@observe
        }
        viewModel.podiumDetailsLoading.observe(viewLifecycleOwner) {
            liveBindingElements.onPodiumLoading(it)
        }
//        viewModel.remoteVideoState.observe(viewLifecycleOwner) {
//            it?: return@observe
//            if (it==AgoraEngineService.VideoState.PLAYING) {
//                viewModel.setupRemoteMainScreenPreview(binding.mainScreen.mainScreenSurface)
//            }
//        }
        viewModel.showLocalVideoSurface.observe(viewLifecycleOwner) {
            Log.w("PLVM", "observe: showVideoSurface $it")
            liveBindingElements.showLocalVideoSurface(it)
        }
        viewModel.liveChat.observe(viewLifecycleOwner) {
            mLiveChatAdapter?.updateData(it)
        }
        viewModel.isChatFrozen.observe(viewLifecycleOwner) {
            frozenToast = if (it.first == true) {
                if (frozenToast?.isShown == true) {
                    frozenToast
                    return@observe
                }
                when (it.second) {
                    null -> showSnackbar(getString(R.string.podium_frozen_by_common_message), Snackbar.LENGTH_INDEFINITE)
                    true -> showSnackbar(getString(R.string.podium_frozen_by_manager_message), Snackbar.LENGTH_INDEFINITE)
                    else -> showSnackbar(getString(R.string.podium_frozen_by_admin_message), Snackbar.LENGTH_INDEFINITE)
                }
            } else {
                frozenToast?.dismiss()
                null
            }
        }
        viewModel.onChatDisabled.observe(viewLifecycleOwner) {
            if (it.first == false) {
                showToast(R.string.podium_comments_resumed)
            }
        }

        viewModel.onReadyToJoin.observe(viewLifecycleOwner) {
            checkPermission(Permission.READ_PHONE_STATE, binding.root, R.string.podium_permission_rationale) {
                Log.w("AGORA", "setup: viewModel.joinChannel")
                viewModel.joinChannel()
                onAboutToJoin()
                Log.d("PDPresenter", "onReadyToJoin")
                refreshPodiumScroller()
            }
        }

        viewModel.onPodiumLoadError.observe(viewLifecycleOwner) {
            popSafely()
        }
        viewModel.onLeftPodium.observe(viewLifecycleOwner) {
            popSafely()
        }
        viewModel.onExitPodium.observe(viewLifecycleOwner) {
            popSafely()
        }

        viewModel.onKickedFromPodium.observe(viewLifecycleOwner) {
            when (it) {
                PodiumLiveViewModel.PodiumKickReason.GoLiveFailed -> showToast(R.string.podium_error_go_live)
              is PodiumLiveViewModel.PodiumKickReason.JoinFailed -> showToast(it.message?:getString(R.string.podium_error_join))
                PodiumLiveViewModel.PodiumKickReason.NotLive -> showToast(R.string.podium_error_not_live)
                PodiumLiveViewModel.PodiumKickReason.PodiumBlocked -> showToast(R.string.podium_error_blocked)
                is PodiumLiveViewModel.PodiumKickReason.LiveInAnotherPodium -> {
                    val builder = MaterialAlertDialogBuilder(requireContext()).apply {
                        setCancelable(false)
                        setMessage(it.message)
                        if (it.canLeave) {
                            setPositiveButton(resources.getString(R.string.common_proceed)) { dialog, which ->
                                dialog.dismiss()
                                viewModel.leavePodium(it.otherPodiumId) { left ->
                                    if (!left) return@leavePodium
                                    val action = NavGraphHomeDirections.actionGlobalNavLivePodium(podiumIdArg)
                                    val options = NavOptions.Builder().setPopUpTo(R.id.nav_live_podium, inclusive = true).build()
                                    findNavController().navigateSafe(action, options)
                                }
                            }
                            setNegativeButton(resources.getString(R.string.common_cancel)) { dialog, which ->
                                dialog.dismiss()
                                val action = NavGraphHomeDirections.actionGlobalNavLivePodium(it.otherPodiumId)
                                val options = NavOptions.Builder().setPopUpTo(R.id.nav_live_podium, inclusive = true).build()
                                findNavController().navigateSafe(action, options)
                            }
                        } else {
                            setPositiveButton(resources.getString(R.string.podium_join_back)) { dialog, which ->
                                dialog.dismiss()
                                val action = NavGraphHomeDirections.actionGlobalNavLivePodium(it.otherPodiumId)
                                val options = NavOptions.Builder().setPopUpTo(R.id.nav_live_podium, inclusive = true).build()
                                findNavController().navigateSafe(action, options)
                            }
                        }
                    }
                    builder.show()
                    return@observe
                }

                is PodiumLiveViewModel.PodiumKickReason.PodiumClosed -> when (it.closedBy) {
                    PodiumClosedBy.MANAGER -> showToast(R.string.podium_closed_by_manager)
                    PodiumClosedBy.ADMIN -> showToast(R.string.podium_closed_by_admin)
                    PodiumClosedBy.EMPOWERED, PodiumClosedBy.MINISTER -> showToast(R.string.podium_closed_by_empowered)
                    PodiumClosedBy.SYSTEM -> showToast(R.string.podium_closed_by_empowered)
                }

                PodiumLiveViewModel.PodiumKickReason.PodiumClosedByMe -> showToast(R.string.podium_closed_by_empowered)
                is PodiumLiveViewModel.PodiumKickReason.MismatchedGender -> when (it.entry) {
                    PodiumEntry.GENERAL -> {}
                    PodiumEntry.MEN_ONLY -> showToast(R.string.podium_gender_alert_male)
                    PodiumEntry.WOMEN_ONLY -> showToast(R.string.podium_gender_alert_female)
                }
            }
            popSafely()
        }

        //TODO this needs to go into the Worker
        viewModel.iAmSpeaker.observe(viewLifecycleOwner) {
            checkAudioPermission(binding.root) {
                viewModel.agoraSession?.apply {
                    setAsSpeaker(it)
                }
            }
        }

        viewModel.onNewSpeaker.observe(viewLifecycleOwner) {
            playTone(R.raw.podium_sound_one)
        }

        viewModel.onIGotRemovedFromSpeaker.observe(viewLifecycleOwner) { endByManger ->
            endByManger ?: return@observe
            if (endByManger) {
                showToast(R.string.podium_speaking_session_end_by_manager_message)
            } else {
                showToast(R.string.podium_speaking_session_end_by_admin_message)
            }
        }
        viewModel.iAmMuted.observe(viewLifecycleOwner) {
            viewModel.agoraSession?.apply {
                muteAudio(it)
            }
        }

        viewModel.closingPodium.observe(viewLifecycleOwner) {
            if (it) showAPILoader(getString(R.string.podium_closing)) else hideLoader()
        }

        viewModel.leavingPodium.observe(viewLifecycleOwner) {
            if (it) showAPILoader(getString(R.string.podium_leaving)) else hideLoader()
        }

        viewModel.onLikedByHost.observe(viewLifecycleOwner) {
            liveBindingElements.likesContainer?.apply {
                animateLike(this, likesHeartImage)
            }
        }

        viewModel.onLikedByParticipant.observe(viewLifecycleOwner) {
            liveBindingElements.likesContainer?.apply {
                animateLike(this, likesHeartImage)
            }
        }

        viewModel.onNavigateToPrivateMessage.observe(viewLifecycleOwner) {
            val action = NavGraphHomeDirections.actionGlobalNavigationChatPrivate(it.first, it.second)
            (activity as MainActivity).navController.navigateSafe(action)
        }

        viewModel.cameraTimeExpired.observe(viewLifecycleOwner) { expired ->
            if (expired) {
                if (viewModel.myVideoIsTurnedOn.value == true) {
                    viewModel.enableVideo(false)
                }
            }
        }

        viewModel.cameraTimeExpireSoon.observe(viewLifecycleOwner) { expireSoon ->
            cameraDisabledAlert(getString(R.string.podium_camera_time_expired, expireSoon.toMinutes().toString()), false)
        }

        viewModel.podiumInviteMaxAdminsError.observe(viewLifecycleOwner) {
            if (!it.isNullOrEmpty()) {
                showToast(R.string.title_podium_max_admin_recievor)
                viewModel.podiumInviteMaxAdminsErrorReset()
            }
        }

        viewModel.invitedToBeAdmin.observe(viewLifecycleOwner) {
            it ?: return@observe
            Log.d("GCVM", "observe: huddle admin status changed: $it")
            initAdminInvite(it)
        }

        viewModel.podiumAboutToClose.observe(viewLifecycleOwner) {
            if (it) {
                podiumCloseAlert = showSnackbar(getString(R.string.podium_about_to_close_message), Snackbar.LENGTH_INDEFINITE)
            } else {
                podiumCloseAlert?.dismiss()
                podiumCloseAlert = null
            }
        }

        viewModel.onPodiumGiftSent.observe(viewLifecycleOwner) {
            onGiftReceived(it)
        }

        viewModel.onSpeakerInvite.observe(viewLifecycleOwner) {
            confirmSpeakerInvite(it)
        }

        viewModel.onSpeakerInviteResponse.observe(viewLifecycleOwner) {
            if (it.first == AcceptDecline.INSUFFICIENT_COIN) {
                showToast(getString(R.string.podium_accept_invitation_insufficient_coin_message_to_host, it.second))
            } else if (it.first == AcceptDecline.DECLINE) {
                Toast.makeText(requireContext(), getString(R.string.podium_invite_declined_message_to_host, it.second), Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.onSpeakerInviteReplyError.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
        }

        viewModel.allowScrollToAdjacentPodium.observe(viewLifecycleOwner) {
            Log.w("SCRLL", "observe: allowScrollToAdjacentPodium: $it")
            refreshPodiumScroller()
        }
        viewModel.onPaidLikeInsufficientBalance.observe(viewLifecycleOwner) {
            if (it) {
                showInsufficientBalanceAlert(R.string.podium_paid_like_balance_error)
            }
        }

        viewModel.onRequestedToSpeakError.observe(viewLifecycleOwner) { msg ->
            msg?.let { showToast(message = it) }
        }

        challengeViewModel.onChallengeContributorRequest.observe(viewLifecycleOwner) { pl ->
            showExternalChallengeContributorRequestAlert(pl)
        }

        viewModel.liveTimer.observe(viewLifecycleOwner) {
            timerMenuItem?.setupTimerMenuItem(time = it)
        }

        viewModel.canSpeakByUserRating.observe(viewLifecycleOwner) {
            Log.w("PLAF","observe: can speak -> $it")
        }

        viewModel.canCommentByUserRating.observe(viewLifecycleOwner) {
            Log.w("PLAF","observe: can comment -> $it")
        }

        viewModel.isAdvancedMenuVisible.observe(viewLifecycleOwner) {
            Log.w("PLAF","observe: Advanced Menu -> $it")
        }

        viewModel.onInviteInsufficientBalanceError.observe(viewLifecycleOwner) {
//            showPodiumSpeakingInsufficientBalanceAlert(coinBalance = it)
            showToast(message = getString(R.string.podium_accept_invitation_insufficient_coin_message))
        }

        viewModel.flashatAnthem.observe(viewLifecycleOwner) {
            setupAnthem(it)
        }

        viewModel.onPlayAnthemError.observe(viewLifecycleOwner) {
            showToast(it)
        }

        viewModel.mainScreenComposeSpeaker.observe(viewLifecycleOwner) {
            Log.d("PLAF", "observe: Main Screen Compose Speaker $it")
        }
    }

    protected fun MenuItem.setupTimerMenuItem(time: String?) {
        val timerText = " $time "
        title = timerText.highlightOccurrences(timerText) { BackgroundColorSpan(ContextCompat.getColor(requireContext(), R.color.colorPrimaryDark)) }
        setupMenuItemTextColor(color = R.color.white, context = requireContext(), isTextBold = true)
    }

    protected open fun onAboutToJoin() {}

    protected open fun onGiftReceived(gift: SentGiftPayload) {
        if (gift.hasVideo) downloadAndShowGift(gift)
    }

    private fun showExternalChallengeContributorRequestAlert(challenge: PodiumChallenge) {
        Log.d("PLAF", "showExternalChallengeContributorRequestAlert: entered")
        Log.d("PLAF", "showExternalChallengeContributorRequestAlert: time remaining: ${challenge.contributorRequestedTimeRemaining.seconds} s")
        if (challenge.contributorRequestedTimeRemaining.seconds <= 0L) return
        var timerJob: Job? = null
        val managerName = challenge.facilitator?.name
        val isMaidan = challenge.challengeType == ChallengeType.MAIDAN
        Log.d("PLAF", "showExternalChallengeContributorRequestAlert: ${challenge.challengeType}")
        if (!isMaidan) return
        // TODO check if I can actually leave
        val currentPodiumWillEnd = viewModel.iAmManager.value==true && !viewModel.hasOtherElevatedSpeakers
        val message = if(currentPodiumWillEnd) {
            if ((challenge.competitorFee ?: 0.0) == 0.0) getString(R.string.podium_free_maidan_invite_message_external_host, managerName)
            else getString(R.string.podium_maidan_invite_message_external_host, managerName, challenge.competitorFee.toString())
        }
        else {
            if ((challenge.competitorFee ?: 0.0) == 0.0) getString(R.string.podium_free_maidan_invite_message_external_audience, managerName)
            else getString(R.string.podium_maidan_invite_message_external_audience, managerName, challenge.competitorFee.toString())
        }

        val alert = MaterialAlertDialogBuilder(requireContext())
            .setMessage(message)
            .setCancelable(false)
            .setPositiveButton(R.string.podium_maidan_challenge) { dialog, _ ->
                fun respond() {
                    challengeViewModel.respondToContributorRequest(challenge, true)
                    dialog.dismiss()
                    timerJob?.cancel()
                }
                if (currentPodiumWillEnd) {
                    viewModel.close {
                        respond()
                    }
                } else {
                    viewModel.leave { left ->
                        if(!left) return@leave
                        respond()
                    }
                }
            }
            .setNegativeButton(R.string.common_decline) { dialog, _ ->
                challengeViewModel.respondToContributorRequest(challenge,false)
                dialog.dismiss()
                timerJob?.cancel()
            }.show()

        timerJob = DateTimeUtils.countDownTimerFlow(challenge.contributorRequestedTimeRemaining.seconds*1000).onCompletion { cause ->
            if (cause is CancellationException) {
                // Flow was canceled
                println("Flow was canceled")
            } else {
                // Flow completed normally
                if (alert.isShowing) {
                    alert.dismiss()
                }
            }
        }.launchIn(lifecycleScope)
    }

    private var adjacentPodiums: Pair<PodiumPreview?,PodiumPreview?> = Pair(null, null)

    private fun setupPodiumScroller() {
        liveBindingElements.scrollerLayout.apply {
            setEnableRefresh(false)
            setEnableLoadMore(false)
            setEnableAutoLoadMore(false)
            setOnRefreshListener {
                Log.w("PDPresenter", "setOnRefreshListener")
                val next = adjacentPodiums.first?: return@setOnRefreshListener

                podiumLiveSwipeToAnotherPodiumAlert(
                    skipAlertBox = viewModel.skipPodiumSwipeAlertBox,
                    isNextPodiumAdmin = next.isAdmin,
                    nextPodiumJoiningFee = next.joiningFee ?: 0,
                    isNextPodiumJoiningFeePaid = next.isJoiningFeePaid,
                    onCancel = {
                        closeHeaderOrFooter()
                    },
                    onConfirm = {
                        onAboutToSwitchToAnotherPodium {
                            viewModel.leave { left ->
                                if(!left) return@leave
                                val options = NavOptions.Builder().setPopUpTo(R.id.nav_live_podium, inclusive = true).build()
                                val action = NavGraphHomeDirections.actionGlobalNavLivePodium(next.id, kind = next.kind?.ordinal ?: -1, enableScrollForTab = enableScrollForTab?.ordinal ?: -1)
                                findNavController().navigateSafe(action, options)
                            }
                        }
                    }
                )
            }
            setOnLoadMoreListener {
                Log.w("PDPresenter", "setOnLoadMoreListener")
                val next = adjacentPodiums.second?: return@setOnLoadMoreListener

                podiumLiveSwipeToAnotherPodiumAlert(
                    skipAlertBox = viewModel.skipPodiumSwipeAlertBox,
                    nextPodiumJoiningFee = next.joiningFee ?: 0,
                    isNextPodiumAdmin = next.isAdmin,
                    isNextPodiumJoiningFeePaid = next.isJoiningFeePaid,
                    onCancel = {
                        closeHeaderOrFooter()
                    },
                    onConfirm = {
                        onAboutToSwitchToAnotherPodium {
                            viewModel.leave { left ->
                                if(!left) return@leave
                                val options = NavOptions.Builder().setPopUpTo(R.id.nav_live_podium, inclusive = true).build()
                                val action = NavGraphHomeDirections.actionGlobalNavLivePodium(next.id, kind = next.kind?.ordinal ?: -1, enableScrollForTab = enableScrollForTab?.ordinal ?: -1)
                                findNavController().navigateSafe(action, options)
                            }
                        }
                    }
                )
            }
        }
    }

    private fun PodiumSwipAlertType.setupMessage(nextPodiumJoiningFee: Int) : String = when(this) {
        PodiumSwipAlertType.PaidUserSwipeToPaidPodium -> getString(R.string.podium_swipe_from_joining_fee_podium_confirmation_message, "$nextPodiumJoiningFee")
        PodiumSwipAlertType.PaidUserSwipeToFreePodium -> getString(R.string.podium_swipe_free_join_fee_message)
        PodiumSwipAlertType.SwipeToPaidPodium -> getString(R.string.podium_swipe_paid_join_fee_message, "$nextPodiumJoiningFee")
        PodiumSwipAlertType.SwipeToFreePodium -> ""
    }

    private fun podiumLiveSwipeToAnotherPodiumAlert(
        skipAlertBox: Boolean,
        nextPodiumJoiningFee: Int,
        isNextPodiumJoiningFeePaid: Boolean?,
        isNextPodiumAdmin : Boolean?,
        onCancel: () -> Unit,
        onConfirm: () -> Unit,
    ) {
        val currentPodiumDetails = viewModel.podium.value
        val currentPodiumJoiningFee = currentPodiumDetails?.joiningFee ?: 0
        val canJoinPodiumFree = viewModel.user.userEmpowerment?.allowJoinSpeakPodiumForFree == true

        val isSpeaker = viewModel.isSpeaker(viewModel.user.id)
        val speakingFee = currentPodiumDetails?.audienceFee ?: 0
        val isSpeakerWithSpeakingFee = isSpeaker && speakingFee != 0
        val isPaidSpeakingFeeOrJoiningFee = isSpeakerWithSpeakingFee || currentPodiumJoiningFee != 0
        val nextPodiumJoiningFeePaid = isNextPodiumJoiningFeePaid
        val isCurrentPodiumPaid = currentPodiumDetails?.joiningFeePaid == true

        val type = when {
            isPaidSpeakingFeeOrJoiningFee && nextPodiumJoiningFee != 0 -> PodiumSwipAlertType.PaidUserSwipeToPaidPodium
            isPaidSpeakingFeeOrJoiningFee && nextPodiumJoiningFee == 0 -> PodiumSwipAlertType.PaidUserSwipeToFreePodium
            nextPodiumJoiningFee != 0 -> PodiumSwipAlertType.SwipeToPaidPodium
            else -> PodiumSwipAlertType.SwipeToFreePodium
        }

        if (type == PodiumSwipAlertType.SwipeToFreePodium || isNextPodiumAdmin == true || nextPodiumJoiningFeePaid == true || canJoinPodiumFree) {
            onConfirm()
            return
        }

        if (type == PodiumSwipAlertType.PaidUserSwipeToFreePodium && skipAlertBox) {
            onConfirm()
            return
        }

        showFlashatDialog {
            setMessage(type.setupMessage(nextPodiumJoiningFee = nextPodiumJoiningFee))
            setCloseButton(title = R.string.common_cancel, icon = R.drawable.ic_close) {
                onCancel()
                true
            }
            setCheckBox(message = R.string.profile_dont_ask, checked = false, isVisible = type == PodiumSwipAlertType.PaidUserSwipeToFreePodium)
            setConfirmButton(title = R.string.common_confirm) {
                if (nextPodiumJoiningFee > viewModel.user.coinBalance && nextPodiumJoiningFeePaid==false) {
                    viewModel.exit()
                    showToast(message = R.string.podium_insufficient_coin_balance)
                    return@setConfirmButton true
                }
                onConfirm()
                if (type == PodiumSwipAlertType.PaidUserSwipeToFreePodium) {
                    viewModel.setPodiumSwipeAlertBoxVisibility(checkBoxChecked)
                }
                true
            }
        }
    }

    open fun onAboutToSwitchToAnotherPodium(proceed: (Boolean) -> Unit) {
        proceed.invoke(false)
    }

    private fun refreshPodiumScroller() {
        Log.d("SCRLL", "setupPodiumScroller: ${viewModel.allowScrollToAdjacentPodium.value}")
        activity?: return
        val refresh: SmartRefreshLayout = liveBindingElements.scrollerLayout
        if(viewModel.allowScrollToAdjacentPodium.value==false || enableScrollForTab == null || viewModel.flashatAnthem.value?.playing==true) {
            refresh.setEnableRefresh(false)
            refresh.setEnableLoadMore(false)
            adjacentPodiums = Pair(null, null)
            return
        }
        Log.d("SCRLL", "setupPodiumScroller: activity exists")
        enableScrollForTab?.let { tab ->

            fun setHeader(prev: PodiumPreview) {
                Log.d("SCRLL", "setupPodiumScroller: prev: $prev")
                adjacentPodiums = Pair(prev, adjacentPodiums.second)
                refresh.setEnableRefresh(true)
                val header = PodiumScrollPreviewView(requireContext()).apply {
                    setData(prev, true)
                }
                refresh.setRefreshHeader(header)
                refresh.setHeaderTriggerRate(1.0f)
            }

            fun setFooter(next: PodiumPreview) {
                Log.d("SCRLL", "setupPodiumScroller: next: $next")
                adjacentPodiums = Pair(adjacentPodiums.first, next)
                refresh.setEnableLoadMore(true)
                val footer = PodiumScrollPreviewView(requireContext()).apply {
                    setData(next, false)
                }
                refresh.setRefreshFooter(footer)
                refresh.setFooterTriggerRate(1.0f)
            }

            Log.d("SCRLL", "setupPodiumScroller: tab is $tab")
            when (tab) {
                PodiumTab.LIVE_PODIUM -> {
                    val current = livePodiumPagingDataPresenter.findIndexOf(podiumIdArg)
                    Log.d("SCRLL", "setupPodiumScroller: current index $current")
                    if (current < 0) return
                    if (current > 0) {
                        fun getPrevious(): Podium? {
                            var i = current-1
                            while (i>=0) {
                                val p = livePodiumPagingDataPresenter[i] ?: return null
                                if (p.allowedFor(viewModel.user)) return p
                                i--
                            }
                            return null
                        }
                        getPrevious()?.let {
                            Log.d("SCRLL", "setupPodiumScroller: prev $it")
                            val prev = PodiumPreview.LivePodium(it)
                            if (adjacentPodiums.first?.id != prev.id) {
                                setHeader(prev)
                            }
                        }
                    }
                    if (livePodiumPagingDataPresenter.size > current + 1) {
                        fun getNext(): Podium? {
                            var i = current+1
                            while (i<livePodiumPagingDataPresenter.size) {
                                val p = livePodiumPagingDataPresenter[i] ?: return null
                                if (p.allowedFor(viewModel.user)) return p
                                i++
                            }
                            return null
                        }
                        getNext()?.let {
                            Log.d("SCRLL", "setupPodiumScroller: next $it")
                            val next = PodiumPreview.LivePodium(it)
                            if (adjacentPodiums.second?.id != next.id) {
                                setFooter(next)
                            }
                        }
                    }
                    current
                }

                PodiumTab.LIVE_FRIENDS -> {
                    val current = liveFriendsPagingDataPresenter.findIndexOfFriend(podiumIdArg)
                    if (current < 0) return
                    if (current > 0) {
                        fun getPrevious(): PodiumFriend? {
                            var i = current-1
                            while (i>=0) {
                                val p = liveFriendsPagingDataPresenter[i] ?: return null
                                val cp = liveFriendsPagingDataPresenter[current]?: return null
                                if (p.allowedFor(viewModel.user) && cp.podiumId!=p.podiumId) return p
                                i--
                            }
                            return null
                        }
                        getPrevious()?.let {
                            val prev = PodiumPreview.LiveFriend(it)
                            if (adjacentPodiums.first?.id != prev.id) {
                                setHeader(prev)
                            }
                        }
                    }
                    if (liveFriendsPagingDataPresenter.size > current + 1 && adjacentPodiums.second == null) {
                        fun getNext(): PodiumFriend? {
                            var i = current+1
                            while (i<liveFriendsPagingDataPresenter.size) {
                                val p = liveFriendsPagingDataPresenter[i] ?: return null
                                if (p.allowedFor(viewModel.user)) return p
                                i++
                            }
                            return null
                        }
                        getNext()?.let {
                            val next = PodiumPreview.LiveFriend(it)
                            if (adjacentPodiums.second?.id != next.id) {
                                setFooter(next)
                            }
                        }
                    }
                    current
                }

                else -> {}
            }
        }
    }

    private var podiumCloseAlert: Snackbar? = null

    protected open fun setMainScreenSpeaker(binding: ItemPodiumSpeakerMainBinding, item: ActiveSpeakerUIModel?) {
        Log.w("AGORA", "setMainScreenSpeaker: $item")
        binding.apply {
            speaker = item
            isSelf = viewModel.user.id == item?.speaker?.id
            speakerHeader.setSpeakerTitle(item)
        }
    }

    protected fun ItemPodiumSpeakerHeaderBinding.setSpeakerTitle(item: ActiveSpeakerUIModel?) {
        var textColor = R.color.textColorOnPrimary
        var backgroundColor = R.color.colorPrimary

        val speaker = item?.speaker?: return
        val isMainScreen = viewModel.isMainScreenSpeaker(speaker) || viewModel.isSecondMainScreenSpeaker(speaker)

        var notchText = if (speaker.inactive) {
            textColor = R.color.colorError
            backgroundColor = R.color.colorAlwaysLightSurfaceSecondary
            resources.getString(R.string.user_citizenship_inactive)
        } else when (speaker.citizenship) {
            UserCitizenship.VISITOR -> {
                textColor = R.color.textColorOnSecondary
                backgroundColor = R.color.colorAlwaysLightSurfaceSecondaryDarker
                resources.getString(R.string.user_citizenship_visitor)
            }
            UserCitizenship.RESIDENT -> {
                textColor = R.color.textColorOnPrimary
                backgroundColor = R.color.colorPodiumSpeakerResident
                resources.getString(R.string.user_citizenship_resident)
            }
            UserCitizenship.CITIZEN -> {
                textColor = R.color.colorPrimaryColorDarkest1
                backgroundColor = R.color.colorPodiumSpeakerCitizen
                resources.getString(R.string.user_citizenship_citizen)
            }

            UserCitizenship.OFFICER -> {
                textColor = R.color.textColorAlwaysLightPrimary
                backgroundColor = R.color.colorPodiumSpeakerOfficer
                resources.getString(R.string.user_citizenship_officer)
            }

            UserCitizenship.AMBASSADOR -> {
                textColor = R.color.colorPrimaryColorDarkest1
                backgroundColor = R.color.colorPodiumSpeakerAmbassador
                resources.getString(R.string.user_citizenship_ambassador)
            }

            UserCitizenship.MINISTER -> {
                textColor = R.color.textColorOnPrimary
                backgroundColor = R.color.colorPrimary
                resources.getString(R.string.user_citizenship_minister)
            }

            UserCitizenship.PRESIDENT -> {
                textColor = R.color.colorPrimary
                backgroundColor = R.color.colorSecondaryDark
                resources.getString(R.string.user_citizenship_president)
            }
            UserCitizenship.GOLDEN -> {
                textColor = R.color.white
                backgroundColor = R.color.colorSecondaryDark
                resources.getString(R.string.user_citizenship_golden)
            }
            null -> "<>"
        }
        val needsHostTag = viewModel.podiumKind.value in listOf(PodiumKind.INTERVIEW, PodiumKind.LECTURE, PodiumKind.THEATER)
        if (viewModel.isManager(speaker.id) && isMainScreen && needsHostTag) {
            notchText = resources.getString(R.string.podium_speaker_prefix_manager, notchText)
        }

        userTitle.text = notchText
        ContextCompat.getColor(userTitle.context, textColor).let {
            userTitle.setTextColor(it)
            coinsReceived.setTextColor(it)
        }
        if (speaker.citizenship == UserCitizenship.PRESIDENT) {
            holder.setBackgroundResource(R.drawable.bg_president_podium_speaker_title)
        }else if(speaker.citizenship == UserCitizenship.GOLDEN) {
            holder.setBackgroundResource(R.drawable.bg_golden_podium_speaker_title)
        } else {
            holder.setBackgroundColor(ContextCompat.getColor(userTitle.context, backgroundColor))
        }
    }

    private var apiLoader: MaterialDialog? = null

    internal fun showAPILoader(message: String) {
        apiLoader = showLoader(message)
    }

    internal fun hideLoader() {
        apiLoader?.dismiss()
        apiLoader = null
    }

    protected open fun confirmLeave(confirm: () -> Unit) {
        confirmAction(
            title = R.string.podium_action_leave_confirm_title,
            message = R.string.podium_action_leave_confirm_message,
            positiveTitle = R.string.podium_action_leave,
            negativeTitle = R.string.common_cancel
        ) {
            confirm.invoke()
        }
    }

    open fun canExit(): Boolean {
        return true
    }

    protected fun confirmExit(confirm: () -> Unit) {
        if (!canExit()) return
        confirmAction(
            title = R.string.podium_action_exit_confirm_title,
            message = R.string.podium_action_exit_confirm_message,
            positiveTitle = R.string.common_confirm,
            negativeTitle = R.string.common_cancel
        ) {
            confirm.invoke()
        }
    }

    protected open fun confirmClose(confirm: () -> Unit) {
        confirmAction(
            title = R.string.podium_action_close_confirm_title, message = R.string.podium_action_close_confirm_message, positiveTitle = R.string.common_confirm, negativeTitle = R.string.common_cancel
        ) {
            confirm.invoke()
        }
    }

    protected fun confirmCommentsDisable(confirm: () -> Unit) {
        confirmAction(
            message = if (viewModel.podium.value?.chatDisabled == true) R.string.podium_resume_comments_confirmation
            else R.string.podium_pause_comments_confirmation, positiveTitle = R.string.common_confirm, negativeTitle = R.string.common_cancel
        ) {
            confirm.invoke()
        }
    }

    protected fun confirmLikeDisable(confirm: () -> Unit) {
        confirmAction(
            message = if (viewModel.podium.value?.likesDisabled == true) R.string.podium_resume_likes_confirmation
            else R.string.podium_pause_likes_confirmation, positiveTitle = R.string.common_confirm, negativeTitle = R.string.common_cancel
        ) {
            confirm.invoke()
        }
    }
    protected fun confirmGiftDisable(confirm: () -> Unit) {
        confirmAction(
            message = if (viewModel.podium.value?.podiumGiftPaused == true) R.string.podium_resume_gift_confirmation else R.string.podium_pause_gift_confirmation,
            positiveTitle = if (viewModel.podium.value?.podiumGiftPaused == true) R.string.podium_resume else R.string.podium_pause,
            negativeTitle = R.string.common_cancel
        ) {
            confirm.invoke()
        }
    }

    protected fun confirmHideLiveUsers(confirm: () -> Unit) {
        confirmAction(
            message = if (viewModel.podiumHideLiveUsers.value == false) getString(R.string.podium_live_users_hide) else getString(R.string.podium_live_users_unhide),
            positiveTitle = R.string.common_confirm,
            negativeTitle = R.string.common_cancel
        ) {
            confirm.invoke()
        }
    }

    protected fun showPodiumSpeakingInsufficientBalanceAlert(requiredCoins: Int?)  {
        showFlashatDialog {
            setMessage(getString(R.string.podium_invite_confirm_insufficient_error_message, "${requiredCoins ?: 0}"))
            setConfirmButton(R.string.title_buy_coins,R.drawable.ic_coin,false) {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalBuyflaxFragment(isBuyCoin = true))
                true
            }
            setCloseButton(R.string.common_later, R.drawable.ic_close) {
                true
            }
        }
    }

    protected open fun confirmSpeakerInvite(invite: Podium.SpeakerInvite) {
        try {
            val podium = viewModel.podium.value?: return
            val haveEmpowermentToSpeak = viewModel.user.userEmpowerment?.allowJoinSpeakPodiumForFree == true

            val message = if (invite.joinType==TheaterJoinType.STAGE) {
                if (haveEmpowermentToSpeak) {
                    getString(R.string.podium_challenge_confirm_invite_message_stage)
                } else if ((podium.stageFee ?: 0) > 0) {
                    getString(R.string.podium_theater_invite_message, podium.stageFee?.toString()?:"-")
                } else {
                    getString(R.string.podium_challenge_confirm_invite_message_stage)
                }
            }
            else if (invite.joinType==TheaterJoinType.AUDIENCE) {
                if (haveEmpowermentToSpeak) {
                    getString(R.string.podium_challenge_confirm_invite_message)
                } else if((podium.audienceFee ?: 0) > 0) {
                    getString(R.string.podium_theater_invite_message,podium.audienceFee?.toString()?:"-")
                } else {
                    getString(R.string.podium_challenge_confirm_invite_message)
                }
            }
            else if (haveEmpowermentToSpeak) {
                getString(R.string.podium_challenge_confirm_invite_message)
            }
            else if ((invite.inviteFee ?: 0) != 0) getString(R.string.podium_challenge_confirm_invite_with_fee_message, "${invite.inviteFee ?: 0}")
            else getString(R.string.podium_challenge_confirm_invite_message)

            var timerJob: Job? = null

            val alert = showFlashatDialog {
                setMessage(message = message)
                setCloseButton(R.string.common_decline, R.drawable.ic_close) {
                    timerJob?.cancel()
                    viewModel.replySpeakerInvite(false,invite.joinType, invitedForFree = invite.invitedForFree)
                    true
                }
                setConfirmButton(R.string.common_accept) {
                    timerJob?.cancel()
                    viewModel.replySpeakerInvite(true,invite.joinType, invite.invitedForFree)
                    true
                }
            }

            if (viewModel.podiumKind.value in listOf(PodiumKind.INTERVIEW,PodiumKind.THEATER)) {
                invite.timeToExpire?.let { tte ->
                    timerJob = DateTimeUtils.countDownTimerFlow(tte.toMillis()).onEach { }.onCompletion { cause ->
                        if (cause is CancellationException) {
                            // Flow was canceled
                            println("Flow was canceled")
                        } else {
                            // Flow completed normally
                            if (alert.isShowing) {
                                alert.dismiss()
                                if (viewModel.podiumKind.value == PodiumKind.INTERVIEW) {
                                    viewModel.replySpeakerInvite(false, invite.joinType)
                                }
                            }
                        }
                    }.launchIn(lifecycleScope)
                }
            }

        } catch (e: Exception) {
            Firebase.crashlytics.recordException(e)
        }
    }

    private fun initAdminInvite(invitedToBeAdmin: Boolean) {
        liveBindingElements.actionDecorHolderTop?.apply {
            removeAllViews()
            when (invitedToBeAdmin) {
                true -> {
                    val binding = DataBindingUtil.inflate<LayoutPodiumAdminInviteCollapsedBinding>(layoutInflater, R.layout.layout_podium_admin_invite_collapsed, this, false)
                    binding.dayNight = isDisplayedOnDayNightSurface
                    binding.root.setOnClickListener {
                        ensureAnthemNotPlaying {
                            removeAllViews()
                            val expanded = DataBindingUtil.inflate<LayoutPodiumAdminInviteExpandedBinding>(layoutInflater, R.layout.layout_podium_admin_invite_expanded, this, false)
                            expanded.acceptButton.setOnClickListener {
                                ensureAnthemNotPlaying {
                                    ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                                        if (!viewModel.isAdminCountReachLimit()) {
                                            viewModel.respondAdminInviteAction(PodiumActionType.CONFIRM_PODIUM_ADMIN_INVITE)
                                        } else {
                                            showToast(R.string.title_podium_max_admin_recievor)
                                        }
                                    }
                                }
                            }
                            expanded.dayNight = isDisplayedOnDayNightSurface
                            expanded.declineButton.setOnClickListener {
                                ensureAnthemNotPlaying {
                                    viewModel.respondAdminInviteAction(PodiumActionType.DECLINE_PODIUM_ADMIN_INVITE)
                                }
                            }
                            expanded.loading = viewModel.podiumActionLoading
                            expanded.lifecycleOwner = viewLifecycleOwner
                            addView(expanded.root)
                        }
                    }
                    addView(binding.root)
                    isVisible = true
                }
                else -> {
                    isVisible = false
                }
            }
        }
    }

    private fun animateLike(container: ItemPodiumLikesContainerBinding, heartImage: AppCompatImageView, range: IntRange = 1000..5000) {

        fun cloneImage(): AppCompatImageView {
            val clone = AppCompatImageView(requireContext())
            clone.layoutParams = heartImage.layoutParams
            clone.isVisible = true
            clone.elevation = 10.DpToPx.toFloat()
            clone.setImageDrawable(heartImage.drawable)
//            clone.imageTintList = ContextCompat.getColor(requireContext(), color).let { ColorStateList.valueOf(it) }
            container.root.addView(clone)
            return clone
        }

        fun animateFlying(image: AppCompatImageView) {
            val x = 0f
            val y = 0f
            val r = Random.nextInt(range.first, range.last)
            val angle = 25f

            val path = Path().apply {
                when (r % 2) {
                    0 -> arcTo(RectF(x, y - r, x + 2 * r, y + r), 180f, angle)
                    else -> arcTo(RectF(x - 2 * r, y - r, x, y + r), 0f, -angle)
                }
            }

            ObjectAnimator.ofFloat(image, View.X, View.Y, path).apply {
                duration = 2000
                start()
            }
        }

        fun animateFading(image: AppCompatImageView) {
            image.animate().alpha(0f).setDuration(2000).setListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    container.root.removeView(image)
                }
            })
        }

        fun disableAllParentsClip(_view: View) {
            var view = _view
            view.parent?.let {
                while (view.parent is ViewGroup) {
                    val viewGroup = view.parent as ViewGroup
                    viewGroup.clipChildren = false
                    viewGroup.clipToPadding = false
                    view = viewGroup
                }
            }
        }

        // Disable clips on all parent generations
        disableAllParentsClip(heartImage)

        // Create clone
        val imageClone = cloneImage()

        // Animate
        animateFlying(imageClone)
        animateFading(imageClone)
    }

    protected var popup: PopupMenu? = null
    protected var timerMenuItem : MenuItem? = null

    protected open fun showMoreMenu(v: View): PopupMenu {

        val popup = PopupMenu(requireContext(), v)
        popup.menuInflater.inflate(R.menu.menu_podium_live_group_actions, popup.menu)
        popup.setForceShowIcon(true)
        popup.menu.apply {
            findItem(R.id.podium_timer)?.apply {
                timerMenuItem = this
                setupTimerMenuItem(time = viewModel.liveTimer.value)
            }
            findItem(R.id.action_admins).isVisible = viewModel.iAmManager.value == true
            findItem(R.id.action_restricted).isVisible = viewModel.iAmElevated.value == true
            findItem(R.id.action_exit).isVisible = if(viewModel.iAmManager.value == true) false else if(viewModel.iAmElevated.value==true) viewModel.hasOtherElevatedSpeakers else (viewModel.iCanExitPodium() || viewModel.privilegedCitizenship)
            findItem(R.id.action_report_podium).apply {
                setAsDestructiveMenuItem(requireContext())
                isVisible = viewModel.iAmManager.value != true && viewModel.user.premium
            }
            findItem(R.id.action_close).isVisible =
                viewModel.iAmManager.value == true  || viewModel.iHavePowerToEndPodium /*|| (viewModel.iAmElevated.value==true && !viewModel.isManagerAvailable())*/
            findItem(R.id.action_challenges).isVisible = false
//            findItem(R.id.action_leave).isVisible = if(viewModel.iAmManager.value == true) viewModel.hasOtherElevatedSpeakers else viewModel.podium.value?.isInvited==true || viewModel.podium.value?.isInvitee==true
            findItem(R.id.action_pause_resume_likes).apply {
                isVisible = (viewModel.iAmManager.value == true || viewModel.iAmElevated.value == true) && viewModel.podiumKind.value?.isBirthDayPodium == false
                setTitle(
                    if (viewModel.podium.value?.likesDisabled == true) R.string.podium_resume_likes
                    else R.string.podium_pause_likes
                )
            }
            findItem(R.id.action_pause_resume_gift).apply {
                isVisible = (viewModel.iAmManager.value == true || viewModel.iAmElevated.value == true) && viewModel.podiumKind.value?.isBirthDayPodium == false
                setTitle(
                    if (viewModel.podium.value?.podiumGiftPaused == true) R.string.podium_resume_gift
                    else R.string.podium_pause_gift
                )
            }
            findItem(R.id.action_pause_resume_mic).apply {
                isVisible = (viewModel.iAmManager.value == true || viewModel.iAmElevated.value == true) && viewModel.podiumKind.value?.isBirthDayPodium == false
                setTitle(
                    if (viewModel.podium.value?.requestToSpeakDisabled == true) R.string.podium_resume_mic
                    else R.string.podium_pause_mic
                )
            }

            findItem(R.id.action_pause_resume_comments).apply {
                isVisible = viewModel.iAmElevated.value == true
                setTitle(
                    if (viewModel.podium.value?.chatDisabled == true) R.string.podium_resume_comments
                    else R.string.podium_pause_comments
                )
            }

            findItem(R.id.action_send_invitations).isVisible = viewModel.iAmElevated.value == true

            findItem(R.id.action_hide_live_users).apply {
                isVisible = (viewModel.iAmMinister && viewModel.iAmManager.value == true)|| viewModel.iAmPresident
                setTitle(
                    if (viewModel.podiumHideLiveUsers.value == true) getString(R.string.podium_action_unhide_live_users)
                    else getString(R.string.podium_hide_live_users)
                )
            }

            findItem(R.id.action_share).isVisible = viewModel.podium.value?.isPrivate == false || viewModel.iAmElevated.value == true
            findItem(R.id.action_advanced_settings).isVisible = viewModel.isAdvancedMenuVisible.value == true
            findItem(R.id.action_anthem).isVisible = viewModel.iAmElevated.value == true && viewModel.podiumKind.value?.isBirthDayPodium == false
        }

        popup.setOnMenuItemClickListener { onMenuClick(it) }
        popup.setOnDismissListener {
            this.popup = null
            timerMenuItem = null
        }
        popup.show()
        this.popup = popup
        return popup
    }

    @CallSuper
    protected open fun onMenuClick(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_send_invitations -> ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                toInvitations()
            }
            R.id.action_leave -> confirmLeave {
                viewModel.leave()
            }

            R.id.action_exit -> {
                onExit()
            }

            R.id.action_close -> {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    confirmClose { viewModel.close() }
                }
            }

            R.id.action_pause_resume_likes -> ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                confirmLikeDisable {
                    viewModel.disableLikes()
                }
            }

            R.id.action_pause_resume_gift -> ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                confirmGiftDisable {
                    if (viewModel.podium.value?.podiumGiftPaused == true)
                        viewModel.pauseGift(PodiumPauseGift.PODIUM_RESUME_GIFT) else viewModel.pauseGift(PodiumPauseGift.PODIUM_PAUSE_GIFT)
                }
            }

            R.id.action_pause_resume_mic ->  ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                confirmMicDisable {
                    viewModel.disableMic()
                }
            }

            R.id.action_challenges -> {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    viewModel.podiumId.value?.let {
                        viewModel.checkIfChallengeActiveAndNavigate()
                    }
                }
            }

            R.id.action_pause_resume_comments -> ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                confirmCommentsDisable {
                    ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                        viewModel.toggleComments()
                    }
                }
            }

            R.id.action_admins -> toAdminList()
            R.id.action_restricted -> toRestrictedUsers()

            R.id.action_about -> {
                viewModel.podium.value?.let {
                    val action = NavGraphHomeDirections.actionGlobalNavAboutPodium(it.id, it.role?.isElevated!!)
                    findNavController().navigateSafe(action)
                }
            }

            R.id.action_hide_live_users -> confirmHideLiveUsers {
                if (viewModel.podiumHideLiveUsers.value == true) viewModel.hideUnHideLiveUsers(false)
                else viewModel.hideUnHideLiveUsers(true)
            }

            R.id.action_share -> {
                sharePodium()
            }

            R.id.action_advanced_settings -> {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    viewModel.podium.value?.id?.let {
                        val action = NavLivePodiumDirections.actionGlobalAdvancedSettingsFragment(podiumId = it)
                        findNavController().navigateSafe(action)
                    }
                }
            }

            R.id.action_report_podium -> {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    viewModel.podium.value?.let {
                        val action = NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.PodiumReport(podium = it).serialize())
                        findNavController().navigateSafe(action)
                    }
                }
            }

            R.id.action_anthem -> {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    if (viewModel.challengeActive.value==true) {
                        showFlashatDialog {
                            setMessage(R.string.podium_play_anthem_error_challenge)
                            setCloseButton(R.string.common_close) {
                                true
                            }
                        }
//                    } else if (viewModel.user.coinBalance.toInt()<100) {
//                        showInsufficientBalanceAlert(requiredCoins = 100)
                    } else {
                        showFlashatDialog {
                            setTitle(R.string.podium_play_anthem)
                            setMessage(R.string.podium_play_anthem_confirm)
                            setConfirmButton(R.string.common_accept) {
                                ifStillAttached {
                                    viewModel.playFlashatAnthem()
                                }
                                true
                            }
                            setCloseButton(R.string.common_cancel) {
                                true
                            }
                        }
                    }
                }
            }

            else -> return false
        }
        return true
    }

    private fun confirmMicDisable(confirm: () -> Unit) {
        confirmAction(
            message = if (viewModel.podium.value?.requestToSpeakDisabled == true) R.string.podium_resume_mic_confirmation
            else R.string.podium_pause_mic_confirmation, positiveTitle = R.string.common_confirm, negativeTitle = R.string.common_cancel
        ) {
            confirm.invoke()
        }
    }
    protected fun toggleCameraFacing() {
        viewModel.switchCamera()
    }

     fun likeAndCountClickActions(header: String) {
        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutPodiumCameraDisabledBinding>(layoutInflater, R.layout.layout_podium_camera_disabled, null, false)
            view.textHeader = header
            val header = view.layoutHeader
            header.setPadding(header.paddingLeft, header.paddingTop, header.paddingRight,2)
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(true)
            view.actionClose.setOnClickListener {
                dismiss()
            }
        }
    }

    fun checkUserCanCommentByRating() : Boolean {
        Log.d("PLAF", "can comment -> ${viewModel.canCommentByUserRating.value}")
        if (viewModel.canCommentByUserRating.value == true) return true
        val message = if (viewModel.podium.value?.requiredRatingToComment == 100 && viewModel.user.userRatingPercent.toInt() != 100)
            getString(R.string.podium_comment_rating_hundred_error_message)
        else getString(R.string.podium_comment_rating_above_ninety_error_message)
        showUserJoinByRatingError(
            message = message,
            userRating = viewModel.user.userRatingPercent,
            isPremium = viewModel.user.premium
        )
        return false
    }

    fun checkUserCanSpeakByRating() : Boolean {
        Log.d("PLAF", "can speak -> ${viewModel.canSpeakByUserRating.value}")
        if (viewModel.canSpeakByUserRating.value == true) return true
        val message = if (viewModel.podium.value?.requiredRatingToSpeak == 100 && viewModel.user.userRatingPercent.toInt() != 100)
            getString(R.string.podium_speak_rating_hundred_error_message)
        else getString(R.string.podium_speak_rating_above_ninety_error_message)
        showUserJoinByRatingError(
            message = message,
            userRating = viewModel.user.userRatingPercent,
            isPremium = viewModel.user.premium
        )
        return false
    }

    fun showSpeakingConfirmationAlert(
        haveEmpowermentToSpeak: Boolean,
        confirmMessage: String,
        userCoins: Int?,
        podiumSpeakingFee: Int?,
        onConfirm: () -> Unit
    ) {
        if (((podiumSpeakingFee ?: 0) > (userCoins ?: 0)) && !haveEmpowermentToSpeak) {
            showPodiumSpeakingInsufficientBalanceAlert(requiredCoins = podiumSpeakingFee)
        } else {
            showFlashatDialog {
                setMessage(confirmMessage)
                setConfirmButtonVisible(true)
                setConfirmButton(R.string.common_confirm) {
                    onConfirm()
                    true
                }
            }
        }
    }

    private var anthemPresenter: PodiumAnthemPresenter? = null

    fun setupAnthem(anthem: Podium.Anthem?) {
        Log.d("TVVM", "setupAnthem: $anthem")
        if (anthem==null) {
            anthemPresenter?.let {
                it.cleanup()
                anthemPresenter = null
            }
            viewModel.muteAllAudio(false)
            refreshPodiumScroller()
            ActivePodiumTracker.registerActiveScreen(ActivePodiumTracker.ActivePodiumScreen.PodiumRoom(podiumIdArg), viewLifecycleOwner)
            return
        }
        val overlay = liveBindingElements.anthemOverlay?: return
        val presenter = anthemPresenter?: PodiumAnthemPresenter(overlay,object: PodiumAnthemPresenter.AnthemListener{
            override fun getFragment() = this@PodiumLiveAbstractFragment
            override fun onFinished() {
                anthemPresenter = null
            }

            override fun onShow(show: Boolean) {
                refreshPodiumScroller()
                executeExtraStepsToShowAnthem(show)
            }
        })
        lifecycleScope.launch {
            viewModel.muteAllAudio(true)
            viewModel.muteSelf()
            ActivePodiumTracker.registerActiveScreen(ActivePodiumTracker.ActivePodiumScreen.PodiumRoom(podiumIdArg, true), viewLifecycleOwner)
            presenter.playAnthem(anthem)
            showToast(R.string.podium_anthem_blocked_message)
        }
        anthemPresenter = presenter
    }

    private var lastAnthemToastTime = ZonedDateTime.now()

    fun ensureAnthemNotPlaying(notPlaying: () -> Unit) {
        Log.w("TVVM", "ensureAnthemNotPlaying: ${viewModel.flashatAnthem.value?.playing}")
        if (viewModel.flashatAnthem.value?.playing==true) {
            if (Duration.between(lastAnthemToastTime, ZonedDateTime.now()).seconds > 10) {
                showToast(R.string.podium_anthem_blocked_message)
                lastAnthemToastTime = ZonedDateTime.now()
            }
            return
        }
        notPlaying.invoke()
    }

    protected open fun executeExtraStepsToShowAnthem(show: Boolean) {

    }

    override fun onDestroyView() {
        super.onDestroyView()
        Log.d("TVVM", "onDestroyView: cleaning up anthem presenter")
        anthemPresenter?.cleanup()
        anthemPresenter = null
    }

}