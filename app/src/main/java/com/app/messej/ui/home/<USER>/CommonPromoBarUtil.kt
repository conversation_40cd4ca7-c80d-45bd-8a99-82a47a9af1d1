package com.app.messej.ui.home.promobar

import android.content.Context
import android.util.Log
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.enums.LegalAffairsMainTab
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.socket.PromoAnnouncement
import com.app.messej.ui.home.publictab.authorities.AuthoritiesUtils.showSocialWelfareFreeUserAlert
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveAbstractFragment
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.showLoader
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.dialog.MaterialAlertDialogBuilder

typealias PromoBarQueue = MutableList<PromoAnnouncement>

object CommonPromoBarUtil {

    fun PromoBarQueue.insertPromo(pl: PromoAnnouncement) {

        pl.setCreatedDate()
        Log.d("ANNC", "existing ${pl.type}: ${this.count { it.type == pl.type }} | max: ${pl.maxInQueue}")
        while (this.count { it.type == pl.type } >= pl.maxInQueue) {
            // Remove the oldest element of same type
            val oldest = this.filter { it.type == pl.type }.minByOrNull { it.created }
            Log.d("ANNC", "remove to make space: $oldest")
            oldest ?: break
            this.remove(oldest)
        }
        Log.d("ANNC", "insertPromo: $pl")
        if (pl is PromoAnnouncement.AdminMessage && pl.message.isNullOrBlank()) { /* Do not add */ }
        else if (pl is PromoAnnouncement.AdminAnnouncementScheduled && pl.message.isNullOrEmpty()) {
            removeAdminScheduledMessage(adminScheduledMessage = pl)
        }
        // Special handling for birthday announcements
        else this.add(pl)
        Log.d("ANNC", "New List: $this")
        this.sortBy { it.created }
        this.sortBy { it.type }
    }

    fun PromoBarQueue.clearPromo(pType: PromoAnnouncement.AnnouncementType) {
        this.removeAll { it.type == pType }
    }

    fun PromoBarQueue.removeAdminScheduledOtherUserCitizenshipMessage(userCitizenship: UserCitizenship) {
        this.removeAll { promoQueue ->
            if (promoQueue is PromoAnnouncement.AdminAnnouncementScheduled) {
                if (promoQueue.citizenships.isNullOrEmpty()) return@removeAll false
                // Remove all admin scheduled announcements, if current user citizenship is not in the announcement's citizenship list.
                return@removeAll !promoQueue.citizenships.contains(element = userCitizenship)
            } else false
        }
    }

    fun PromoBarQueue.clearAllPromos() {
        this.clear()
        Log.d("ANNC", "Promo bar cleared")
    }

    fun PromoBarQueue.removeAdminScheduledMessage(adminScheduledMessage : PromoAnnouncement.AdminAnnouncementScheduled) {
        this.removeAll { promoQueue ->
            (promoQueue is PromoAnnouncement.AdminAnnouncementScheduled) && (promoQueue.id == adminScheduledMessage.id)
        }
        Log.d("ANNC", "Removed Admin Scheduled Message: $adminScheduledMessage")
    }

    private fun PodiumLiveAbstractFragment.toPodium(id: String, name: String) {
        fun leave() {
            showAPILoader(getString(R.string.common_loading))
//            viewModel.checkIfPodiumLive(id) { live ->
//                hideLoader()
//                if (live) {
//                    viewModel.leave { left ->
//                        if (left) {
//                            val options = NavOptions.Builder().setPopUpTo(R.id.nav_live_podium, inclusive = true).build()
//                            val action = NavGraphHomeDirections.actionGlobalNavLivePodium(podiumId = id)
//                            findNavController().navigateSafe(action, options)
//                        }
//                    }
//                } else {
//                    showToast(R.string.podium_error_not_live)
//                }
//            }

            viewModel.checkPodiumAccessibility(id) { podium ->
                hideLoader()
                if (podium.isLive) {
                    if ((podium.isPrivate && podium.isInvited) || !podium.isPrivate) {
                        viewModel.leave { left ->
                            if (left) {
                                val options = NavOptions.Builder()
                                    .setPopUpTo(R.id.nav_live_podium, inclusive = true).build()
                                val action =
                                    NavGraphHomeDirections.actionGlobalNavLivePodium(podiumId = id)
                                findNavController().navigateSafe(action, options)
                            }
                        }
                    } else if (!podium.isInvited) {
                        showFlashatDialog {
                            setMessage(getString(R.string.podium_error_private_not_invited))
                            setConfirmButtonVisible(visible = false)
                        }
                    }
                } else {
                    showFlashatDialog {
                        setMessage(getString(R.string.podium_error_not_live))
                        setConfirmButtonVisible(visible = false)
                    }
                }
            }

            if (viewModel.podiumId.value == id) return
            if (viewModel.iAmElevated.value == true && !viewModel.hasOtherElevatedSpeakers) return
            if (canExit()) {
                onAboutToSwitchToAnotherPodium { confirmed ->
                    if (!confirmed) {
                        confirmAction(
                            message = getString(
                                R.string.podium_action_leave_join_confirm_message,
                                name
                            ),
                        ) {
                            leave()
                        }
                    } else leave()
                }
            }
        }
    }

    fun PodiumLiveAbstractFragment.setupPodiumPromoBoard(view: ComposeView) {
        val promoBarViewModel: CommonPromoBarViewModel by activityViewModels()
        promoBarViewModel.extendDelayForReset()
        view.setContent {
            val messageState = promoBarViewModel.promoCycle.observeAsState()
            val message = messageState.value
            PromoBar(message, {
                promoBarViewModel.setDelay(it)
            }) {
                if (viewModel.flashatAnthem.value?.playing==true) {
                    ensureAnthemNotPlaying {  }
                    return@PromoBar
                }
                when(it) {
                    is PromoAnnouncement.AdminMessage -> {
                        MaterialAlertDialogBuilder(requireContext())
                            .setMessage(it.message)
                            .setPositiveButton(getText(R.string.common_close)) { dialog, _ ->
                                dialog.dismiss()
                            }.show()
                    }
                    is PromoAnnouncement.UserUpgrade -> {
                        if (canExit()) {
                            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(it.userId))
                        }
                    }
                    is PromoAnnouncement.TopLikedPodium -> toPodium(it.id,it.name)
                    is PromoAnnouncement.TopUserPodium -> toPodium(it.id,it.name)
                    is PromoAnnouncement.MaidanResult -> toPodium(it.podiumId, getString(R.string.podium_maidan_vs,it.winnerName,it.loserName))
                    is PromoAnnouncement.MostGenerousPodium -> toPodium(it.podiumId,it.name)
                    is PromoAnnouncement.CaseReported -> {
                        if (canExit()) {
                            if (promoBarViewModel.user.premium) {
                                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalLegalAffairsFragment(defaultMainTab = LegalAffairsMainTab.InvestigationBureau))
                            } else {
                                freeUserPromoBarValidation {
                                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalUpgradePremiumFragment())
                                }
                                return@PromoBar

                            }
                        }
                    }
                    is PromoAnnouncement.StrongestTribe -> {
                        if (canExit()) {
                            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(it.managerId))
                        }
                    }
                    is PromoAnnouncement.MostGenerousUser -> {
                        if (canExit()) {
                            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(it.userId))
                        }
                    }
                    is PromoAnnouncement.AdminAnnouncementScheduled -> {
                        showAdminAnnouncementDialog(context = requireContext(), message = it.message)
                    }
                    is PromoAnnouncement.Birthday -> {
                        if (canExit()) {
                            val userId = it.user.userId
                            if (userId != promoBarViewModel.user.id) {
                                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalBirthdayNotificationBottomSheetFragment(userId = userId, currentBirthday = true))
                            }
                        }
                    }
                    is PromoAnnouncement.SocialCases -> {
                        if (canExit()) {
                            if (promoBarViewModel.user.premium) {
                                findNavController().navigateSafe(direction = NavGraphHomeDirections.actionGlobalSocialAffairActiveCasesFragment())
                            } else {
                                showSocialWelfareFreeUserAlert()
                            }
                        }
                    }
                }
            }
        }
    }

    fun Fragment.setupPromoBoard(view: ComposeView) {

        val promoBarViewModel: CommonPromoBarViewModel by activityViewModels()
        promoBarViewModel.extendDelayForReset()
        fun toPodium(id: String) {
            val loader = showLoader()
            promoBarViewModel.checkPodiumAccessibility(id) { podium ->
                loader.dismiss()
                if (podium.isLive) {
                    if ((podium.isPrivate && (podium.isInvited || podium.isInvitee)) || !podium.isPrivate) {
                        val options = NavOptions.Builder().setPopUpTo(R.id.nav_live_podium, inclusive = true).build()
                        val action = NavGraphHomeDirections.actionGlobalNavLivePodium(podiumId = id)
                        findNavController().navigateSafe(action, options)
                    }
                    else if (!podium.isInvited) {
                        showFlashatDialog {
                            setMessage(getString(R.string.podium_error_private_not_invited))
                            setConfirmButtonVisible(visible = false)
                        }
                    }
                } else {
                    showFlashatDialog {
                        setMessage(getString(R.string.podium_error_not_live))
                        setConfirmButtonVisible(visible = false)
                    }
                }
            }
        }
        view.setContent {
            val messageState = promoBarViewModel.promoCycle.observeAsState()
            val message = messageState.value
            PromoBar(message, {
                promoBarViewModel.setDelay(it)
            }) {
                when(it) {
                    is PromoAnnouncement.AdminMessage -> {
                        MaterialAlertDialogBuilder(requireContext())
                            .setMessage(it.message)
                            .setPositiveButton(getText(R.string.common_close)) { dialog, _ ->
                                dialog.dismiss()
                            }.show()
                    }
                    is PromoAnnouncement.UserUpgrade -> {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(it.userId))
                    }
                    is PromoAnnouncement.TopLikedPodium -> toPodium(it.id)
                    is PromoAnnouncement.TopUserPodium -> toPodium(it.id)
                    is PromoAnnouncement.MaidanResult -> toPodium(it.podiumId)
                    is PromoAnnouncement.MostGenerousPodium -> toPodium(it.podiumId)
                    is PromoAnnouncement.CaseReported -> {
                        if (promoBarViewModel.user.premium) {
                            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalLegalAffairsFragment(defaultMainTab = LegalAffairsMainTab.InvestigationBureau))
                        } else {
                            freeUserPromoBarValidation {
                                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalUpgradePremiumFragment())
                            }
                            return@PromoBar
                        }
                    }
                    is PromoAnnouncement.StrongestTribe -> {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(it.managerId))
                    }
                    is PromoAnnouncement.MostGenerousUser -> {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(it.userId))
                    }
                    is PromoAnnouncement.AdminAnnouncementScheduled -> {
                        showAdminAnnouncementDialog(context = requireContext(), message = it.message)
                    }
                    is PromoAnnouncement.Birthday -> {
                        val userId = it.user.userId
                        if (userId != promoBarViewModel.user.id) {
                            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalBirthdayNotificationBottomSheetFragment(userId = userId, currentBirthday = true))
                        }
                    }
                    is PromoAnnouncement.SocialCases -> {
                        if (promoBarViewModel.user.premium) {
                            findNavController().navigateSafe(direction = NavGraphHomeDirections.actionGlobalSocialAffairActiveCasesFragment())
                        } else {
                            showSocialWelfareFreeUserAlert()
                        }
                    }
                }
            }
        }
    }

    fun showAdminAnnouncementDialog(context: Context, message: String?) {
        if (message.isNullOrEmpty()) return
        MaterialAlertDialogBuilder(context).setMessage(message).setPositiveButton(context.getText(R.string.common_close)) { dialog, _ ->
                dialog.dismiss()
            }.show()
    }
    
    private fun Fragment.freeUserPromoBarValidation(onConfirmButtonClick: () -> Unit){
            showFlashatDialog {
                setMessage(getString(R.string.promo_bar_free_user_action))
                setConfirmButton(R.string.common_upgrade, R.drawable.ic_promo_upgrade, false) {
                    onConfirmButtonClick()
                    true
                }
            }
    }
}