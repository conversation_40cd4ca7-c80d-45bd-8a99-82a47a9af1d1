package com.app.messej.ui.home.publictab.socialAffairs.donateBottomSheet

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.socialAffairs.SocialCaseInfo
import com.app.messej.data.model.api.socialAffairs.SocialDonateRequest
import com.app.messej.data.model.api.socialAffairs.SocialVoteErrorResponse
import com.app.messej.data.repository.SocialAffairsRepository
import com.app.messej.ui.composeComponents.ComposeTextFieldState
import com.google.gson.Gson
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class SocialDonateViewModel(application: Application) : AndroidViewModel(application = application) {

    private val repo = SocialAffairsRepository(context = application)
    val donateTextFieldState = ComposeTextFieldState()

    private val _caseDetail = MutableLiveData<SocialCaseInfo?>()
    val caseDetail : LiveData<SocialCaseInfo?> = _caseDetail

    private val _isSubmitting = MutableLiveData(false)
    val isSubmitting: LiveData<Boolean> = _isSubmitting

    val onError = MutableLiveData<String?>()
    val onSuccess = MutableLiveData<Boolean>()

    fun parseCase(json: String) {
        try {
            val item = Gson().fromJson(json, SocialCaseInfo::class.java)
            _caseDetail.postValue(item)
        } catch (error: Exception) {
            Log.d("SDV", "ParseError -> ${error.message}")
        }
    }

    val onDonateError = LiveEvent<SocialVoteErrorResponse.SocialError?>()
    fun donateCoins() {
        viewModelScope.launch(context = Dispatchers.IO) {
            _isSubmitting.postValue(true)
            val request = SocialDonateRequest(socialRequestId = _caseDetail.value?.id, amount = donateTextFieldState.text?.toIntOrNull())
            val response = repo.donateCoins(request = request)
            when(response) {
                is SocialAffairsRepository.SocialResultOf.Success -> {
                    onSuccess.postValue(true)
                }
                is SocialAffairsRepository.SocialResultOf.APIError -> {
                    if (response.code == 403 && response.error.result?.reason != null) {
                        onDonateError.postValue(response.error.result)
                    } else {
                        onError.postValue(response.error.message)
                    }
                }
                is SocialAffairsRepository.SocialResultOf.Error -> {
                    onError.postValue(response.exception.message)
                }
            }
            _isSubmitting.postValue(false)
        }
    }

}