package com.app.messej.ui.home.publictab.socialAffairs.caseInfo

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import com.app.messej.R
import com.app.messej.data.model.api.socialAffairs.SocialCaseInfo
import com.app.messej.data.model.enums.SocialVoteAction
import com.app.messej.ui.composeComponents.CustomLinearProgressIndicator
import com.app.messej.ui.composeComponents.CustomOutlinedButton
import com.app.messej.ui.composeComponents.ListErrorItemView
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomVerticalSpacer
import com.app.messej.ui.home.publictab.socialAffairs.components.SocialAffairCustomLargeButton
import com.app.messej.ui.home.publictab.socialAffairs.components.SocialLargeOpposeButton
import com.app.messej.ui.home.publictab.socialAffairs.components.SocialLargeSupportButton
import com.kennyc.view.MultiStateView

interface SocialCaseInfoListeners {
    fun onVote(action: SocialVoteAction, caseId: Int?)
    fun onArchive()
    fun onUnArchive()
    fun onDelete(caseInfo: SocialCaseInfo?)
    fun onViewVotersList()
    fun onViewAskedQuestions(caseId: Int)
    fun onDonate(case: SocialCaseInfo)
    fun onHoldCase()
    fun onUserDpClick(userId: Int)
}

@Composable
fun SocialCaseInfoComposeScreen(
    viewModel: SocialCaseInfoViewModel,
    listener: SocialCaseInfoListeners
) {
    val scrollState = rememberScrollState()
    val caseInfo by viewModel.caseInfo.observeAsState()
    val viewState by viewModel.viewState.observeAsState(initial = false)
    val isVoteSubmitting by viewModel.isVoteSubmitting.observeAsState(initial = false)
    val isPersonalCaseActionLoading by viewModel.isPersonalCaseActionLoading.observeAsState(initial = false)
    val isCaseApproving by viewModel.isApprovingCase.observeAsState(initial = false)

    if (viewState == MultiStateView.ViewState.LOADING) {
        Column(modifier = Modifier.fillMaxWidth()) {
            CustomLinearProgressIndicator(
                color = colorResource(id = R.color.colorSocialGreen)
            )
        }
        return
    }
    if (viewState == MultiStateView.ViewState.ERROR) {
        ListErrorItemView(
            onRetry = viewModel::getCaseInfoDetail
        )
        return
    }

    Column(
        modifier = Modifier
            .padding(horizontal = dimensionResource(id = R.dimen.activity_margin))
            .fillMaxSize()
    ) {
        Column(modifier = Modifier
            .weight(weight = 1F)
            .verticalScroll(state = scrollState)
            .padding(vertical = dimensionResource(id = R.dimen.activity_margin))
        ) {
            //User detail, donation, vote progress, details, proof files view
            SocialCaseInfoUserProofFileView(
                caseInfo = caseInfo?.data,
                user = viewModel.user,
                onUserDpClick = listener::onUserDpClick
            )
            // Vote Description View
            if (caseInfo?.data?.isVotingButtonsVisible(currentUserId = viewModel.user.id) == true) {
                CustomVerticalSpacer(
                    space = dimensionResource(id = R.dimen.activity_margin)
                )
                CaseInfoThumbsUpDownDescription()
            }

            // Upgrade Request View Detail
            if (caseInfo?.data?.isUpgradeRequest == true) {
                CustomVerticalSpacer(
                    space = dimensionResource(id = R.dimen.activity_margin)
                )
                CaseInfoUpgradeSupportView(
                    userStatus = caseInfo?.userStatus
                )
            }
            //Committee Members View Visible in donate view
            if (caseInfo?.data?.isCommitteeMembersListVisible == true) {
                caseInfo?.committeeMembers?.takeIf { it.isNotEmpty() }?.let { members ->
                    CustomVerticalSpacer(
                        space = dimensionResource(id = R.dimen.activity_margin)
                    )
                    CaseInfoCommitteeMembersView(
                        committeeMembers = members,
                        onUserDpClick = listener::onUserDpClick
                    )
                }
            }
            //Show Voters Count in Donate Case detail screens, committee Members, MSA, and President
            if (caseInfo?.data?.isVotersCountVisible(
                    currentUser = viewModel.user,
                    committeeMembersIdList = caseInfo?.committeeMembers?.map { it.userDetail?.id }
                ) == true) {
                CustomVerticalSpacer(
                    space = dimensionResource(id = R.dimen.activity_margin)
                )
                CaseInfoCardWithCount(
                    title = R.string.social_view_voters_list,
                    description = R.string.social_voters,
                    count = caseInfo?.voteCount,
                    onClick = listener::onViewVotersList
                )
            }
            //Show Questions Count in Donate Case detail screens
            if (caseInfo?.data?.isQuestionsVisible == true) {
                CustomVerticalSpacer(
                    space = dimensionResource(id = R.dimen.activity_margin)
                )
                CaseInfoCardWithCount(
                    title = R.string.social_view_asked_questions,
                    description = R.string.social_questions,
                    count = caseInfo?.questionsCount,
                    onClick = {
                        caseInfo?.data?.id?.let { listener.onViewAskedQuestions(caseId = it) }
                    }
                )
            }

            //Donate Button for "Active" status -> and "Personal case"
            if (caseInfo?.data?.isPersonalCaseDonateButtonVisible(currentUserId = viewModel.user.id) == true) {
                CustomVerticalSpacer(
                    space = dimensionResource(id = R.dimen.activity_margin)
                )
                SocialAffairCustomLargeButton(
                    text = R.string.social_affair_donate,
                    onClick = {
                        caseInfo?.data?.let { listener.onDonate(case = it) }
                    }
                )
            }

            //Approve and Hold Buttons for President user
            if (caseInfo?.data?.canApproveCase(userCitizenship = viewModel.user.citizenship) == true) {
                CustomVerticalSpacer(
                    space = dimensionResource(id = R.dimen.activity_margin)
                )
                //Approve Case -> Only for President
                SocialAffairCustomLargeButton(
                    text = R.string.social_case_approve,
                    isLoading = isCaseApproving,
                    onClick = { viewModel.approveCase() }
                )
                CustomVerticalSpacer(
                    space = dimensionResource(id = R.dimen.element_spacing)
                )
                //Hold Case -> Only for President
                SocialAffairCustomLargeButton(
                    text = R.string.social_case_hold,
                    isLoading = isCaseApproving,
                    buttonColor = colorResource(id = R.color.colorSocialRed),
                    onClick = { listener.onHoldCase() }
                )
            }
        }

        /*Bottom Action Buttons -> Below buttons are visible at the bottom of the screen*/
        //Voting Buttons at the bottom -> Visible to other users
        if (caseInfo?.data?.isVotingButtonsVisible(currentUserId = viewModel.user.id) == true) {
            Column(
                modifier = Modifier
                    .padding(vertical = dimensionResource(id = R.dimen.element_spacing))
                    .fillMaxWidth()
            ) {
                //Support Button
                SocialLargeSupportButton(
                    isLoading = isVoteSubmitting,
                    onClick = { listener.onVote(action = SocialVoteAction.Support, caseId = caseInfo?.data?.id) }
                )
                CustomVerticalSpacer(
                    space = dimensionResource(id = R.dimen.element_spacing)
                )
                //Oppose Button
                SocialLargeOpposeButton(
                    isLoading = isVoteSubmitting,
                    onClick = { listener.onVote(action = SocialVoteAction.Oppose, caseId = caseInfo?.data?.id) }
                )
            }
        }

        //Donate Button for "Upgrade case" in "Active" status
        if (caseInfo?.data?.isUpgradeCaseDonateButtonVisible(currentUserId = viewModel.user.id) == true) {
            SocialAffairCustomLargeButton(
                modifier = Modifier.padding(vertical = dimensionResource(id = R.dimen.element_spacing)),
                text = R.string.social_affair_donate,
                onClick = {
                    caseInfo?.data?.let { listener.onDonate(case = it) }
                }
            )
        }

        //Archive and delete buttons for own user view -> only for case "NEW" and "ARCHIVED" stats
        if (caseInfo?.data?.canDoSelfActionsOnCase(currentUserId = viewModel.user.id) == true) {
            Column(
                modifier = Modifier
                    .padding(vertical = dimensionResource(id = R.dimen.element_spacing))
                    .fillMaxWidth()
            ) {
                //Archive Button and Un Archive
                if (caseInfo?.data?.canArchive == true) {
                    SocialAffairCustomLargeButton(
                        text =  R.string.common_archive,
                        isLoading = isPersonalCaseActionLoading,
                        onClick = listener::onArchive
                    )
                }
                //Unarchive Button
                if (caseInfo?.data?.isArchived == true) {
                    SocialAffairCustomLargeButton(
                        text =  R.string.common_un_archive,
                        isLoading = isPersonalCaseActionLoading,
                        onClick = listener::onUnArchive
                    )
                }
                CustomVerticalSpacer(
                    space = dimensionResource(id = R.dimen.element_spacing)
                )
                //Delete Button
                if (caseInfo?.data?.canDelete == true) {
                    CustomOutlinedButton(
                        text = R.string.common_delete,
                        isEnabled = !isPersonalCaseActionLoading,
                        onClick = { listener.onDelete(caseInfo = caseInfo?.data) }
                    )
                }
            }
        }
    }
}