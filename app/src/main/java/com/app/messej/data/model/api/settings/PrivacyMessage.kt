package com.app.messej.data.model.api.settings


import androidx.room.TypeConverter
import com.app.messej.data.model.enums.PrivateChatPrivacy
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

data class PrivacyMessage(
    @SerializedName("disablePrivateChat")
    val disablePrivateChat: <PERSON>olean,
    @SerializedName("whoCanStartPrivateChat")
    val whoCanStartPrivateChat: StartPrivateChat?=null,
    @SerializedName("whoCanContinuePrivateChat")
    val whoCanContinuePrivateChat:StartPrivateChat?=null
){

    val whoCanStartPrivateChatEnum: PrivateChatPrivacy?
        get() = whoCanStartPrivateChat?.asEnum

    val whoCanContinuePrivateChatEnum: PrivateChatPrivacy?
        get() = whoCanContinuePrivateChat?.asEnum

    class Converter {
        @TypeConverter
        fun decode(data: String?): PrivacyMessage? {
            data?: return null
            val type: Type = object : TypeToken<PrivacyMessage?>() {}.type
            return Gson().fromJson<PrivacyMessage>(data, type)
        }
        @TypeConverter
        fun encode(someObjects: PrivacyMessage?): String? {
            return Gson().toJson(someObjects)
        }
    }
}