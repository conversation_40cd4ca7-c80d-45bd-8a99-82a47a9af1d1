package com.app.messej.data.model.api.settings

import androidx.room.TypeConverter
import com.app.messej.data.model.enums.PrivateChatPrivacy
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

data class StartPrivateChat(@SerializedName("ANYONE")
                                val anyOne: Boolean?=false,
                                @SerializedName("NO_ONE")
                                val noOne: Boolean?=false,
                                @SerializedName("ONLY_DEARS")
                                val onlyDears: Boolean?=false,
                                @SerializedName("ONLY_DEARS_FANS")
                                val onlyDearsFans: Boolean?=false,
                                @SerializedName("ONLY_PREMIUM")
                                val onlyPremium: Boolean?=false,
                                @SerializedName("ONLY_PREMIUM_WITH_100")
                                val onlyPremiumWithHundred: Boolean?=false,
                                @SerializedName("ONLY_USERS_WITH_100")
                                val onlyUsersWithHundred: Boolean?=false){

    class Converter {
        @TypeConverter
        fun decode(data: String?): StartPrivateChat? {
            data ?: return null
            val type: Type = object : TypeToken<StartPrivateChat?>() {}.type
            return Gson().fromJson<StartPrivateChat>(data, type)
        }

        @TypeConverter
        fun encode(someObjects: StartPrivateChat?): String? {
            return Gson().toJson(someObjects)
        }
    }

    val asEnum: PrivateChatPrivacy
        get() {
            return when {
                anyOne == true -> PrivateChatPrivacy.ANYONE
                noOne == true -> PrivateChatPrivacy.NO_ONE
                onlyDears == true -> PrivateChatPrivacy.ONLY_DEARS
                onlyDearsFans == true -> PrivateChatPrivacy.ONLY_DEARS_FANS
                onlyPremium == true -> PrivateChatPrivacy.ONLY_PREMIUM
                onlyPremiumWithHundred == true -> PrivateChatPrivacy.ONLY_PREMIUM_WITH_100
                onlyUsersWithHundred == true -> PrivateChatPrivacy.ONLY_USERS_WITH_100
                else -> PrivateChatPrivacy.ANYONE
            }
        }
}
