package com.app.messej.data.model.api.podium

import androidx.recyclerview.widget.DiffUtil
import androidx.room.TypeConverter
import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.UserRatingProvider
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.AssemblySpeakingStatus
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.ui.utils.DataFormatHelper
import com.app.messej.ui.utils.DataFormatHelper.numberToKWithFractions
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type
import java.time.Duration
import java.time.LocalDateTime
import java.time.ZonedDateTime

data class PodiumSpeaker(
    @SerializedName("id"         ) override val id          : Int,
    @SerializedName("name"       ) override val name        : String,
    @SerializedName("username"   ) override val username    : String?,
    @SerializedName("thumbnail"  ) override val thumbnail   : String?,
    @SerializedName("membership" ) override val membership  : UserType,
    @SerializedName("citizenship") override val citizenship : UserCitizenship?,
    @SerializedName("role"       )          var role        : Podium.PodiumUserRole? = null,
    @SerializedName("verified"   ) override val verified    : Boolean,
    @SerializedName("invited_to_be_admin" ) val invitedToBeAdmin:Boolean = false,
    @SerializedName("country_code")override val countryCode : String?  = null,
    @SerializedName("add_to_main_screen"  ) var shownOnMainScreen : Boolean = false,
    @SerializedName("add_to_stage"        ) val showOnStage : Boolean? = false,

    @SerializedName("mute", alternate = ["muted"]) var muted: Boolean = true,
    @SerializedName("likes"      )          var likes       : Int = 0,
    @SerializedName("is_online"  )          var online       : Boolean? = false,
    @SerializedName("enable_camera")        var allowVideoInPodiums       : Boolean? = false,
    @SerializedName("camera_violation")        var cameraViolation       : Boolean? = false,
    @SerializedName("camera_expiry")        var cameraExpiry       : String? = null,

    @SerializedName("coins_received" )      val coinsReceived: Double? = null,
    @SerializedName("coins_sent" )      val coinSent: Double? = null,
    @SerializedName("speaking_order" )      val speakingOrder: Int? = null,
    @SerializedName("speaking_status" )      var speakingStatus: AssemblySpeakingStatus? = null,
    @SerializedName("speaking_time_limit" )      var speakingTimeLimit: Int? = null,
    @SerializedName("speaking_start_time" )      var speakingStartTime: Double? = null,
    @SerializedName("spoken_time" )      val spokenTime: Double? = null,
    @SerializedName("original_status" )      val originalStatus: AssemblySpeakingStatus? = null,

    // FOR THEATER PODIUM
    @SerializedName("podium_blocked"          ) var blockedFromPodium  : Boolean? = false,
    @SerializedName("audience_blocked"      ) var blockedFromAudience  : Boolean? = false,
    @SerializedName("stage_blocked"         ) var blockedFromStage  : Boolean? = false,
    @SerializedName("user_likes"            ) var likesGivenByUser  : Int? = 0,
    @SerializedName("user_rating"            ) override var userRating  : Double? = 0.0,
    @SerializedName("user_tribe_name"       ) val tribeName: String?=null,
    @SerializedName("report_pay_fine"   ) val reportPayFine   : Boolean? = false,

): AbstractUser(), UserRatingProvider {

    companion object {
        fun from(user: AbstractUser): PodiumSpeaker {
            return PodiumSpeaker(
                id = user.id,
                name = user.name,
                username = user.username,
                thumbnail = user.thumbnail,
                membership = user.membership,
                citizenship = user.citizenship?:UserCitizenship.CITIZEN,
                verified = user.verified
            )
        }

        fun getDiffer() = object : DiffUtil.ItemCallback<PodiumSpeaker>() {
            override fun areItemsTheSame(oldItem: PodiumSpeaker, newItem: PodiumSpeaker): Boolean {
                return oldItem.id == newItem.id
            }

            override fun areContentsTheSame(oldItem: PodiumSpeaker, newItem: PodiumSpeaker): Boolean {
                return oldItem == newItem
            }
        }
    }

    class Converter {

        @TypeConverter
        fun decode(data: String?): PodiumSpeaker? {
            data?: return null
            val type: Type = object : TypeToken<PodiumSpeaker?>() {}.type
            return Gson().fromJson<PodiumSpeaker>(data, type)
        }

        @TypeConverter
        fun encode(someObjects: PodiumSpeaker?): String? {
            return Gson().toJson(someObjects)
        }

        @TypeConverter
        fun decodeList(data: String?): List<PodiumSpeaker>? {
            data?: return null
            val type: Type = object : TypeToken<List<PodiumSpeaker>?>() {}.type
            return Gson().fromJson<List<PodiumSpeaker>>(data, type)
        }

        @TypeConverter
        fun encodeList(someObjects: List<PodiumSpeaker>?): String? {
            return Gson().toJson(someObjects)
        }
    }

    val likesFormatted : String
        get() = likes.numberToKWithFractions()

    val coinsReceivedFormatted : String
        get() = coinsReceived?.toInt()?.let { DataFormatHelper.numberToK(it) } ?: ""

    val coinsSentFormated: String
        get() = coinSent?.toInt()?.numberToKWithFractions() ?: "0"

    val isManager : Boolean
        get() = role == Podium.PodiumUserRole.MANAGER

    val parsedSpeakingStartTime: ZonedDateTime?
        get() {
            val timeInMilliSeconds = speakingStartTime?.times(1000L)?.toLong()
            return DateTimeUtils.parseMillisToDateTime(timeInMilliSeconds?:0L)
        }

    val speakingTimeRemaining: Long
        get() {
            return ((((speakingTimeLimit?:0) - (spokenTime?:0.0))*1000L).toLong()) - ((DateTimeUtils.durationToNowFromPast(parsedSpeakingStartTime))?: Duration.ZERO).toMillis()
        }

    val violation: Boolean
        get() = cameraViolation ?: false

    val parsedExpiryTime: LocalDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(cameraExpiry)?.run { DateTimeUtils.getLocalDateTime(this) }
}
