package com.app.messej.data.model.api.podium

import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.entity.Podium
import com.google.gson.annotations.SerializedName

data class PodiumBirthdayTopGiftersEventResponse(
    @SerializedName("result") val result: List<Podium.TopGifters>?,
    @SerializedName("chat_event_data") val birthdayChatGiftData: BirthdayGiftChatData?
) {
    data class BirthdayGiftChatData(
        @SerializedName("podium_id") val podiumId: String,
        @SerializedName("coins") val coins: Int,
        @SerializedName("total_coins") val totalCoins: Int,
        @SerializedName("sender") val sender: SenderDetails
    )
}