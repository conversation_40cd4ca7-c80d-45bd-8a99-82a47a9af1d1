package com.app.messej.data.room

import androidx.room.DeleteColumn
import androidx.room.migration.AutoMigrationSpec
import androidx.room.migration.Migration

object DatabaseMigrations {

    val MIGRATIONS: Array<Migration>
        get() = arrayOf(
//            object : Migration(3, 4) {
//                override fun migrate(database: SupportSQLiteDatabase) {
//                    database.execSQL("UPDATE fls_huddles SET role = UPPER(role)")
//                }
//            }
        )

//    @DeleteTable(tableName = "fls_account_verification")
//    class DeleteAccountVerificationTable: AutoMigrationSpec
//

    @DeleteColumn.Entries(
        DeleteColumn(
            tableName = "fls_podium",
            columnName = "type"
        )
    )
    class DeletePodiumTypeEnumColumn: AutoMigrationSpec

    @DeleteColumn.Entries(
        DeleteColumn(
            tableName = "fls_yalla_live",
            columnName = "is_participant"
        )
    )
    class DeleteYallaParticipantColumn: AutoMigrationSpec

}
