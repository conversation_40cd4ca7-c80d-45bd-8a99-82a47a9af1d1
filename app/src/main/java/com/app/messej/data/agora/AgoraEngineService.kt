package com.app.messej.data.agora

import android.content.Context
import android.util.Log
import android.view.SurfaceView
import com.app.messej.BuildConfig
import com.app.messej.data.utils.BaseMultiListener
import com.google.gson.Gson
import io.agora.rtc2.ChannelMediaOptions
import io.agora.rtc2.Constants
import io.agora.rtc2.IRtcEngineEventHandler
import io.agora.rtc2.RtcConnection
import io.agora.rtc2.RtcEngine
import io.agora.rtc2.RtcEngineConfig
import io.agora.rtc2.video.VideoCanvas
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.runBlocking

object AgoraEngineService: IRtcEngineEventHandler() {

    private const val APP_ID = BuildConfig.AGORA_APP_ID

    private var _activeSession: AgoraSessionImpl? = null

    val activeSession: AgoraSession?
        get() = _activeSession

    private fun setupAgoraEngine(c: Context): RtcEngine? {
        return try {
            // Set the engine configuration
            val config = RtcEngineConfig().apply {
                mContext = c.applicationContext
                mAppId = APP_ID
                // Assign an event handler to receive engine callbacks
                mEventHandler = this@AgoraEngineService
            }
            // Create an RtcEngine instance
            RtcEngine.create(config)
        } catch (e: Exception) {
            null
        }
    }

    // Listen for a remote user joining the channel.
    override fun onUserJoined(uid: Int, elapsed: Int) {
        Log.w("AGREGS", "onUserJoined: $uid")
    }

    override fun onAudioVolumeIndication(speakers: Array<out AudioVolumeInfo>?, totalVolume: Int) = runBlocking {
        val list = speakers.orEmpty().map {
            VolumeInfo(it.uid,it.volume)
        }.filter { it.volume>42 }
//        Log.w("AgoraEngineService", "onAudioVolumeIndication: $list", )
        _volumeInfoFlow.emit(list)
    }

    override fun onJoinChannelSuccess(channel: String, uid: Int, elapsed: Int) {
        Log.w("AGREGS", "onJoinChannelSuccess: $channel")
        if (_activeSession?.channelName!= channel) return
        _activeSession?.onAllListeners {
            onJoinedChannel(elapsed)
        }
    }

    override fun onRemoteVideoStateChanged(uid: Int, state: Int, reason: Int, elapsed: Int) {
        Log.w("AGREGS", "onRemoteVideoStateChanged: uid $uid | state $state | reason $reason")
        val vs = when(state) {
            Constants.REMOTE_VIDEO_STATE_STARTING -> VideoState.ENABLED
            Constants.REMOTE_VIDEO_STATE_DECODING -> VideoState.PLAYING
            Constants.REMOTE_VIDEO_STATE_FROZEN -> VideoState.PLAYING
            else -> VideoState.DISABLED
        }
        Log.w("AGREGS", "onRemoteVideoStateChanged: uid $uid | videoState $vs")
        _activeSession?.onAllListeners {
            onVideoStateChanged(uid,vs)
        }
    }

    override fun onLocalVideoStateChanged(source: Constants.VideoSourceType?, state: Int, error: Int) {
        Log.w("AGREGS", "onLocalVideoStateChanged: state $state | error $error")
    }

    override fun onRemoteVideoStats(stats: RemoteVideoStats?) {
        Log.w("AGREGS", "onRemoteVideoStats: ${Gson().toJson(stats)}")
    }

    override fun onLeaveChannel(stats: RtcStats?) {
        Log.w("AGREGS", "onLeaveChannel")
        _activeSession?.onAllListeners {
            onLeave()
        }
        _activeSession?.removeAllListeners()
        _activeSession = null
    }

    override fun onUserOffline(uid: Int, reason: Int) {
    }

    override fun onError(err: Int) {
//            when (err) {
//                ErrorCode.ERR_TOKEN_EXPIRED -> sendMessage("Your token has expired")
//                ErrorCode.ERR_INVALID_TOKEN -> sendMessage("Your token is invalid")
//                else -> sendMessage("Error code: $err")
//            }
        _activeSession?.onAllListeners {
            onError(err)
        }
    }

    override fun onConnectionStateChanged(state: Int, reason: Int) {
        _activeSession?.onAllListeners {
            val stateStr = when (state) {
                0 -> "CONNECTION_STATE_NOT_INITIALIZED"
                1 -> "CONNECTION_STATE_DISCONNECTED"
                2 -> "CONNECTION_STATE_CONNECTING"
                3 -> "CONNECTION_STATE_CONNECTED"
                4 -> "CONNECTION_STATE_RECONNECTING"
                5 -> "CONNECTION_STATE_FAILED"
                else -> state.toString()
            }

            val reasonStr = when (reason) {
                0 -> "CONNECTION_CHANGED_CONNECTING"
                1 -> "CONNECTION_CHANGED_JOIN_SUCCESS"
                2 -> "CONNECTION_CHANGED_INTERRUPTED"
                3 -> "CONNECTION_CHANGED_BANNED_BY_SERVER"
                4 -> "CONNECTION_CHANGED_JOIN_FAILED"
                5 -> "CONNECTION_CHANGED_LEAVE_CHANNEL"
                6 -> "CONNECTION_CHANGED_INVALID_APP_ID"
                7 -> "CONNECTION_CHANGED_INVALID_CHANNEL_NAME"
                8 -> "CONNECTION_CHANGED_INVALID_TOKEN"
                9 -> "CONNECTION_CHANGED_TOKEN_EXPIRED"
                10 -> "CONNECTION_CHANGED_REJECTED_BY_SERVER"
                11 -> "CONNECTION_CHANGED_SETTING_PROXY_SERVER"
                12 -> "CONNECTION_CHANGED_RENEW_TOKEN"
                13 -> "CONNECTION_CHANGED_CLIENT_IP_ADDRESS_CHANGED"
                14 -> "CONNECTION_CHANGED_KEEP_ALIVE_TIMEOUT"
                15 -> "CONNECTION_CHANGED_REJOIN_SUCCESS"
                16 -> "CONNECTION_CHANGED_LOST"
                17 -> "CONNECTION_CHANGED_ECHO_TEST"
                18 -> "CONNECTION_CHANGED_CLIENT_IP_ADDRESS_CHANGED_BY_USER"
                19 -> "CONNECTION_CHANGED_SAME_UID_LOGIN"
                20 -> "CONNECTION_CHANGED_TOO_MANY_BROADCASTERS"
                21 -> "CONNECTION_CHANGED_LICENSE_VALIDATION_FAILURE"
                22 -> "CONNECTION_CHANGED_CERTIFICATION_VERYFY_FAILURE"
                else -> reason.toString()
            }
            this.onConnectionStateChanged(stateStr,reasonStr)
        }
    }

    data class VolumeInfo(
        val userId: Int,
        val volume: Int
    )

    interface ChannelEventListener {
        fun onJoinedChannel(elapsed: Int)

        fun onLeaveTriggered(stack: String) {}

        fun onLeave()

        fun onVideoStateChanged(id: Int, state: VideoState) {}

        fun onError(code: Int) {}

        fun onConnectionStateChanged(state: String, reason: String) {}
    }

    enum class VideoState {
        ENABLED,
        PLAYING,
        DISABLED
    }

    private val _volumeInfoFlow: MutableSharedFlow<List<VolumeInfo>> = MutableSharedFlow()
    val volumeInfoFlow: SharedFlow<List<VolumeInfo>> = _volumeInfoFlow

    abstract class AgoraSession: BaseMultiListener<ChannelEventListener>() {
        abstract val channelName: String
        abstract val localUid: Int
        abstract val multiVideo: Boolean

        var localVideoMode: Boolean = false
            protected set

        val podiumId: String
            get() = channelName

        abstract val valid: Boolean

        abstract fun leave()

        abstract fun connected(): Boolean

        abstract fun connectionState(): RtcConnection.CONNECTION_STATE_TYPE?
        abstract fun connectionStateInt(): Int?

        abstract fun muteAudio(muted: Boolean)
        abstract fun muteAllAudio(muted: Boolean)
        abstract fun switchCamera()
        abstract fun setAsSpeaker(canSpeak: Boolean)
        abstract fun attachLocalVideoStream(surface: SurfaceView)
        abstract fun detachLocalVideoStream()

        abstract fun attachRemoteVideoStream(id: Int, surface: SurfaceView)
    }

    private data class AgoraSessionImpl(
        override val channelName: String,
        override val localUid: Int,
        override val multiVideo: Boolean = false,
        val token: String,
        var agoraEngine: RtcEngine?
    ): AgoraSession() {

        override val valid: Boolean
            get() = agoraEngine!=null

        override fun muteAudio(muted: Boolean) {
            Log.w("AGREGS", "toggleMute: $muted")
            agoraEngine?.muteLocalAudioStream(muted)
        }

        override fun muteAllAudio(muted: Boolean) {
            Log.w("AGREGS", "toggleMute: $muted")
            agoraEngine?.muteAllRemoteAudioStreams(muted)
        }

        override fun switchCamera() {
            agoraEngine?.switchCamera()
        }

        override fun setAsSpeaker(canSpeak: Boolean) {
            Log.w("AGREGS", "setAsSpeaker: $canSpeak")
            agoraEngine?.setClientRole(if(canSpeak) Constants.CLIENT_ROLE_BROADCASTER else Constants.CLIENT_ROLE_AUDIENCE)
        }

        private fun resetVideoSurface() {
            agoraEngine?.apply {
                setupLocalVideo(null)
                setupRemoteVideo(null)
            }
        }

        override fun attachLocalVideoStream(surface: SurfaceView) {
            Log.w("AGREGS", "attachLocalVideoStream")
            localVideoMode = true
            agoraEngine?.apply {

                if (!multiVideo) {
                    resetVideoSurface()
                }
                enableLocalVideo(true)
                setLocalRenderMode(
                    Constants.RENDER_MODE_HIDDEN,
                    Constants.VIDEO_MIRROR_MODE_DISABLED
                )
                enableLocalVideo(true)
                setupLocalVideo(
                    VideoCanvas(surface,VideoCanvas.RENDER_MODE_HIDDEN,0)
                )
                startPreview()
            }
        }

        override fun detachLocalVideoStream() {
            Log.w("AGREGS", "detachLocalVideoStream: multi: $multiVideo")
            localVideoMode = false
            agoraEngine?.apply {
                if (!multiVideo) {
                    resetVideoSurface()
                } else {
                    setupLocalVideo(null)
                }
                stopPreview()
                enableLocalVideo(false)
//                disableVideo()
            }
        }

        override fun attachRemoteVideoStream(id: Int, surface: SurfaceView) {
            if (localVideoMode && !multiVideo) {
                detachLocalVideoStream()
                localVideoMode = false
            }
            Log.w("AGREGS", "attachRemoteVideoStream: $id")
            // Create a VideoCanvas using the remoteSurfaceView
            val videoCanvas = VideoCanvas(
                surface,
                VideoCanvas.RENDER_MODE_HIDDEN, id
            )
            agoraEngine?.apply {
//                muteRemoteVideoStream(id,false)
                if(!multiVideo) {
                    resetVideoSurface()
                }
                setupRemoteVideo(videoCanvas)
            }
        }

        override fun connected(): Boolean {
            return agoraEngine?.connectionState == RtcConnection.CONNECTION_STATE_TYPE.CONNECTION_STATE_CONNECTED.ordinal
        }

        override fun connectionStateInt(): Int? {
            return agoraEngine?.connectionState
        }

        override fun connectionState(): RtcConnection.CONNECTION_STATE_TYPE? {
            val st = connectionStateInt()?: return null
            return RtcConnection.CONNECTION_STATE_TYPE.entries[st]
        }

        override fun leave() {
            val stackTraceElements = Thread.currentThread().stackTrace
            onAllListeners {
                onLeaveTriggered(stackTraceElements.toString())
            }
            agoraEngine?.let { engine ->
                agoraEngine = null
                engine.leaveChannel()
                engine.disableVideo()
                // Release the RtcEngine instance to free up resources
                RtcEngine.destroy()
                clearSession()
            }
        }

        fun onAllListeners(onOne: ChannelEventListener.() -> Unit) {
            _activeSession?.listeners?.forEach {
                it.onOne()
            }
        }
    }

    private fun clearSession() {
        _activeSession = null
    }

    fun createSession(c: Context, localUid: Int, channelName: String, token: String, listener: ChannelEventListener, multiVideo: Boolean = false): AgoraSession? {
        _activeSession?.leave()
        _activeSession = null

        val engine = setupAgoraEngine(c)?: return null

        val options = ChannelMediaOptions()
        // For a Video/Voice call, set the channel profile as COMMUNICATION.
        options.channelProfile = Constants.CHANNEL_PROFILE_COMMUNICATION
        // Set the client role to broadcaster or audience
        options.clientRoleType = Constants.CLIENT_ROLE_AUDIENCE

        engine.enableAudioVolumeIndication(500,3,true)
        engine.setAINSMode(true,0)
        engine.enableVideo()
        engine.enableLocalVideo(false)

        // Join the channel using a token.
        engine.joinChannel(token, channelName, localUid, options)

        val session = AgoraSessionImpl(
            channelName = channelName,
            localUid = localUid,
            multiVideo = multiVideo,
            token = token,
            agoraEngine = engine
        ).apply {
            registerListener(listener)
        }
        _activeSession = session
        return session
    }
}