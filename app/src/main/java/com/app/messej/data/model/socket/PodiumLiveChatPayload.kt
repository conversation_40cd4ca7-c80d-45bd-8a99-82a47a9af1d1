package com.app.messej.data.model.socket

import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.api.podium.UserStats
import com.app.messej.data.model.enums.PodiumLiveChatType
import com.google.gson.annotations.SerializedName

data class PodiumLiveChatPayload(
//    @SerializedName("eventName"     ) var eventName    : String = SocketEvent.RX_TX_PODIUM_CHAT.key,
    @SerializedName("podium_id"     ) val podiumId     : String,
    @SerializedName("chat_id"       ) val chatId       : String,
    @SerializedName("user_stats"    ) var userStats: UserStats? = null,
    @SerializedName("message"       ) val message      : String,
    @SerializedName("created"       ) val created      : String,
    @SerializedName("sender_detail" ) val senderDetails: SenderDetails,
    @SerializedName("user_id"       ) val userId       : Int,
    @SerializedName("country_code"  ) val countryCode  : String?,
    @SerializedName("chat_type"     ) private val _chatType     : PodiumLiveChatType? = PodiumLiveChatType.NORMAL,
    @SerializedName("user_tribe_name"   ) val userTribeName   : String? = null,
    @SerializedName("report_pay_fine"   ) val reportPayFine   : Boolean? = false

    ): SocketEventPayload() {

        val chatType: PodiumLiveChatType
            get() = _chatType ?: PodiumLiveChatType.NORMAL
    }
