package com.app.messej.data.model.api.promo


import com.app.messej.data.model.UserIdAndName
import com.app.messej.data.model.socket.PromoAnnouncement
import com.google.gson.annotations.SerializedName

data class PromoHistoryResponse(
    @SerializedName("top_user_podium"           ) val topUser           : PromoHistoryItem<PromoAnnouncement.TopUserPodium>?       = null,
    @SerializedName("top_liked_podiums"         ) val topLiked          : PromoHistoryItem<PromoAnnouncement.TopLikedPodium>?      = null,
    @SerializedName("maidan_result"             ) val maidan            : PromoHistoryItem<PromoAnnouncement.MaidanResult>?        = null,
    @SerializedName("admin_announcement"        ) val admin             : PromoHistoryItem<PromoAnnouncement.AdminMessage>?        = null,
    @SerializedName("user_citizenship_upgrade"  ) val userUpgrade       : PromoHistoryItem<PromoAnnouncement.UserUpgrade>?         = null,
    @SerializedName("legal_cases"               ) val caseReported      : PromoHistoryItem<PromoAnnouncement.CaseReported>?         = null,
    @SerializedName("strongest_tribe"           ) val strongestTribe    : PromoHistoryItem<PromoAnnouncement.StrongestTribe>?      = null,
    @SerializedName("most_generous_user"        ) val mostGenerousUser  : PromoHistoryItem<PromoAnnouncement.MostGenerousUser>?    = null,
    @SerializedName("top_gifts_podium"          ) val mostGenerousPodium: PromoHistoryItem<PromoAnnouncement.MostGenerousPodium>?    = null,
    @SerializedName("admin_announcement_scheduled") val adminAnnouncementScheduled: List<PromoHistoryItem<PromoAnnouncement.AdminAnnouncementScheduled>?>? = null,
    @SerializedName("birthdays_today"           ) val birthdaysToday    : BirthdaysHolder? = null,
    @SerializedName("social_cases"              ) val socialCase    : PromoHistoryItem<PromoAnnouncement.SocialCases>? = null,
) {
    data class PromoHistoryItem<T: PromoAnnouncement> (
        @SerializedName("type"    ) val type: PromoAnnouncement.AnnouncementType,
        @SerializedName("content" ) val content: T?
    )

    data class BirthdaysHolder(
        @SerializedName("type"    ) val type: PromoAnnouncement.AnnouncementType,
        @SerializedName("content" ) val content: Birthdays?
    )

    data class Birthdays(
        @SerializedName("minister") val ministers: List<UserIdAndName>?,
        @SerializedName("president") val president: UserIdAndName?
    )

    val flatList: List<PromoAnnouncement>
        get() {
            val list = mutableListOf<PromoAnnouncement>()
            topUser?.content?.let { list.add(it) }
            topLiked?.content?.let { list.add(it) }
            admin?.content?.let {
                if (!it.message.isNullOrBlank())list.add(it)
            }
            userUpgrade?.content?.let { list.add(it) }
            caseReported?.content?.let { list.add(it) }
            strongestTribe?.content?.let { list.add(it) }
            mostGenerousUser?.content?.let { list.add(it) }
            mostGenerousPodium?.content?.let { list.add(it) }
            adminAnnouncementScheduled?.mapNotNull { it?.content }?.let {
                list.addAll(it)
            }

            // Handle birthday announcements
            birthdaysToday?.content?.let { bd ->
                // Add president birthday if present
                bd.president?.let { president ->
                    list.add(PromoAnnouncement.Birthday(
                        userType = PromoAnnouncement.Birthday.BirthdayType.PRESIDENT,
                        user = president
                    ))
                }

                // Add each minister birthday separately
                bd.ministers?.forEach { minister ->
                    list.add(PromoAnnouncement.Birthday(
                        userType = PromoAnnouncement.Birthday.BirthdayType.MINISTER,
                        user = minister
                    ))
                }
            }

            socialCase?.content?.let { list.add(it) }

            return list
        }
}