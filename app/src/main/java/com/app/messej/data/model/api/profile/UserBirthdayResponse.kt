package com.app.messej.data.model.api.profile

import com.app.messej.data.model.socket.AdminNotificationPayload
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.DateTimeUtils.isUserBirthdayToday
import com.google.gson.annotations.SerializedName
import java.time.LocalDate


data class UserBirthdayResponse(
    @SerializedName("birthday_animation_url_android", alternate = ["animation_url_android"]) val birthdayAnimationUrlAndroid: String?=null,
    @SerializedName("birthday_animation_url_ios"        ) val birthdayAnimationUrlIos: String?=null,
    @SerializedName("is_current_user_bday"              ) val isCurrentUserBday: Boolean?=null,
    @SerializedName("other_user_birthdays"              ) val otherUserBirthdays: List<BirthdayUser>?= listOf(),
    @SerializedName("userId"                            ) val userId: Int?=null,
    @SerializedName("animation_for_president"           ) val animationForPresident:String?=null,
    @SerializedName("current_president_name"            ) val currentPresidentName: String?=null,
    @SerializedName("current_president_id"              ) val currentPresidentId: Int?=null,
    @SerializedName("upgrade_support_violation"         ) val isUpgradeSupportViolationDetected: Boolean?=false,
    @SerializedName("upgraded_users"                    ) val upgradedUsersList : List<Int>?= mutableListOf(),
    @SerializedName("dob"                               ) val dateOfBirth: String? = null,
    @SerializedName("admin_notification_popups"         ) val adminNotificationPopups: List<AdminNotificationPayload>?= null
){
    val hasVideo: Boolean
        get() = birthdayAnimationUrlAndroid!=null

    val isCurrentUserBirthday: Boolean
        get() = isUserBirthdayToday(dob = dateOfBirth)

}
data  class BirthdayUser(
    @SerializedName("name"                          ) val name: String,
    @SerializedName("user_id"                       ) val userId: Int?=null,
    @SerializedName("gender"                        ) val gender: String?=null,
    @SerializedName("date_of_birth"                 ) val dateOfBirth: String?=null,
    @SerializedName("profile_photo"                 ) val profilePhoto: String?=null,
    @SerializedName("thumbnail"                     ) val thumbnail: String?=null,
    @SerializedName("verified"                      )val verified: Boolean?=null,
    @SerializedName("birthday_animation_url_android", alternate = ["animation_url_android"]    )  val birthdayAnimationUrlAndroid: String?=null,

    ){
    val hasVideo: Boolean
        get() = birthdayAnimationUrlAndroid!=null

    val parsedDateOfBirth: LocalDate?
        get() = DateTimeUtils.parseDate(dateOfBirth)

}