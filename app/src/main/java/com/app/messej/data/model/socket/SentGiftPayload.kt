package com.app.messej.data.model.socket

import android.os.Parcelable
import com.app.messej.MainApplication
import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.enums.GiftType
import com.app.messej.data.repository.AccountRepository
import com.app.messej.ui.utils.DataFormatHelper.numberToKWithFractions
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize


@Parcelize
data class SentGiftPayload(
    @SerializedName("gift_Id", alternate = ["id"]) override val id: Int,
    @SerializedName("gift_identifier") override val giftIdentifier: String?,
    @SerializedName("gift_url") override val animationUrl: String?,
    @SerializedName("gift_static_url") override val thumbnail: String?,
    @SerializedName("coins") override val coins: Int?,
    @SerializedName("flix") override val flix: Int?,
    @SerializedName("coins_received", alternate = ["recipient_received_coins"]) val coinsReceived: Double? = null,
    @SerializedName("gift_name") override val giftName: String?,
    @SerializedName("gift_description") override val description: String?,
    @SerializedName("gift_type") override val giftType: GiftType?,
    @SerializedName("gift_animation_url") override val giftAnimationUrl: String?,
    @SerializedName("gift_animation_url_android") override val giftAnimationUrlAndroid: String?,

    @SerializedName("sender_id") val senderId: Int?,
    @SerializedName("sender_name") val senderName: String?=null,
    @SerializedName(value = "receiver_id", alternate = ["userId"]) val receiverId: Int?,
    @SerializedName("receiver_name") val receiverName: String?=null,
    @SerializedName("receiver_image") val receiverImage: String?=null,

    @SerializedName("translations") override val nameTranslations: Translations?=null,
    @SerializedName("description_translations") override val descTranslations: Translations?=null,

    @SerializedName("podium_id") val podiumId: String? = null,
    @SerializedName("challenge_id") val challengeId: String? = null,
    @SerializedName("special_occasion_date")        override val specialOccasionDate        : String?=null,
    @SerializedName("manager_id")  override val managerId: Int? = null,
    @SerializedName("manager_received_coins") override val managerReceivedCoins: Double? = null,
    @SerializedName("category_name")  override val categoryName: String?,
    @SerializedName("category_id")   override val categoryId:Int?

    ) : AbstractGiftItem(), Parcelable {

    companion object {
        fun from(base: AbstractGiftItem, from: AbstractUser, to: AbstractUser, podium: String? = null, coinsReceived: Double? = null,managerId:Int?=null,managerReceivedCoins: Double?=null): SentGiftPayload {
            return SentGiftPayload(
                id = base.id,
                giftIdentifier = base.giftIdentifier,
                animationUrl = base.animationUrl,
                thumbnail = base.thumbnail,
                coins = base.coins,
                flix = base.flix,
                giftName = base.giftName,
                description = base.description,
                giftType = base.giftType,
                giftAnimationUrl = base.giftAnimationUrl,
                giftAnimationUrlAndroid = base.giftAnimationUrlAndroid,
                nameTranslations = base.nameTranslations,
                descTranslations = base.descTranslations,

                senderId = from.id,
                senderName = from.name,

                receiverId = to.id,
                receiverName = to.name,
                receiverImage = to.thumbnail,

                podiumId = podium,
                coinsReceived = coinsReceived,
                specialOccasionDate = base.specialOccasionDate,
                managerId = managerId,
                managerReceivedCoins = managerReceivedCoins,
                categoryName = base.categoryName,
                categoryId = base.categoryId
            )
        }
        const val COINS ="COiNS"
        const val FLIX = "FLiX"
    }

    val isReceived: Boolean
        get() = AccountRepository(MainApplication.applicationContext()).user.id == receiverId

    val isSent: Boolean
        get() = AccountRepository(MainApplication.applicationContext()).user.id == senderId

    val isInvolved: Boolean
        get() = isReceived || isSent

    val giftCoinFormatted: String
        get() = (coins?:0).numberToKWithFractions()

    val hasValidAnimationUrl: Boolean
        get() = !giftAnimationUrlAndroid.isNullOrBlank()

    val isVisible: Boolean
        get() = (coins?:0) >= 5 || (flix?:0) >= 1

    val isFlix: Boolean
    get() = categoryName == "FLiX"

}
