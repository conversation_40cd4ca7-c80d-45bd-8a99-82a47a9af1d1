package com.app.messej.data.model.entity

import android.text.Spanned
import androidx.core.text.HtmlCompat
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.ZonedDateTime

@Entity(tableName = EntityDescriptions.TABLE_NOTIFICATIONS)
@TypeConverters(NotificationAssociate.Converter::class)
data class Notification(
    @PrimaryKey @SerializedName("id"    ) @ColumnInfo(name = "id"                ) val id: Int,
    @SerializedName("action"            ) @ColumnInfo(name = "action"            ) val action: NotificationAction? = null,
    @SerializedName("associate_obj_id"  ) @ColumnInfo(name = "associateObjId"    ) val associateObjId: Int? = null,
    @SerializedName("body"              ) @ColumnInfo(name = "body"              ) val body: String? = null,
    @SerializedName("category"          ) @ColumnInfo(name = "category"          ) val category: String? = null,
    @SerializedName("delivered_time"    ) @ColumnInfo(name = "delivered_time"    ) val deliveredTime: String? = null,
    @SerializedName("highlight_text"    ) @ColumnInfo(name = "highlightText"     ) val highlightText: String? = null,
    @SerializedName("icon_path"         ) @ColumnInfo(name = "iconPath"          ) val iconPath: String? = null,
    @SerializedName("is_deleted"        ) @ColumnInfo(name = "isDeleted"         ) val isDeleted: Boolean? = null,
    @SerializedName("is_read"           ) @ColumnInfo(name = "readTime"          ) val readTime: String? = null,
    @SerializedName("is_viewed"         ) @ColumnInfo(name = "isViewed"          ) val isViewed: String? = null,
    @SerializedName("normal_text"       ) @ColumnInfo(name = "normalText"        ) val normalText: String? = null,
    @SerializedName("receiver_id"       ) @ColumnInfo(name = "receiverId"        ) val receiverId: Int? = null,
    @SerializedName("sender_id"         ) @ColumnInfo(name = "senderId"          ) val senderId: Int? = null,
    @SerializedName("status"            ) @ColumnInfo(name = "status"            ) val status: NotificationStatus? = null,
    @SerializedName("time_created"      ) @ColumnInfo(name = COLUMN_CREATED_TIME ) val timeCreated: String? = null,
    @SerializedName("time_updated"      ) @ColumnInfo(name = "timeUpdated"       ) val timeUpdated: String? = null,
    @SerializedName("title"             ) @ColumnInfo(name = "title"             ) val title: String? = null,
    @SerializedName("is_private"        ) @ColumnInfo(name = "is_private"        ) val isPrivate: Boolean? = null,
    @SerializedName("associate_data"        ) @ColumnInfo(name = "associate_data"        ) val assosiateData: NotificationAssociate? = null
) {

    companion object {
        const val COLUMN_CREATED_TIME = "timeCreated"
    }

    enum class NotificationAction {
        @SerializedName("user_level_upgrade") USER_LEVEL_UPGRADE,
        @SerializedName("user_citizenship_upgrade") CITIZENSHIP_LEVEL_UPGRADE,
        @SerializedName("delete_huddle")HUDDLE_DELETED,
        @SerializedName("forceful_username_change")FORCEFUL_USER_NAME_CHANGE,
        @SerializedName("suggest_new_username")SUGGEST_USERNAME,
        @SerializedName("user_profile_edited") USER_PROFILE_EDITED,
        @SerializedName("invite_participant")INVITE_PARTICIPANT,
        @SerializedName("user_delete")USER_DELETE,
        @SerializedName("subscription_renew_failure")SUBSCRIPTION_RENEW_FAILURE,
        @SerializedName("subscription_failure")SUBSCRIPTION_FAILURE,
        @SerializedName("report_user")REPORT_USER,
        @SerializedName("renew_subscription_reminder")RENEW_SUBSCRIPTION_REMINDER,
        @SerializedName("renew_subscription") RENEW_SUBSCRIPTION,
        @SerializedName("remove_participant")REMOVE_PARTICIPANT,
        @SerializedName("reject_username")REJECT_USERNAME,
        @SerializedName("other")OTHER,
        @SerializedName("new_subscription")NEW_SUBSCRIPTION,
        @SerializedName("new_follower")NEW_FOLLOWER,
        @SerializedName("new_fan")NEW_FAN,
        @SerializedName("admin_invite")ADMIN_INVITE,
        @SerializedName("accept_username")ACCEPT_USERNAME,
        @SerializedName("accept_join_request")ACCEPT_JOIN_REQUEST,
        @SerializedName("gifts")GIFT_RECEIVE,
        @SerializedName("poll_invite") POLL_INVITE,
        @SerializedName("invited_to_podium") INVITE_PODIUM,
        @SerializedName("podium") PODIUM,
        @SerializedName("deals") DEALS,
        @SerializedName("huddle") HUDDLE_MESSAGE,
        @SerializedName("notify_manager") POLL_VOTED,
        @SerializedName("birthdays") BIRTHDAYS,
        @SerializedName("report_comment") REPORT_COMMENT,
        @SerializedName("report_post") REPORT_POST,
        @SerializedName("comment_created") FLASH_COMMENT,
        @SerializedName("flash_comment_reply") FLASH_COMMENT_REPLY,
        @SerializedName("postat_comment") POSTAT_COMMENT,
        @SerializedName("Payouts") SELL_FLIX_PAYOUTS,
        @SerializedName("podium_gifts") PODIUM_GIFT,
        @SerializedName("invited_to_contribute_podium_challenge") PODIUM_EXTERNAL_CONTRIBUTE_CHALLENGE,
        @SerializedName("president") PRESIDENT,
        @SerializedName("admin_added_coins") COINS_UPDATE,
        @SerializedName("flix_purchase") FLIX_PURCHASED,
        @SerializedName("report_closed") JURY_CASE_REPORT_CLOSED,
        @SerializedName("case_notification_no_fine") CASE_NOTIFICATION_NO_FINE,
        @SerializedName("report_fine_distribution_ib") REPORT_FINE_INVESTIGATION_BUREAU,
        @SerializedName("report_fine_distribution_jury") JURY_REPORT_FINE_CLOSED,
        @SerializedName("report_fine_distribution_defendant") REPORT_FINE_CLOSED,
        @SerializedName("report_fine_distribution_plaintiff") MY_LEGAL_RECORD_REPORT_BAN,
        @SerializedName("report_advocate_fee") REPORT_ADVOCATE_FEE,
        @SerializedName("report_fine_distribution_plaintiff") REPORT_FINE_DISTRIBUTION,
        @SerializedName("report_ban_fine_distribution_plaintiff") REPORT_BAN_FINE_DISTRIBUTION,
        @SerializedName("social_pending_approval") SOCIAL_PENDING_APPROVAL,
        @SerializedName("case_notification") CASE_NOTIFICATION,
        @SerializedName("admin_announcement") ADMIN_ANNOUNCEMENT,
        @SerializedName("reported_podium_winner") REPORTED_PODIUM_WINNER,
        @SerializedName("e_tribe") E_TRIBE,
        @SerializedName("birthday_podium_live") BIRTHDAY_PODIUM_LIVE;


        override fun toString(): String {
            return javaClass.getField(name).getAnnotation(SerializedName::class.java)?.value ?: ""
        }
    }

    enum class NotificationStatus {
        @SerializedName("Declined")DECLINED,
        @SerializedName("Accepted") ACCEPTED;

        override fun toString(): String {
            return javaClass.getField(name).getAnnotation(SerializedName::class.java)?.value ?: ""
        }
    }

    val isRead: Boolean
        get() = readTime.isNullOrBlank()

    val parsedCreatedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(timeCreated)

    val huddleType: HuddleType
        get() = if (isPrivate == true) HuddleType.PRIVATE else HuddleType.PUBLIC

    val bodyAsHTML: Spanned
        get() = HtmlCompat.fromHtml(body.orEmpty(), HtmlCompat.FROM_HTML_MODE_LEGACY)

    val invitedTimeRemaining : Long
        get() {

            val elapsedSeconds = (DateTimeUtils.durationToNowFromPast(parsedCreatedTime))?.seconds ?: 0L
            val remainingTime = 30 - elapsedSeconds
            return remainingTime.coerceIn(0, 30)
        }

}
