package com.app.messej.data.model.socket

import com.app.messej.data.model.UserIdAndName
import com.app.messej.data.model.enums.AppLocale
import com.app.messej.data.model.enums.UserCitizenship
import com.google.gson.annotations.SerializedName
import java.time.ZonedDateTime

sealed class PromoAnnouncement {
    open val maxInQueue: Int
        get() = 1

    abstract val type: AnnouncementType

    var created: ZonedDateTime = ZonedDateTime.now()
        private set

    fun setCreatedDate() {
        created = ZonedDateTime.now()
    }

    enum class AnnouncementType {
        @SerializedName("admin_announcement_scheduled") ADMIN_ANNOUNCEMENT_SCHEDULED,
        @SerializedName("top_user_podium") TOP_USER_PODIUMS,
        @SerializedName("top_liked_podiums") TOP_LIKED_PODIUMS,
        @SerializedName("maidan_result") MAIDAN_RESULT,
        @SerializedName("admin_announcement") ADMIN_ANNOUNCEMENT,
        @SerializedName("user_citizenship_upgrade") USER_UPGRADE,
        @SerializedName("new_case_reported") CASE_REPORTED,
        @SerializedName("strongest_tribe") STRONGEST_TRIBE,
        @SerializedName("most_generous_user") MOST_GENEROUS_USER,
        @SerializedName("top_gifts_podium") MOST_GENEROUS_PODIUM,
        @SerializedName("social_cases") SOCIAL_CASES,
        @SerializedName("birthdays_today") BIRTHDAYS_TODAY;

        fun serializedName(): String {
            return javaClass.getField(name).getAnnotation(SerializedName::class.java)?.value ?: ""
        }
    }

    data class AdminMessage(
        @SerializedName("message") val message: String?,
        @SerializedName("language") val language: String?
    ): PromoAnnouncement() {
        override val type: AnnouncementType
            get() = AnnouncementType.ADMIN_ANNOUNCEMENT

        val isRtl: Boolean
            get() = language == AppLocale.ARABIC.isoCode || language == AppLocale.ARABIC.apiCode
    }

    data class MaidanResult(
        @SerializedName("winner_id") val winnerId: Int,
        @SerializedName("winner_name") val winnerName: String,
        @SerializedName("loser_id") val loserId: Int,
        @SerializedName("loser_name") val loserName: String,
        @SerializedName("podium_id") val podiumId: String
    ): PromoAnnouncement() {
        override val type: AnnouncementType
            get() = AnnouncementType.MAIDAN_RESULT
    }

    data class TopUserPodium(
        @SerializedName("id") val id: String,
        @SerializedName("name") val name: String,
        @SerializedName("live_users") val liveUsers: Int,
    ): PromoAnnouncement() {
        override val type: AnnouncementType
            get() = AnnouncementType.TOP_USER_PODIUMS
    }

    data class TopLikedPodium(
        @SerializedName("id") val id: String,
        @SerializedName("name") val name: String,
        @SerializedName("likes") val likes: Int
    ): PromoAnnouncement() {
        override val type: AnnouncementType
            get() = AnnouncementType.TOP_LIKED_PODIUMS
    }

    data class UserUpgrade(
        @SerializedName("user_id") val userId: Int,
        @SerializedName("name") val name: String,
        @SerializedName("citizenship") val citizenship: UserCitizenship
    ) : PromoAnnouncement() {
        override val maxInQueue: Int
            get() = 3

        override val type: AnnouncementType
            get() = AnnouncementType.USER_UPGRADE
    }

    data class CaseReported(
        @SerializedName("report_id") val reportId: Int,
    ) : PromoAnnouncement() {

        override val type: AnnouncementType
            get() = AnnouncementType.CASE_REPORTED
    }

    data class StrongestTribe(
        @SerializedName("huddle_id") val huddleId: Int,
        @SerializedName("name") val tribeName: String,
        @SerializedName("manager_name") val managerName: String,
        @SerializedName("manager_id") val managerId: Int,
        @SerializedName("members") val members: Int,
    ) : PromoAnnouncement() {

        override val type: AnnouncementType
            get() = AnnouncementType.STRONGEST_TRIBE
    }

    data class MostGenerousUser(
        @SerializedName("user_id") val userId: Int,
        @SerializedName("name") val name: String,
        @SerializedName("coins") val coinsContributed: Int,
    ) : PromoAnnouncement() {

        override val type: AnnouncementType
            get() = AnnouncementType.MOST_GENEROUS_USER
    }

    data class MostGenerousPodium(
        @SerializedName("id") val podiumId: String,
        @SerializedName("name") val name: String,
        @SerializedName("coins") val coinsContributed: Int,
    ) : PromoAnnouncement() {

        override val type: AnnouncementType
            get() = AnnouncementType.MOST_GENEROUS_PODIUM
    }

    data class SocialCases(
        @SerializedName("case_count") val caseCount: Int?
    ) : PromoAnnouncement() {

        override val type: AnnouncementType
            get() = AnnouncementType.SOCIAL_CASES
    }

    data class AdminAnnouncementScheduled(
        @SerializedName("id") val id: Int?,
        @SerializedName("message") val message: String?,
        @SerializedName("language") val language: String?,
        @SerializedName("repeat") val repeat: Boolean?,
        @SerializedName("citizenships") val citizenships: List<UserCitizenship>?,
    ) : PromoAnnouncement() {

        override val maxInQueue: Int
            get() = Int.MAX_VALUE

        override val type: AnnouncementType
            get() = AnnouncementType.ADMIN_ANNOUNCEMENT_SCHEDULED

        val isRtl: Boolean
            get() = language == AppLocale.ARABIC.isoCode || language == AppLocale.ARABIC.apiCode
    }

    data class Birthday(
        val userType: BirthdayType,
        val user: UserIdAndName


    ) : PromoAnnouncement() {

        override val maxInQueue: Int
            get() = Int.MAX_VALUE

        override val type: AnnouncementType
            get() = AnnouncementType.BIRTHDAYS_TODAY

        enum class BirthdayType {
            PRESIDENT, MINISTER
        }
    }
}
