package com.app.messej.data.model.entity


import android.util.Log
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.Category
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.UserGiftPaused
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.enums.ChallengeType
import com.app.messej.data.model.enums.Gender
import com.app.messej.data.model.enums.PodiumEntry
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.data.model.enums.TheaterJoinType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.ui.utils.DataFormatHelper
import com.app.messej.ui.utils.DataFormatHelper.numberToKWithFractions
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type
import java.time.Duration
import java.time.ZonedDateTime

@Entity(tableName = EntityDescriptions.TABLE_PODIUMS)

@TypeConverters(
    Category.Converter::class,
    Podium.Anthem.Converter::class,
    Podium.TopGifters.Converter::class
)
data class Podium(
    @SerializedName("id") @PrimaryKey(autoGenerate = false  ) @ColumnInfo(name = COLUMN_ID                      ) val id            : String,
    @SerializedName("agora_channel_id"                      ) @ColumnInfo(name = "agora_channel_id"             ) val channelId     : String?,
    @SerializedName("name"                                  ) @ColumnInfo(name = "name"                         ) val name          : String,
    @SerializedName("about"                                 ) @ColumnInfo(name = "about"                        ) val bio           : String?,
    @SerializedName("category"                              ) @ColumnInfo(name = "category"                     ) val category      : Category,
    @SerializedName("thumbnail"                             ) @ColumnInfo(name = "thumbnail"                    ) val thumbnail     : String?,
    @SerializedName("profile_pic"                           ) @ColumnInfo(name = "profile_pic"                  ) val profilePic    : String?,
    @SerializedName("invite_code"                           ) @ColumnInfo(name = "invite_code"                  ) val inviteCode    : String?,
    @SerializedName("kind"                                  ) @ColumnInfo(name = "kind"                         ) val kind          : PodiumKind?,

    @SerializedName("manager_id"                            ) @ColumnInfo(name = "manager_id"                   ) val managerId     : Int,
    @SerializedName("manager_name"                          ) @ColumnInfo(name = "manager_name"                 ) var managerName   : String,
    @SerializedName("role"                                  ) @ColumnInfo(name = "role"                         ) val role          : PodiumUserRole? = null,

    @SerializedName("is_private"                            ) @ColumnInfo(name = "is_private"  ,defaultValue = "0" ) val isPrivate     : Boolean = false,
    @SerializedName("type"                                  ) @ColumnInfo(name = "entry"                        ) val entry         : PodiumEntry? = null,

    @SerializedName("live"                                  ) @ColumnInfo(name = "live"                         ) val isLive        : Boolean = false,
    @SerializedName("live_users"                            ) @ColumnInfo(name = "live_users"                   ) val liveUsers     : Int = 0,
    @SerializedName("total_users"                           ) @ColumnInfo(name = "total_users"                  ) val totalUsers    : Int = 0,

    @SerializedName("is_invited"                            ) @ColumnInfo(name = "is_invited"                   ) val wasInvited    : Boolean? = false,

    @SerializedName("created"                               ) @ColumnInfo(name = "created"                      ) val createdTime   : String?,
    @SerializedName("updated"                               ) @ColumnInfo(name = "updated"                      ) val updatedTime   : String?,
    @SerializedName("started"                               ) @ColumnInfo(name = "started"                      ) val startedTime   : String?,
    @SerializedName("last_go_live_time"                     ) @ColumnInfo(name = "last_go_live_time"            ) val lastGoLiveTime: String?,

    @SerializedName("total_likes"                           ) @ColumnInfo(name = "total_likes"                  ) val totalLikes    : Int = 0,
    @SerializedName("total_coins"                           ) @ColumnInfo(name = "total_coins"                  ) val totalCoins    : Int? = 0,
    @SerializedName("likes"                                 ) @ColumnInfo(name = "likes", defaultValue = "0"    ) var likes : Int = 0,
    @SerializedName("likes_disabled"                        ) @ColumnInfo(name = "likes_disabled"               ) val likesDisabled : Boolean? = false,
    @SerializedName("podium_gift_paused"                    ) @ColumnInfo(name = "podium_gift_paused"           ) val podiumGiftPaused : Boolean? = false,
    @SerializedName("likes_disabled_by")                      @ColumnInfo(name = "likes_disabled_by"            ) val likesDisabledBy: Int? = null,
    @SerializedName("is_liked_by_self"                      ) @ColumnInfo(name = "is_liked_by_self"             ) val liked         : Boolean = false,
    @SerializedName("temp_id"                               ) @ColumnInfo(name = "temp_id"                      ) val tempId        : String,
    @SerializedName("share_link"                            ) @ColumnInfo(name = "share_link"                   ) val shareLink     : String? = null,
    @SerializedName("invited_to_be_admin"                   ) @ColumnInfo(name = "invited_to_be_admin"          ) var invitedToBeAdmin : Boolean? = false,

    @SerializedName("chat_disabled"                         ) @ColumnInfo(name = "chat_disabled"                ) var chatDisabled  : Boolean? = false,
    @SerializedName("chat_disabled_by"                      ) @ColumnInfo(name = "chat_disabled_by"             ) var chatDisabledBy : Int?,
    @SerializedName("mic_disabled"                          ) @ColumnInfo(name = "mic_disabled"                 ) var requestToSpeakDisabled : Boolean? = false,
    @SerializedName("mic_disabled_by"                       ) @ColumnInfo(name = "mic_disabled_by"              ) val micDisabledBy: Int? = null,

    @SerializedName("gifts_count"                           ) @ColumnInfo(name = "gifts_count", defaultValue = "0") val giftCount: Int = 0,
    @SerializedName("hide_live_users"                       ) @ColumnInfo(name = "hide_live_users"              ) val hideLiveUsers: Boolean? = false,
    @SerializedName("freeze"                                ) @ColumnInfo(name = "freeze"                       ) var chatFrozen    : Boolean = true,

    @SerializedName("stage_fee_paid"                        ) @ColumnInfo(name = "stage_fee_paid"               ) var stageFeePaid    : Boolean? = false,
    @SerializedName("audience_fee_paid"                     ) @ColumnInfo(name = "audience_fee_paid"            ) var audienceFeePaid : Boolean? = false,

    @SerializedName("current_live_session_id"               ) @ColumnInfo(name = "current_live_session_id"      ) val currentLiveSessionId: String? = null,
    @SerializedName("manager_profile_thumbnail"             ) @ColumnInfo(name = "manager_profile_thumbnail"    ) val managerProfileThumbnail: String? = null,
    @SerializedName("mute"                                  ) @ColumnInfo(name = "mute"                         ) val mute: Boolean? = null,
    @SerializedName("game_type"                             ) @ColumnInfo(name = "game_type"                    ) val gameType  :   ChallengeType?  =   null,
//    @SerializedName("deleted")                                @ColumnInfo(name = "deleted"                      ) val deleted: Boolean? = null

    @SerializedName("required_rating_to_comment"            ) @ColumnInfo(name = "required_rating_to_comment"      ) val requiredRatingToComment: Int? = null,
    @SerializedName("required_rating_to_speak"              ) @ColumnInfo(name = "required_rating_to_speak"        ) val requiredRatingToSpeak: Int? = null,
    @SerializedName("joining_fee"                           ) @ColumnInfo(name = "joining_fee"                     ) val joiningFee: Int? = null,
    @SerializedName("joining_fee_paid"                      ) @ColumnInfo(name = "joining_fee_paid", defaultValue = "0") val joiningFeePaid: Boolean = false,


    //For Maidan Podiums Only
    @SerializedName("talk_time_duration"                    ) @ColumnInfo(name = "talk_time_duration"           ) val talkTimeDuration : Int? = null,
    @SerializedName("talk_time_start"                       ) @ColumnInfo(name = "talk_time_start"              ) val talkTimeStart    : Int? = null,
    @SerializedName("parent_id"                             ) @ColumnInfo(name = "parent_id"                    ) val parentId         : String? = null,
    @SerializedName("competitor_user_id"                    ) @ColumnInfo(name = "competitor_user_id"           ) var competitorUserId : Int? = null,
    @SerializedName("competitor_thumbnail"                  ) @ColumnInfo(name = "competitor_thumbnail"         ) val competitorThumbnail: String? = null,
    @SerializedName("competitor_name"                       ) @ColumnInfo(name = "competitor_name"              ) var competitorName: String? = null,
    @SerializedName("competitor_muted"                      ) @ColumnInfo(name = "competitor_muted"             ) val competitorMuted: Boolean? = false,
    @SerializedName("maidan_status"                         ) @ColumnInfo(name = "maidan_status"                ) val maidanStatus: MaidanStatus? = null,
    @SerializedName("competitor_podium_id"                  ) @ColumnInfo(name = "competitor_podium_id"         ) var competitorPodiumId: String? = null,
    @SerializedName("invited_user_id"                       ) @ColumnInfo(name = "invited_user_id"              ) val privateInvitedUserId: Int? = null,
    @SerializedName("challenge_id"                          ) @ColumnInfo(name = "challenge_id"                 ) val challengeId: String? = null,
    @SerializedName("maidan_sessions_count"                 ) @ColumnInfo(name = "maidan_sessions_count"        ) val sessionCount: Int? = null,

    //For Theater Podiums only
    @SerializedName("stage_fee"                             ) @ColumnInfo(name = "stage_fee"                    ) val stageFee : Int? = null,
    @SerializedName("audience_fee"                          ) @ColumnInfo(name = "audience_fee"                 ) val audienceFee : Int? = null,
    @SerializedName("yalla_challenges_count"                ) @ColumnInfo(name = "yalla_challenges_count"       ) var yallaChallengesCount :Int?=null,
    @SerializedName("allow_yalla"                           ) @ColumnInfo(name = "allow_yalla"                  ) var allowYallaGuys :Boolean?=false,
    @SerializedName("required_user_rating"                  ) @ColumnInfo(name = "required_user_rating"         ) var requiredUserRating :Int?=null,
    @SerializedName("time_blocked"                          ) @ColumnInfo(name = "time_blocked"                 ) val timeBlocked: String? = null,
    @SerializedName("anthem_object"                         ) @ColumnInfo(name = "anthem"                       ) var flashatAnthem : Anthem? = null,

    //For Birthday Podium
    @SerializedName("top_gifters"                           ) @ColumnInfo(name = "top_gifters"                  ) val topGifters : List<TopGifters>? = null
    ){

    @SerializedName("coins_given"                           ) @Ignore                                 var coinsGiven    : Int? = null
    @SerializedName("coin_balance"                          ) @Ignore                                 var coinBalance   : Double? = null

    @SerializedName("speakers"                              ) @Ignore                                 var speakers      : List<PodiumSpeaker> = listOf()
    @SerializedName("agora_token"                           ) @Ignore                                 var agoraToken    : String? = null
    @SerializedName("challenge"                             ) @Ignore                                 var challenge     : PodiumChallenge? = null
    @SerializedName("speaker_invites"                       ) @Ignore                                 var speakerInvites: List<SpeakerInvite>? = null
    @SerializedName("gift_paused_participants"              ) @Ignore                                 var giftsPausedParticipants:List<UserGiftPaused>?=null

    // Anthem

    companion object {
        const val COLUMN_ID = "id"
    }

    enum class PodiumType {
        @SerializedName("PUBLIC") PUBLIC,
        @SerializedName("PRIVATE") PRIVATE
    }

    enum class MaidanStatus {
        @SerializedName("WAITING") WAITING,
        @SerializedName("CHALLENGE_LIVE") CHALLENGE_LIVE,
        @SerializedName("EXTRA_TIME") EXTRA_TIME,
        @SerializedName("TALK_TIME") TALKTIME;

        companion object {
            fun MaidanStatus?.isChallengeTime(): Boolean {
                return this == CHALLENGE_LIVE || this == EXTRA_TIME
            }
        }
    }


    enum class PodiumUserRole {
        @SerializedName("MANAGER") MANAGER,
        @SerializedName("ADMIN") ADMIN,
        @SerializedName("AUDIENCE") AUDIENCE,
        @SerializedName("INVITEE") INVITEE,
        @SerializedName("INVITED") INVITED;

        val isElevated: Boolean
            get() = this in listOf(MANAGER,ADMIN)

        val canSpeak: Boolean
            get() = this in listOf(MANAGER)

        companion object {
            fun PodiumUserRole?.orDefault(): PodiumUserRole {
                return this?: AUDIENCE
            }
        }
    }

    data class TopGifters(
        @SerializedName(value = "id") override val id: Int,
        @SerializedName(value = "name") override val name: String,
        @SerializedName(value = "profile_url") override val thumbnail: String?,
        @SerializedName(value = "username") override val username: String?,
        @SerializedName(value = "membership") override val membership: UserType,
        @SerializedName(value = "verified") override val verified: Boolean,
        @SerializedName(value = "citizenship") override val citizenship: UserCitizenship?,
        @SerializedName(value = "score") val score: Int?
    ): AbstractUser() {
        val updatedScore: String
            get() = (score?: 0).numberToKWithFractions()

        class Converter {
            @TypeConverter
            fun decode(data: String?): List<TopGifters>? {
                data?: return null
                val type: Type = object : TypeToken<List<TopGifters>?>() {}.type
                return Gson().fromJson(data, type)
            }
            @TypeConverter
            fun encode(someObjects: List<TopGifters>?): String? {
                return Gson().toJson(someObjects)
            }
        }
    }

    data class SpeakerInvite(
        @SerializedName("invitee_id"    ) val inviteeId     : Int,
        @SerializedName("invited_by"    ) val invitedBy     : Int,
        @SerializedName("time_invited"  ) val timeInvited   : String?,
        @SerializedName("accepted"      ) val accepted      : Boolean,
        @SerializedName("action"        ) val joinType      : TheaterJoinType? = null,
        @SerializedName("invited_for_free") val invitedForFree: Boolean? = true,
        @SerializedName("fees") val inviteFee: Int? = null,
    ) {
        val parsedTimeInvited: ZonedDateTime?
            get() = DateTimeUtils.parseZonedDateTime(timeInvited)

        val timeToExpire: Duration?
            get() {
                val ti = parsedTimeInvited?: return null
                val expireTime = ti.plusSeconds(30)
                return if(ZonedDateTime.now().isAfter(expireTime)) Duration.ZERO
                else Duration.between(ZonedDateTime.now(),expireTime)
            }

        val hasExpired: Boolean
            get() {
                val ti = parsedTimeInvited?: return false
                Log.d("PLVM-IS", "hasExpired: Duration.between( $ti , ${ZonedDateTime.now()} ) : ${Duration.between(ti,ZonedDateTime.now())}")
                return Duration.between(ti,ZonedDateTime.now()) > Duration.ofSeconds(30)
            }
        companion object {
            fun create(inviteeId: Int, invitedBy: Int): SpeakerInvite = SpeakerInvite(
                inviteeId = inviteeId,
                invitedBy = invitedBy,
                timeInvited = ZonedDateTime.now().toString(),
                accepted = false
            )
        }
    }

    data class Anthem(
//        @SerializedName("anthem_audio_url"    ) val anthemAudioUrl: String? = null,
//        @SerializedName("anthem_name"         ) val anthemName    : String? = null,
        @SerializedName("anthem_start_time"   ) val anthemStartTime: Long? = null, // Or ZonedDateTime if you parse it
        @SerializedName("podium_id"           ) val podiumId      : String? = null
    ) {
        class Converter {
            @TypeConverter
            fun decode(data: String?): Anthem? {
                data?: return null
                val type: Type = object : TypeToken<Anthem?>() {}.type
                return Gson().fromJson<Anthem>(data, type)
            }
            @TypeConverter
            fun encode(someObjects: Anthem?): String? {
                return Gson().toJson(someObjects)
            }
        }
        companion object {
            const val ANTHEM_DURATION = "2:21"
        }

        val parsedAnthemStartTime: ZonedDateTime?
            get() = anthemStartTime?.let { DateTimeUtils.parseMillisToDateTime(it*1000) }

        val duration = DateTimeUtils.parseToDuration(ANTHEM_DURATION)?: Duration.ZERO

        val playing: Boolean
            get() {
                val start = parsedAnthemStartTime?: return false
                val now = ZonedDateTime.now()
                Log.w("TVVM", "isPlaying: start: $start, now: $now, duration: $duration")
                return start.isBefore(now) && start.plus(duration).isAfter(now)
            }

        val expired: Boolean
            get() {
                val start = parsedAnthemStartTime?: return false
                val now = ZonedDateTime.now()
                return start.plus(duration).plus(Duration.ofSeconds(5)).isBefore(now)
            }

        val millisToStart: Long
            get() {
                val start = parsedAnthemStartTime?: return 0
                val now = ZonedDateTime.now()
                return Duration.between(now,start).toMillis().coerceAtLeast(0L)
            }

    }

    fun cleanedCopy(): Podium {
        return this.copy(
            liveUsers = 0,
            totalUsers = 0,
            likes = 0,
            totalLikes = 0
        )
    }

    val agoraChannelID: String
        get() = channelId?:id

    val isInvited: Boolean
        get() = role == PodiumUserRole.INVITED

    val isManager: Boolean
        get() = role == PodiumUserRole.MANAGER

    val isAdmin: Boolean
        get() = role == PodiumUserRole.ADMIN

    val isAudience: Boolean
        get() = role == PodiumUserRole.AUDIENCE

    val shouldGoLive: Boolean
        get() = role?.isElevated == true && !isLive

    val isInvitee: Boolean
        get() = role == PodiumUserRole.INVITEE

    val type : PodiumType
        get() = if(isPrivate) PodiumType.PRIVATE else PodiumType.PUBLIC

    val canExit: Boolean
        get() = wasInvited==true || role == PodiumUserRole.INVITEE || role == PodiumUserRole.INVITED

    val chatDisabledByRole: PodiumUserRole?
        get() {
            chatDisabledBy?: return null
            return if (chatDisabledBy==managerId) PodiumUserRole.MANAGER
            else PodiumUserRole.ADMIN
        }

    val parsedCreatedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(createdTime)

    val parsedLastGoLiveTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(lastGoLiveTime)

    val parsedUpdatedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(updatedTime)

    val parsedStartedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(startedTime)

    val likesFormatted: String
        get() = likes.numberToKWithFractions()

    val totalCoinsFormatted: String
        get() = totalCoins?.numberToKWithFractions().orEmpty()

    val totalLikesFormatted: String
        get() = totalLikes.numberToKWithFractions()

    val liveUsersFormatted: String
        get() = DataFormatHelper.numberToK(liveUsers)

    val giftCountFormatted: String
        get() = DataFormatHelper.numberToK(giftCount)

    val totalUsersFormatted: String
        get() = DataFormatHelper.numberToK(totalUsers)

    val canEdit: Boolean
        get() = !isLive && isManager

    val maxSpeakers : Int
        get() = kind?.maxSpeakers?:0

    val hasCompetitor: Boolean
        get() = competitorUserId!=null

    val canChallenge: Boolean
        get() = !hasCompetitor

    val profileDp: String
        get() = thumbnail?.takeIf { it.isNotEmpty() } ?: managerProfileThumbnail.toString()

    fun allowedFor(user: CurrentUser): Boolean {
        val gender = user.profile.gender
        // User is Male and trying to join a Women-only podium
        if (entry == PodiumEntry.WOMEN_ONLY && gender != Gender.FEMALE) {
            return false
        }
        // User is Female and trying to join a Men-only podium
        else if (entry == PodiumEntry.MEN_ONLY && gender != Gender.MALE) {
            return false
        }
        else if (managerId == user.id || isAdmin) {
            return true
        }
        else if (user.userRatingPercent.toInt() < (requiredUserRating ?: 0)) {
            return false
        }
        return true
    }

    val parsedTalkTimeStart: ZonedDateTime?
        get() {
            val timeInMilliSeconds = talkTimeStart?.times(1000L)
            return DateTimeUtils.parseMillisToDateTime(timeInMilliSeconds?:return null)
        }

    val parsedTalkTimeDuration: Duration
        get() = Duration.ofSeconds(talkTimeDuration?.toLong()?:0L)

    val talkTimeEnd: ZonedDateTime?
        get() {
            return parsedTalkTimeStart?.plus(parsedTalkTimeDuration)
        }

    val coinsFormatted: String
        get() = coinsGiven?.numberToKWithFractions().orEmpty()

    val hasYallaGuys: Boolean
        get() = kind == PodiumKind.LECTURE && !isPrivate

    val actionBarShareIconVisible: Boolean
        get() = kind == PodiumKind.BIRTHDAY

    val isUserBlocked: Boolean
        get() = !timeBlocked.isNullOrEmpty()
}