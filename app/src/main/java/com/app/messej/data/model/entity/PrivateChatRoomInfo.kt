package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.app.messej.data.model.api.settings.PrivacyMessage
import com.app.messej.data.model.api.settings.StartPrivateChat
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.room.EntityDescriptions
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

@Entity(
    tableName = EntityDescriptions.TABLE_PRIVATE_CHAT_ROOM_INFO
)
@TypeConverters(
    PrivateChatRoomInfo.UserBlockStatus.Converter::class,
    PrivateChatRoomInfo.UserInfo.Converter::class,
    PrivateChatRoomInfo.Converter::class, StartPrivateChat.Converter::class, PrivacyMessage.Converter::class
)
data class PrivateChatRoomInfo(
    @SerializedName("canChat"                ) @ColumnInfo(name = "canChat"                ) val canChat                : Boolean?  = null,
    @SerializedName("error"                  ) @ColumnInfo(name = "error"                  ) val error                  : String?   = null,
    @SerializedName("chatBlockedBySender"    ) @ColumnInfo(name = "chatBlockedBySender"    ) val chatBlockedBySender    : Boolean?  = null,
    @SerializedName("userBlockedByReceiver"  ) @ColumnInfo(name = "userBlockedByReceiver"  ) val userBlockedByReceiver  : Boolean?  = null,
    @SerializedName("userBlockedBySender"    ) @ColumnInfo(name = "userBlockedBySender"    ) val userBlockedBySender    : Boolean?  = null,
    @SerializedName("chatDisabledBySender"   ) @ColumnInfo(name = "chatDisabledBySender"   ) val chatDisabledBySender   : Boolean?  = null,
    @SerializedName("chatDisabledByReceiver" ) @ColumnInfo(name = "chatDisabledByReceiver" ) val chatDisabledByReceiver : Boolean?  = null,
    @SerializedName("followed_by_each"       ) @ColumnInfo(name = "followed_by_each"       ) val followedByMe           : Boolean?  = null,
    @SerializedName("is_banned"              ) @ColumnInfo(name = "is_banned"              ) val isBannedOrSuspectedBan : Boolean?  = null,
    @SerializedName("is_blacklisted"         ) @ColumnInfo(name = "is_blacklisted"         ) val isBlackListed          : Boolean?  = null,
    @SerializedName("chatRoom"               ) @Embedded var chatRoom               : ChatRoom,
    @SerializedName("privateChatSettings"    ) @Embedded var privateChatSettings   : PrivacyMessage?=null,
    @SerializedName("citizenship"            ) @ColumnInfo(name = "citizenship"            )var citizenship           : UserCitizenship?=null,
    @SerializedName("isNewChat"              ) @ColumnInfo(name = "isNewChat"              ) val isNewChat             : Boolean?  = null,
){

    @PrimaryKey @ColumnInfo(name = COLUMN_LOCAL_ID    ) var localRoomId: String = chatRoom.id

    data class ChatRoom (
        @SerializedName("participants"       ) @ColumnInfo(name = "participants"           ) val participants    : List<Int>   = listOf(),
        @SerializedName("creator"            ) @ColumnInfo(name = "creator"                ) val creator         : Int?             = null,
        @SerializedName("userBlockStatus"    ) @ColumnInfo(name = "userBlockStatus"        ) val userBlockStatus : UserBlockStatus? = UserBlockStatus(),
        @SerializedName("followedStatus"     ) @ColumnInfo(name = "followedStatus"         ) var followedStatus  : Map<String, UserInfo>?,
        @SerializedName("threadType"         ) @ColumnInfo(name = "threadType"             ) val threadType      : Map<String, String>? = null,
        @SerializedName("created"            ) @ColumnInfo(name = "created"                ) val created         : String?          = null,
        @SerializedName("id"                 ) @ColumnInfo(name = COLUMN_ID                ) val id: String,
        @SerializedName("roomStatus"         ) @ColumnInfo(name = "roomStatus"             ) val roomStatus      : String?          = null,
        @SerializedName("type"               ) @ColumnInfo(name = "type"                   ) val type            : PrivateChat.ChatType = PrivateChat.ChatType.PRIVATE,
        @SerializedName("chatType"           ) @ColumnInfo(name = "chatType"               ) val chatType        : PrivateChat.ChatType? = null
    )

    companion object {
        const val COLUMN_ID = "id"
        const val COLUMN_LOCAL_ID = "localId"
    }

    fun userInfo(id : Int) : UserInfo? {
        val value = "USER#".plus(id)
        return chatRoom.followedStatus?.get(value)
    }

    fun setUserInfo(id: Int, info: UserInfo) {
        val map = chatRoom.followedStatus.orEmpty().toMutableMap()
        val value = "USER#".plus(id)
        map[value] = info
        chatRoom.followedStatus = map
    }

    val isFollowedByEach : Boolean
        get() {
            val users  = chatRoom.participants
            return (chatRoom.followedStatus?.get("USER#".plus(users[0]))?.followed == true &&
                    chatRoom.followedStatus?.get("USER#".plus(users[1]))?.followed == true)
        }

    class Converter {
        @TypeConverter
        fun decodeFollowedStatus(data: String?): Map<String, UserInfo>? {
            data?: return null
            val type: Type = object : TypeToken<Map<String, UserInfo>?>() {}.type
            return Gson().fromJson<Map<String, UserInfo>>(data, type)
        }
        @TypeConverter
        fun encodeFollowedStatus(someObjects: Map<String, UserInfo>?): String? {
            return Gson().toJson(someObjects)
        }
        @TypeConverter
        fun decodeThreadType(data: String?): Map<String, String>? {
            data?: return null
            val type: Type = object : TypeToken<Map<String, String>?>() {}.type
            return Gson().fromJson<Map<String, String>>(data, type)
        }
        @TypeConverter
        fun encodeThreadType(someObjects: Map<String, String>?): String? {
            return Gson().toJson(someObjects)
        }
    }

    data class UserBlockStatus(@SerializedName("blocked") val blocked: Boolean? = null) {
        class Converter {
            @TypeConverter
            fun decode(data: String?): UserBlockStatus? {
                data?: return null
                val type: Type = object : TypeToken<UserBlockStatus?>() {}.type
                return Gson().fromJson<UserBlockStatus>(data, type)
            }
            @TypeConverter
            fun encode(someObjects: UserBlockStatus?): String? {
                return Gson().toJson(someObjects)
            }
        }
    }
    data class UserInfo(@SerializedName("messagesLeft") val messagesLeft: Int? = null, @SerializedName("followed") val followed: Boolean) {
        class Converter {
            @TypeConverter
            fun decode(data: String?): UserInfo? {
                data?: return null
                val type: Type = object : TypeToken<UserInfo?>() {}.type
                return Gson().fromJson<UserInfo>(data, type)
            }
            @TypeConverter
            fun encode(someObjects: UserInfo?): String? {
                return Gson().toJson(someObjects)
            }
        }
    }
}
