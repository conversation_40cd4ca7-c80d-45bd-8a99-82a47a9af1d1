package com.app.messej.data.model.enums

import com.app.messej.data.utils.EnumUtil.except
import com.google.gson.annotations.SerializedName

enum class UserCitizenship {
    @SerializedName(value = "VISITOR", alternate = ["visitor"]) VISITOR,
    @SerializedName(value = "RESIDENT", alternate = ["resident"]) RESIDENT,
    @SerializedName(value = "CITIZEN", alternate = ["citizen"]) CITIZEN,
    @SerializedName(value = "OFFICER", alternate = ["officer"]) OFFICER,
    @SerializedName(value = "AMBASSADOR", alternate = ["ambassador"]) AMBASSADOR,
    @SerializedName(value = "MINISTER", alternate = ["minister"]) MINISTER,
    @SerializedName(value = "GOLDEN",alternate = ["golden"]) GOLDEN,
    @SerializedName(value = "PRESIDENT",alternate = ["president"]) PRESIDENT;



    companion object {
        fun default(): UserCitizenship = VISITOR

        fun UserCitizenship?.orDefault(): UserCitizenship {
            return this?: UserCitizenship.default()
        }

        fun allExceptVisitor() = UserCitizenship.entries.except(VISITOR)

        fun allExceptVisitorAndGoldenUser() = UserCitizenship.entries.except(VISITOR, GOLDEN)

    }

    val isVisitor: Boolean
        get() = this == VISITOR
    val isResident: Boolean
        get() = this == RESIDENT
    val isCitizen: Boolean
        get() = this == CITIZEN

    val isOfficer: Boolean
        get() = this == OFFICER
    val isPresident: Boolean
        get() = this == PRESIDENT

    val isFreeType: Boolean
        get() = this == VISITOR || this == RESIDENT

    val isBelowAmbassador: Boolean
        get() = this < AMBASSADOR

    private val isBelowMinister: Boolean
        get() = this < MINISTER

    val canUpgradeUserLevel: Boolean
        get() = (!this.isFreeType) && (isBelowMinister)

    val isGolden: Boolean
        get() = this == GOLDEN
}