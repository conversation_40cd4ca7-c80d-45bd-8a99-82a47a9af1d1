package com.app.messej.data.socket.repository

import android.util.Log
import androidx.room.withTransaction
import com.app.messej.data.model.api.podium.PodiumBirthdayTopGiftersEventResponse
import com.app.messej.data.model.api.podium.PodiumCloseEvent
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.PodiumClosedBy
import com.app.messej.data.model.socket.PodiumAssemblyExtendSpeakingTimePayload
import com.app.messej.data.model.socket.PodiumAssemblyPauseSpeakingPayload
import com.app.messej.data.model.socket.PodiumAssemblyStartSpeakingPayload
import com.app.messej.data.model.socket.PodiumAssemblyStopSpeakingPayload
import com.app.messej.data.model.socket.PodiumAttendancePayload
import com.app.messej.data.model.socket.PodiumBlockPayload
import com.app.messej.data.model.socket.PodiumCameraTimeExpired
import com.app.messej.data.model.socket.PodiumChatTogglePayload
import com.app.messej.data.model.socket.PodiumCommentDeletePayload
import com.app.messej.data.model.socket.PodiumEnterWaitListPayload
import com.app.messej.data.model.socket.PodiumFreezeUserPayload
import com.app.messej.data.model.socket.PodiumGiftPausePayload
import com.app.messej.data.model.socket.PodiumJoinLeavePayload
import com.app.messej.data.model.socket.PodiumLikePayLoad
import com.app.messej.data.model.socket.PodiumLiveChatPayload
import com.app.messej.data.model.socket.PodiumMicPayLoad
import com.app.messej.data.model.socket.PodiumMuteUserPayload
import com.app.messej.data.model.socket.PodiumSpeakSessionEndPayload
import com.app.messej.data.model.socket.PodiumSpeakerInvitePayload
import com.app.messej.data.model.socket.PodiumSpeakerInviteResponsePayload
import com.app.messej.data.model.socket.PodiumSyncPayload
import com.app.messej.data.model.socket.PodiumUserIdPayload
import com.app.messej.data.model.socket.SentGiftPayload
import com.app.messej.data.socket.PodiumSocketEvent
import com.app.messej.data.socket.PodiumSocketRepository
import com.app.messej.data.utils.JsonUtil.fromJson
import com.google.gson.Gson
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.runBlocking
import org.json.JSONObject

object PodiumEventRepository : BaseEventRepository<PodiumSocketEvent>(PodiumSocketRepository) {

    override fun handleEvent(event: PodiumSocketEvent, data: JSONObject): Boolean {
        Log.w("SOCKETs", "handleEvent: $event")
        when (event) {
            PodiumSocketEvent.RX_TX_ENTER_PODIUM_WAIT_LIST -> onEnterWaitList(data)
            PodiumSocketEvent.RX_TX_EXIT_PODIUM_WAIT_LIST -> onExitWaitList(data)
            PodiumSocketEvent.RX_TX_ENTER_SPEAKER_LIST -> onEnterSpeakerList(data)
            PodiumSocketEvent.RX_TX_EXIT_SPEAKER_LIST -> onExitSpeakerList(data)
            PodiumSocketEvent.RX_TX_PODIUM_CHAT -> onNewChatMessage(data)
            PodiumSocketEvent.RX_MUTE_PODIUM -> onUserMute(data)
            PodiumSocketEvent.RX_UNMUTE_PODIUM -> onUserMute(data)
            PodiumSocketEvent.RX_FREEZE_USER -> onUserFreeze(data)
            PodiumSocketEvent.RX_UNFREEZE_USER -> onUserUnfreeze(data)
            PodiumSocketEvent.RX_PODIUM_SYNC -> onPodiumSync(data)
            PodiumSocketEvent.RX_TX_LIKE_PODIUM -> onPodiumLike(data)
            PodiumSocketEvent.RX_TX_LEAVE_PODIUM -> onUserLeft(data)
            PodiumSocketEvent.RX_TX_CLOSE_PODIUM -> onPodiumClosed(data)
            PodiumSocketEvent.RX_BLOCK_PODIUM -> onBlocked(data)
            PodiumSocketEvent.RX_APPOINT_ADMIN -> onAppointAdmin(data)
            PodiumSocketEvent.RX_NEW_ADMIN -> onNewAdmin(data)
            PodiumSocketEvent.RX_CANCEL_ADMIN_REQUEST -> onCancelAdminRequest(data)
            PodiumSocketEvent.RX_EXIT_ADMIN -> onAdminExit(data)
            PodiumSocketEvent.RX_DISMISS_ADMIN -> onDismissAdmin(data)
            PodiumSocketEvent.RX_TX_SHOW_IN_MAIN_SCREEN -> onShownInMainScreen(data)
            PodiumSocketEvent.RX_CHAT_ENABLED -> onChatEnabled(data)
            PodiumSocketEvent.RX_CHAT_DISABLED -> onChatDisabled(data)
            PodiumSocketEvent.RX_TX_PODIUM_GIFT -> onNewGift(data)
            PodiumSocketEvent.RX_LIKE_DISABLED -> onLikeDisabled(data)
            PodiumSocketEvent.RX_LIKE_ENABLED -> onLikeEnabled(data)
            PodiumSocketEvent.RX_MIC_ENABLED -> onMicEnabled(data)
            PodiumSocketEvent.RX_MIC_DISABLED -> onMicDisabled(data)
            PodiumSocketEvent.RX_PODIUM_ACTIVE -> onPodiumActive(data)
            PodiumSocketEvent.RX_PODIUM_ASSEMBLY_START_SPEAKING -> onStartSpeaking(data)
            PodiumSocketEvent.RX_PODIUM_ASSEMBLY_EXTEND_SPEAKING_TIME-> onExtendSpeakingTime(data)
            PodiumSocketEvent.RX_PODIUM_ASSEMBLY_PAUSE_SPEAKING-> onPauseSpeaking(data)
            PodiumSocketEvent.RX_PODIUM_CAMERA_TIME_EXPIRED ->onCameraTimeExpired(data)
            PodiumSocketEvent.RX_PODIUM_CAMERA_TIME_EXPIRING_SOON ->onCameraTimeExpiredSoon(data)
            PodiumSocketEvent.RX_SPEAKER_INVITE -> onSpeakerInvite(data)
            PodiumSocketEvent.RX_SPEAKER_INVITE_RESPONSE -> onSpeakerInviteResponse(data)
            PodiumSocketEvent.RX_TX_PODIUM_CHAT_DELETE -> onPodiumChatDelete(data)
            PodiumSocketEvent.RX_TX_PODIUM_GIFT_PAUSE -> onPodiumGiftPaused(data)
            PodiumSocketEvent.RX_TX_PODIUM_GIFT_RESUME -> onPodiumGiftResumed(data)
            PodiumSocketEvent.RX_PODIUM_EDIT -> onPodiumEdit(data)
            PodiumSocketEvent.RX_FLASHAT_ANTHEM -> onFlashatAnthem(data)
            PodiumSocketEvent.TOP_GIFTERS -> onBirthdayPodiumGiftListUpdate(data)
            else -> return false
        }
        return true
    }

    private val _editedPodiumDetails :MutableSharedFlow<Podium> = MutableSharedFlow()
    val editedPodiumDetails:SharedFlow<Podium> = _editedPodiumDetails

    fun onPodiumEdit(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<Podium>(data.toString())
            Log.e("PER", "Edit Podium Details:, $result")
            _editedPodiumDetails.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onPodiumEdit: ", e)
        }
    }

    private val _birthdayPodiumTopGiftResponse :MutableSharedFlow<PodiumBirthdayTopGiftersEventResponse> = MutableSharedFlow()
    val birthdayPodiumTopGiftResponse:SharedFlow<PodiumBirthdayTopGiftersEventResponse> = _birthdayPodiumTopGiftResponse
    fun onBirthdayPodiumGiftListUpdate(data: JSONObject) = runBlocking {
        try {
            val response = Gson().fromJson<PodiumBirthdayTopGiftersEventResponse>(data.toString())
            Log.e("PER", "Birthday Podium Gift List:, $response")
            _birthdayPodiumTopGiftResponse.emit(value = response)
        } catch (e: Exception) {
            Log.e("PER", "onBirthdayPodiumGiftError: ", e)
        }
    }

    fun joinPodium(podiumId: String, userId: Int): Boolean { // TODO("Hardcoded "roomType" for testing")
        return PodiumSocketRepository.sendEvent(PodiumSocketEvent.TX_JOIN_PODIUM, PodiumJoinLeavePayload(podiumId, "detail", userId))
    }

    fun leavePodium(podiumId: String, userId: Int): Boolean { // TODO("Hardcoded "roomType" for testing")
        return PodiumSocketRepository.sendEvent(PodiumSocketEvent.RX_TX_LEAVE_PODIUM, PodiumJoinLeavePayload(podiumId, "detail", userId))
    }

    fun sendGift(payload: SentGiftPayload): Boolean {
        return PodiumSocketRepository.sendEvent(PodiumSocketEvent.RX_TX_PODIUM_GIFT, payload = payload)
    }

    private val _speakerFlow: MutableSharedFlow<Pair<String, PodiumSpeaker>> = MutableSharedFlow()
    val speakerFlow: SharedFlow<Pair<String, PodiumSpeaker>> = _speakerFlow

    private fun onEnterSpeakerList(data: JSONObject) = runBlocking {
        try {
            val podiumId = data.getString("podium_id")
            val result = Gson().fromJson<PodiumSpeaker>(data.toString())
            Log.w("SOCKETs", "onEnterSpeakerList: $podiumId | $result")
            _speakerFlow.emit(Pair(podiumId, result))

        } catch (e: Exception) {
            Log.e("PER", "onEnterSpeakerList: ", e)
        }
    }

    private val _exitSpeakerFlow: MutableSharedFlow<PodiumSpeakSessionEndPayload> = MutableSharedFlow()
    val exitSpeakerFlow: SharedFlow<PodiumSpeakSessionEndPayload> = _exitSpeakerFlow

    private fun onExitSpeakerList(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumSpeakSessionEndPayload>(data.toString())
            _exitSpeakerFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onExitSpeakerList: ", e)
        }
    }

    private val _speakerInviteFlow:MutableSharedFlow<PodiumSpeakerInvitePayload> = MutableSharedFlow()
    val speakerInviteFlow:SharedFlow<PodiumSpeakerInvitePayload> = _speakerInviteFlow

    private fun onSpeakerInvite(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumSpeakerInvitePayload>(data.toString())
            _speakerInviteFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onChallengeSpeakerInvite: ", e)
        }
    }

    private val _speakerInviteResponseFlow:MutableSharedFlow<PodiumSpeakerInviteResponsePayload> = MutableSharedFlow()
    val speakerInviteResponseFlow:SharedFlow<PodiumSpeakerInviteResponsePayload> = _speakerInviteResponseFlow

    private fun onSpeakerInviteResponse(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumSpeakerInviteResponsePayload>(data.toString())
            _speakerInviteResponseFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onChallengeSpeakerInviteResponse: ", e)
        }
    }

    private val _podiumGiftFlow: MutableSharedFlow<SentGiftPayload?> = MutableSharedFlow()
    val podiumGiftFlow: SharedFlow<SentGiftPayload?> = _podiumGiftFlow
    private fun onNewGift(data: JSONObject) = runBlocking {

        Log.d("giftResponse", data.toString())
        try {
            val result = Gson().fromJson<SentGiftPayload>(data.toString())
            _podiumGiftFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onExitSpeakerList: ", e)
        }
    }

    private val _waitListFlow: MutableSharedFlow<Pair<String, PodiumSpeaker>> = MutableSharedFlow()
    val waitListFlow: SharedFlow<Pair<String, PodiumSpeaker>> = _waitListFlow

    private fun onEnterWaitList(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumEnterWaitListPayload>(data.toString())
            Log.w("SOCKETs", "onEnterPodiumWaitList: ${result.podiumId} | ${result.asSpeaker()}")
            _waitListFlow.emit(Pair(result.podiumId, result.asSpeaker()))

        } catch (e: Exception) {
            Log.e("PER", "onEnterWaitList: ", e)
        }
    }

    private val _exitWaitListFlow: MutableSharedFlow<Pair<String, Int>> = MutableSharedFlow()
    val exitWaitListFlow: SharedFlow<Pair<String, Int>> = _exitWaitListFlow

    private fun onExitWaitList(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumUserIdPayload>(data.toString())
            _exitWaitListFlow.emit(Pair(result.podiumId, result.userId))
        } catch (e: Exception) {
            Log.e("PER", "onExitWaitList: ", e)
        }
    }

    fun likePodium(podiumId: String, userId: Int): Boolean {
        return PodiumSocketRepository.sendEvent(PodiumSocketEvent.RX_TX_LIKE_PODIUM, PodiumUserIdPayload(podiumId, userId))
    }

    private val _liveChatFlow: MutableSharedFlow<PodiumLiveChatPayload> = MutableSharedFlow()
    val liveChatFlow: SharedFlow<PodiumLiveChatPayload> = _liveChatFlow
    private fun onNewChatMessage(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumLiveChatPayload>(data.toString())
            _liveChatFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onNewChatMessage: ", e)
        }
    }

    private val _leaveFlow: MutableSharedFlow<Pair<String, Int>> = MutableSharedFlow()
    val leaveFlow: SharedFlow<Pair<String, Int>> = _leaveFlow
    private fun onUserLeft(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumJoinLeavePayload>(data.toString())
            _leaveFlow.emit(Pair(result.podiumId, result.userId))
        } catch (e: Exception) {
            Log.e("PER", "onUserLeft: ", e)
        }
    }

    private val _closeFlow: MutableSharedFlow<Pair<String?,PodiumClosedBy?>> = MutableSharedFlow()
    val closeFlow: SharedFlow<Pair<String?,PodiumClosedBy?>> = _closeFlow
    private fun onPodiumClosed(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumCloseEvent>(data.toString())
            result?.let { res->
                _closeFlow.emit(Pair(res.podiumId,res.closedBy))
            }

        } catch (e: Exception) {
            Log.e("PER", "onPodiumClosed: ", e)
        }
    }

    private val _blockedFlow: MutableSharedFlow<PodiumBlockPayload> = MutableSharedFlow()
    val blockedFlow: SharedFlow<PodiumBlockPayload> = _blockedFlow
    private fun onBlocked(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumBlockPayload>(data.toString())
            Log.w("PER", "onBlocked: $result")
            _blockedFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onBlocked: ", e)
        }
    }

    fun sendChatMessage(podiumChatPayload: PodiumLiveChatPayload): Boolean {
        return PodiumSocketRepository.sendEvent(PodiumSocketEvent.RX_TX_PODIUM_CHAT, podiumChatPayload)
    }

    fun deletePodiumChatMessage(podiumCommentDeletePayload: PodiumCommentDeletePayload): Boolean {
        return PodiumSocketRepository.sendEvent(PodiumSocketEvent.RX_TX_PODIUM_CHAT_DELETE, podiumCommentDeletePayload)
    }

    private val _muteUserFlow: MutableSharedFlow<PodiumMuteUserPayload> = MutableSharedFlow()
    val muteUserFlow: SharedFlow<PodiumMuteUserPayload> = _muteUserFlow
    private fun onUserMute(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumMuteUserPayload>(data.toString())
            _muteUserFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onUserMute: ", e)
        }
    }

    fun toggleUserMute(podiumId: String, userId: Int, muted: Boolean): Boolean {
        return PodiumSocketRepository.sendEvent(
            PodiumSocketEvent.RX_MUTE_PODIUM, PodiumMuteUserPayload(
                podiumId = podiumId, userId = userId, muted = muted
            )
        )
    }

    fun sendUserAttendance(podiumId: String, userId: Int, role: Podium.PodiumUserRole): Boolean {
        return PodiumSocketRepository.sendEvent(PodiumSocketEvent.TX_PODIUM_ATTENDANCE, PodiumAttendancePayload(podiumId, userId, role))
    }

    private val _syncFlow: MutableSharedFlow<PodiumSyncPayload> = MutableSharedFlow()
    val syncFlow: SharedFlow<PodiumSyncPayload> = _syncFlow

    private fun onPodiumSync(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumSyncPayload>(data.toString())
            db.withTransaction {
                val podium = db.getPodiumDao().getPodium(result.podiumId) ?: return@withTransaction
                db.getPodiumDao().update(
                    podium.copy(
                        totalLikes = result.likes,
                        totalCoins = result.totalCoins,
                        liveUsers = result.liveUsers,
                        totalUsers = result.totalUsers,
                    )
                )
            }
            _syncFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onPodiumSync: ", e)
        }
    }

    private val _freezeUserFlow: MutableSharedFlow<PodiumFreezeUserPayload> = MutableSharedFlow()
    val freezeUserFlow: SharedFlow<PodiumFreezeUserPayload> = _freezeUserFlow
    private fun onUserFreeze(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumFreezeUserPayload>(data.toString())
            _freezeUserFlow.emit(result.apply { frozen = true })
        } catch (e: Exception) {
            Log.e("PER", "onUserFreeze: ", e)
        }
    }

    private fun onUserUnfreeze(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumFreezeUserPayload>(data.toString())
            _freezeUserFlow.emit(result.apply { frozen = false })
        } catch (e: Exception) {
            Log.e("PER", "onUserUnfreeze: ", e)
        }
    }

    private val _newLikeFLow: MutableSharedFlow<PodiumUserIdPayload> = MutableSharedFlow()
    val newLikeFLow: SharedFlow<PodiumUserIdPayload> = _newLikeFLow

    private fun onPodiumLike(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumUserIdPayload>(data.toString())
//            db.withTransaction {
//                val podium = db.getPodiumDao().getPodium(result.podiumId) ?: return@withTransaction
//                db.getPodiumDao().update(
//                    podium.copy(
//                        totalLikes = podium.totalLikes + 1
//                    )
//                )
//            }
            _newLikeFLow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onPodiumLike: ", e)
        }
    }

    private val _appointAdminFLow: MutableSharedFlow<PodiumUserIdPayload> = MutableSharedFlow()
    val appointAdminFLow: SharedFlow<PodiumUserIdPayload> = _appointAdminFLow

    private fun onAppointAdmin(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumUserIdPayload>(data.toString())
            _appointAdminFLow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onAppointAdmin: ", e)
        }
    }

    private val _newAdminFLow: MutableSharedFlow<PodiumUserIdPayload> = MutableSharedFlow()
    val newAdminFLow: SharedFlow<PodiumUserIdPayload> = _newAdminFLow

    private fun onNewAdmin(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumUserIdPayload>(data.toString())
            _newAdminFLow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onNewAdmin: ", e)
        }
    }

    private val _cancelAdminRequestFLow: MutableSharedFlow<PodiumUserIdPayload> = MutableSharedFlow()
    val cancelAdminRequestFLow: SharedFlow<PodiumUserIdPayload> = _cancelAdminRequestFLow

    private fun onCancelAdminRequest(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumUserIdPayload>(data.toString())
            _cancelAdminRequestFLow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onCancelAdminRequest: ", e)
        }
    }

    private val _dismissAdminFLow: MutableSharedFlow<PodiumUserIdPayload> = MutableSharedFlow()
    val dismissAdminFLow: SharedFlow<PodiumUserIdPayload> = _dismissAdminFLow

    private fun onDismissAdmin(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumUserIdPayload>(data.toString())
            _dismissAdminFLow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onDismissAdmin: ", e)
        }
    }

    private val _adminExitFLow: MutableSharedFlow<PodiumUserIdPayload> = MutableSharedFlow()
    val adminExitFLow: SharedFlow<PodiumUserIdPayload> = _adminExitFLow

    private fun onAdminExit(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumUserIdPayload>(data.toString())
            _adminExitFLow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onDismissAdmin: ", e)
        }
    }

    private val _chatEnabledFlow: MutableSharedFlow<PodiumChatTogglePayload> = MutableSharedFlow()
    val chatEnabledFlow: SharedFlow<PodiumChatTogglePayload> = _chatEnabledFlow

    private fun onChatDisabled(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumChatTogglePayload>(data.toString())
            _chatEnabledFlow.emit(result.apply { enabled = false })
        } catch (e: Exception) {
            Log.e("PER", "onChatDisabled: ", e)
        }
    }

    private fun onChatEnabled(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumChatTogglePayload>(data.toString())
            _chatEnabledFlow.emit(result.apply { enabled = true })
        } catch (e: Exception) {
            Log.e("PER", "onChatEnabled: ", e)
        }
    }

    private val _mainScreenFLow: MutableSharedFlow<PodiumUserIdPayload> = MutableSharedFlow()
    val mainScreenFLow: SharedFlow<PodiumUserIdPayload> = _mainScreenFLow

    private fun onShownInMainScreen(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumUserIdPayload>(data.toString())
            _mainScreenFLow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onDismissAdmin: ", e)
        }
    }

    fun showInMainScreen(podiumId: String, userId: Int): Boolean {
        return PodiumSocketRepository.sendEvent(PodiumSocketEvent.RX_TX_SHOW_IN_MAIN_SCREEN, PodiumUserIdPayload(podiumId, userId))
    }


    private val _likeDisabledFlow: MutableSharedFlow<PodiumLikePayLoad> = MutableSharedFlow()
    val likeDisabledFlow: SharedFlow<PodiumLikePayLoad> = _likeDisabledFlow

    private fun onLikeDisabled(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumLikePayLoad>(data.toString())
            _likeDisabledFlow.emit(result.apply { disabled = true })
        } catch (e: Exception) {
            Log.e("PER", "onLikeDisabled: ", e)
        }
    }

    private fun onLikeEnabled(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumLikePayLoad>(data.toString())
            _likeDisabledFlow.emit(result.apply { disabled = false })
        } catch (e: Exception) {
            Log.e("PER", "onLikeEnabled: ", e)
        }
    }

    private fun onMicEnabled(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumMicPayLoad>(data.toString())
            _micEnabledFlow.emit(result.apply { disabled = false })
        } catch (e: Exception) {
            Log.e("PER", "onMicEnabled: ", e)
        }
    }

    private val _micEnabledFlow: MutableSharedFlow<PodiumMicPayLoad> = MutableSharedFlow()
    val micEnabledFlow: SharedFlow<PodiumMicPayLoad> = _micEnabledFlow

    private fun onMicDisabled(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumMicPayLoad>(data.toString())
            _micEnabledFlow.emit(result.apply { disabled = true })
        } catch (e: Exception) {
            Log.e("PER", "onMicDisabled: ", e)
        }
    }

    private val _podiumActiveFlow: MutableSharedFlow<String> = MutableSharedFlow()
    val podiumActiveFlow: SharedFlow<String> = _podiumActiveFlow

    private fun onPodiumActive(data: JSONObject) = runBlocking {
        try {
            val podiumId = data.getString("podium_id")
            _podiumActiveFlow.emit(podiumId)
        } catch (e: Exception) {
            Log.e("PER", "onPodiumActive: ", e)
        }
    }

    fun stopSpeaking(payload: PodiumAssemblyStopSpeakingPayload) : Boolean {
        return PodiumSocketRepository.sendEvent(
            PodiumSocketEvent.TX_PODIUM_ASSEMBLY_STOP_SPEAKING, payload
        )
    }

    private val _startSpeakingFlow: MutableSharedFlow<PodiumAssemblyStartSpeakingPayload> = MutableSharedFlow()
    val startSpeakingFlow: SharedFlow<PodiumAssemblyStartSpeakingPayload> = _startSpeakingFlow
    private fun onStartSpeaking(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumAssemblyStartSpeakingPayload>(data.toString())
            _startSpeakingFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onStartSpeaking: ", e)
        }
    }


    private val _extendSpeakingTimeFlow: MutableSharedFlow<PodiumAssemblyExtendSpeakingTimePayload> = MutableSharedFlow()
    val extendSpeakingTimeFlow: SharedFlow<PodiumAssemblyExtendSpeakingTimePayload> = _extendSpeakingTimeFlow
    private fun onExtendSpeakingTime(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumAssemblyExtendSpeakingTimePayload>(data.toString())
            _extendSpeakingTimeFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onStartSpeaking: ", e)
        }
    }

    private val _pauseSpeakingFlow: MutableSharedFlow<PodiumAssemblyPauseSpeakingPayload> = MutableSharedFlow()
    val pauseSpeakingFlow: SharedFlow<PodiumAssemblyPauseSpeakingPayload> = _pauseSpeakingFlow
    private fun onPauseSpeaking(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumAssemblyPauseSpeakingPayload>(data.toString())
            _pauseSpeakingFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onStartSpeaking: ", e)
        }
    }

    private val _cameraTimeExpiredSoon: MutableSharedFlow<PodiumCameraTimeExpired> = MutableSharedFlow()
    val cameraTimeExpiredSoon: SharedFlow<PodiumCameraTimeExpired> = _cameraTimeExpiredSoon

    private fun onCameraTimeExpiredSoon(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumCameraTimeExpired>(data.toString())
            _cameraTimeExpiredSoon.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onCameraExpiredSoon: ", e)
        }
    }
    private val _cameraTimeExpired: MutableSharedFlow<PodiumCameraTimeExpired> = MutableSharedFlow()
    val cameraTimeExpired: SharedFlow<PodiumCameraTimeExpired> = _cameraTimeExpired

        private fun onCameraTimeExpired(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumCameraTimeExpired>(data.toString())
            _cameraTimeExpired.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onCameraExpired: ", e)
        }
    }
    private val _podiumChatDelete: MutableSharedFlow<PodiumCommentDeletePayload> = MutableSharedFlow()
    val podiumChatDelete: SharedFlow<PodiumCommentDeletePayload> = _podiumChatDelete

    private fun onPodiumChatDelete(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumCommentDeletePayload>(data.toString())
            _podiumChatDelete.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "chatDeletePayload: ", e)
        }
    }

    private val _podiumPauseGift: MutableSharedFlow<PodiumGiftPausePayload> = MutableSharedFlow()
    val podiumPauseGift: SharedFlow<PodiumGiftPausePayload> = _podiumPauseGift

    private fun onPodiumGiftPaused(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumGiftPausePayload>(data.toString())
            _podiumPauseGift.emit(result.apply { giftPaused=true })
        } catch (e: Exception) {
            Log.e("PER", "podiumGiftPausePayload: ", e)
        }
    }

    private fun onPodiumGiftResumed(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumGiftPausePayload>(data.toString())
            _podiumPauseGift.emit(result.apply { giftPaused=false })
        } catch (e: Exception) {
            Log.e("PER", "podiumGiftResumePayload: ", e)
        }
    }

    private val _flathatAnthem: MutableSharedFlow<Podium.Anthem> = MutableSharedFlow()
    val flathatAnthem: SharedFlow<Podium.Anthem> = _flathatAnthem

    private fun onFlashatAnthem(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<Podium.Anthem>(data.toString())
            _flathatAnthem.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "podiumGiftResumePayload: ", e)
        }
    }

}