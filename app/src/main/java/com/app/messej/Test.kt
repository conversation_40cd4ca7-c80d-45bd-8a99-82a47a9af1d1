package com.app.messej

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

@Composable
fun FlixUpgradeOverviewCard() {
    Column(modifier = Modifier.padding(16.dp)) {
        Text(
            text = "Upgrade with FliX Overview",
            style = MaterialTheme.typography.titleLarge
        )
        Text(
            text = "You have upgraded with FliX to Ambassador. Your Ambassador status will expire on 22/12/2025.",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        Card(
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
            colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                OverviewRow("Status:", "Active", Color.Green)
                Divider(modifier = Modifier.padding(vertical = 8.dp))
                OverviewRow("Upgraded Level:", "Ambassador")
                Divider(modifier = Modifier.padding(vertical = 8.dp))
                OverviewRow("Expires on:", "22/12/2025")
            }
        }
    }
}

@Composable
fun OverviewRow(label: String, value: String, valueColor: Color = MaterialTheme.colorScheme.onSurface) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(text = label, style = MaterialTheme.typography.bodyLarge)
        Text(text = value, style = MaterialTheme.typography.bodyLarge, color = valueColor, fontWeight = FontWeight.Bold)
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewFlixUpgradeOverviewCard() {
    MaterialTheme { // Ensure you have a MaterialTheme defined in your project
        FlixUpgradeOverviewCard()
    }
}
